---@diagnostic disable: undefined-global
local proto_cop = Proto("cop", "COP v3_3")

-- field definitions

msg_type_header_msg_type_db78c392_4550_47fe_995b_e096d8a2fc5d =
    ProtoField.uint8(
        "cop.msg_type_header_msg_type_db78c392_4550_47fe_995b_e096d8a2fc5d",
        "msg_type", base.HEX)
msg_length_header_msg_length_43e9cc4e_0f1a_4138_abbb_ae72f0a9969f =
    ProtoField.uint16(
        "cop.msg_length_header_msg_length_43e9cc4e_0f1a_4138_abbb_ae72f0a9969f",
        "msg_length", base.HEX)
msg_number_header_msg_number_b6b9ad9c_89d1_41e8_92d5_a75f38e0e0b6 =
    ProtoField.uint16(
        "cop.msg_number_header_msg_number_b6b9ad9c_89d1_41e8_92d5_a75f38e0e0b6",
        "msg_number", base.HEX)
sender_header_sender_cd49b1ae_ce10_4ebd_b1ce_72c6ace26866 = ProtoField.uint16(
    "cop.sender_header_sender_cd49b1ae_ce10_4ebd_b1ce_72c6ace26866",
    "sender",
    base.HEX)
sim_flag_T_UINT8_454eae62_77cc_4bef_952a_dd6d6ed2b7ad = ProtoField.uint8(
    "cop.sim_flag_T_UINT8_454eae62_77cc_4bef_952a_dd6d6ed2b7ad",
    "sim_flag",
    base.HEX, {
        [0] = "Operationa_Mode",
        [1] = "Simulation_Mode"
    })
msg_sent_time_header_msg_sent_time_9efeb58c_fe7c_4227_a9aa_c9d421938332 =
    ProtoField.double(
        "cop.msg_sent_time_header_msg_sent_time_9efeb58c_fe7c_4227_a9aa_c9d421938332",
        "msg_sent_time", base.DEC)
msg_checksum_header_msg_checksum_58775c64_6950_477f_b268_45045a657997 =
    ProtoField.uint16(
        "cop.msg_checksum_header_msg_checksum_58775c64_6950_477f_b268_45045a657997",
        "msg_checksum", base.HEX)
Threat_Level_T_4_BIT_UINT8_449e2289_00b5_4064_a6d0_60d93d8da3d6 =
    ProtoField.uint8(
        "cop.Threat_Level_T_4_BIT_UINT8_449e2289_00b5_4064_a6d0_60d93d8da3d6",
        "Threat_Level", base.HEX, {
            [0] = "No_Threat",
            [1] = "One",
            [2] = "Two",
            [3] = "Three",
            [4] = "Four",
            [5] = "Five",
            [6] = "Six",
            [7] = "Seven",
            [8] = "Eight",
            [9] = "Nine",
            [10] = "Ten"
        }, 15)
Tracking_Status_T_2_BIT_UINT8_81b184a3_34e4_45cc_b121_c0fd5e8fc2dd =
    ProtoField.uint8(
        "cop.Tracking_Status_T_2_BIT_UINT8_81b184a3_34e4_45cc_b121_c0fd5e8fc2dd",
        "Tracking_Status", base.HEX,
        { [0] = "Not_tracked", [1] = "Lost", [2] = "Tracked" }, 48)
Spare_T_2_BIT_UINT8_a516bf33_8ee9_4867_adff_11ce6ecf1032 = ProtoField.uint8(
    "cop.Spare_T_2_BIT_UINT8_a516bf33_8ee9_4867_adff_11ce6ecf1032",
    "Spare",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3"
    }, 192)
Militry_emergency_T_1_BIT_UINT8_3028f864_f2b2_4657_bc21_993295971959 =
    ProtoField.uint8(
        "cop.Militry_emergency_T_1_BIT_UINT8_3028f864_f2b2_4657_bc21_993295971959",
        "Militry_emergency", base.HEX,
        { [0] = "No_Emergency", [1] = "Emergency" }, 1)
Special_Identification_T_1_BIT_UINT8_183483ea_8fce_4104_be8b_bdb410aa6561 =
    ProtoField.uint8(
        "cop.Special_Identification_T_1_BIT_UINT8_183483ea_8fce_4104_be8b_bdb410aa6561",
        "Special_Identification", base.HEX,
        { [0] = "No_Identification", [1] = "Special_Identification" }, 2)
Foe_Friend_T_2_BIT_UINT8_8bf6a277_862e_4cdd_814f_062c5dcdf206 =
    ProtoField.uint8(
        "cop.Foe_Friend_T_2_BIT_UINT8_8bf6a277_862e_4cdd_814f_062c5dcdf206",
        "Foe_Friend", base.HEX, {
            [0] = "NO_NSM_INTERROGATION",
            [1] = "FRIENDLY_TARGET",
            [2] = "UNKNOWN_TARGET",
            [3] = "NO_NSM_REPLY"
        }, 12)
Jammer_T_1_BIT_UINT8_19403d56_a38e_480f_9153_27dbc12ce9b1 = ProtoField.uint8(
    "cop.Jammer_T_1_BIT_UINT8_19403d56_a38e_480f_9153_27dbc12ce9b1",
    "Jammer",
    base.HEX, {
        [0] = "No_Jammer",
        [1] = "Jammer"
    }, 16)
Spare1_T_1_BIT_UINT8_2a82d0a2_68ca_4b34_838e_d5e989504819 = ProtoField.uint8(
    "cop.Spare1_T_1_BIT_UINT8_2a82d0a2_68ca_4b34_838e_d5e989504819",
    "Spare1",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 32)
Spare2_T_1_BIT_UINT8_907afce3_868f_4c78_91c4_0ed37e98a67f = ProtoField.uint8(
    "cop.Spare2_T_1_BIT_UINT8_907afce3_868f_4c78_91c4_0ed37e98a67f",
    "Spare2",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 64)
Spare3_T_1_BIT_UINT8_fcaf84f5_b608_44a0_8eda_49855a2f5827 = ProtoField.uint8(
    "cop.Spare3_T_1_BIT_UINT8_fcaf84f5_b608_44a0_8eda_49855a2f5827",
    "Spare3",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 128)
X_Pos_T_SINT32_eef02520_bf77_4082_bdf9_974253d764d3 = ProtoField.int32(
    "cop.X_Pos_T_SINT32_eef02520_bf77_4082_bdf9_974253d764d3",
    "X_Pos", base.DEC)
Y_Pos_T_SINT32_558dfe57_e891_41fe_9a42_d951f67c52f9 = ProtoField.int32(
    "cop.Y_Pos_T_SINT32_558dfe57_e891_41fe_9a42_d951f67c52f9",
    "Y_Pos", base.DEC)
Z_Pos_T_SINT32_e1ce30ec_b69d_4c64_b2be_e3ca40e88168 = ProtoField.int32(
    "cop.Z_Pos_T_SINT32_e1ce30ec_b69d_4c64_b2be_e3ca40e88168",
    "Z_Pos", base.DEC)
Vx_T_SINT32_c7df902a_e0fb_4565_9143_9e3c0ad37e4a = ProtoField.int32(
    "cop.Vx_T_SINT32_c7df902a_e0fb_4565_9143_9e3c0ad37e4a",
    "Vx", base.DEC)
Vy_T_SINT32_aeddb5f1_0a83_43cc_953e_4fb647aa00ec = ProtoField.int32(
    "cop.Vy_T_SINT32_aeddb5f1_0a83_43cc_953e_4fb647aa00ec",
    "Vy", base.DEC)
Vz_T_SINT32_2184a7aa_d116_4042_bd09_65fa7a91c269 = ProtoField.int32(
    "cop.Vz_T_SINT32_2184a7aa_d116_4042_bd09_65fa7a91c269",
    "Vz", base.DEC)
Track_ID_T_UINT16_7f0843c7_3985_499f_a795_4ef280d45404 = ProtoField.uint16(
    "cop.Track_ID_T_UINT16_7f0843c7_3985_499f_a795_4ef280d45404",
    "Track_ID",
    base.HEX)
FCR_track_ID_T_UINT16_dd6b7e2d_1e86_4344_8467_2f8bf3626731 = ProtoField.uint16(
    "cop.FCR_track_ID_T_UINT16_dd6b7e2d_1e86_4344_8467_2f8bf3626731",
    "FCR_track_ID",
    base.HEX)
TN_str_T_CHAR_4f880c7c_61eb_4570_baea_db185a07e591 = ProtoField.string(
    "cop.TN_str_T_CHAR_4f880c7c_61eb_4570_baea_db185a07e591",
    "TN_str", base.ASCII,
    base.HEX)
track_update_time_T_FLOAT32_29af4c5a_e7da_464a_8aba_d57930f5dac9 =
    ProtoField.float(
        "cop.track_update_time_T_FLOAT32_29af4c5a_e7da_464a_8aba_d57930f5dac9",
        "track_update_time", base.DEC)
target_type_T_UINT8_6d22b7ba_2db4_4bb3_831b_dfbc65f59122 = ProtoField.uint8(
    "cop.target_type_T_UINT8_6d22b7ba_2db4_4bb3_831b_dfbc65f59122",
    "target_type",
    base.HEX, {
        [0] = "Fighter",
        [1] = "Helicopter",
        [2] = "UAV",
        [3] = "Cruise_Missile",
        [4] = "Spare",
        [5] = "Commercial_Jet",
        [6] = "POS",
        [7] = "Propelled_POS",
        [8] = "Large_UAV",
        [100] = "ICP",
        [255] = "Unknown"
    })
target_identity_T_UINT8_a2646523_2517_4aa8_9444_324227d85fa4 = ProtoField.uint8(
    "cop.target_identity_T_UINT8_a2646523_2517_4aa8_9444_324227d85fa4",
    "target_identity",
    base.HEX, {
        [0] = "Unknown",
        [1] = "Friendly",
        [2] = "Hostile"
    })
SIF_1_Code_T_UINT8_efb0892b_3106_42ab_9c58_05ccb5bc94e1 = ProtoField.uint8(
    "cop.SIF_1_Code_T_UINT8_efb0892b_3106_42ab_9c58_05ccb5bc94e1",
    "SIF_1_Code",
    base.HEX)
SIF_2_Code_T_UINT16_56dbad66_dfce_45f8_8400_7085d17e45de = ProtoField.uint16(
    "cop.SIF_2_Code_T_UINT16_56dbad66_dfce_45f8_8400_7085d17e45de",
    "SIF_2_Code",
    base.HEX)
SIF_3_Code_T_UINT16_3e6d5d21_2cba_46c1_bc01_0aaec0aca12b = ProtoField.uint16(
    "cop.SIF_3_Code_T_UINT16_3e6d5d21_2cba_46c1_bc01_0aaec0aca12b",
    "SIF_3_Code",
    base.HEX)
Spare_T_UINT8_23007b50_dd1b_4f7e_ab8f_cff2483d17cc = ProtoField.uint8(
    "cop.Spare_T_UINT8_23007b50_dd1b_4f7e_ab8f_cff2483d17cc",
    "Spare", base.HEX)
X_T_FLOAT32_5761c97d_827e_41be_b4e7_d9de8e712905 = ProtoField.float(
    "cop.X_T_FLOAT32_5761c97d_827e_41be_b4e7_d9de8e712905",
    "X", base.DEC)
Y_T_FLOAT32_44dcc04b_cd80_4168_bb14_f96f8c050761 = ProtoField.float(
    "cop.Y_T_FLOAT32_44dcc04b_cd80_4168_bb14_f96f8c050761",
    "Y", base.DEC)
Z_T_FLOAT32_e37a1b2b_ed68_4f6d_86b2_39e02ac27d92 = ProtoField.float(
    "cop.Z_T_FLOAT32_e37a1b2b_ed68_4f6d_86b2_39e02ac27d92",
    "Z", base.DEC)
Var_XX_T_FLOAT32_4fa38e8b_3788_493a_b315_067953c4227f = ProtoField.float(
    "cop.Var_XX_T_FLOAT32_4fa38e8b_3788_493a_b315_067953c4227f",
    "Var_XX", base.DEC)
Cov_XY_T_FLOAT32_2c2cf207_126b_49a0_8de2_12ea209bbfea = ProtoField.float(
    "cop.Cov_XY_T_FLOAT32_2c2cf207_126b_49a0_8de2_12ea209bbfea",
    "Cov_XY", base.DEC)
Cov_XZ_T_FLOAT32_d5fffafa_8648_4b7d_8059_8a512ac4fc7a = ProtoField.float(
    "cop.Cov_XZ_T_FLOAT32_d5fffafa_8648_4b7d_8059_8a512ac4fc7a",
    "Cov_XZ", base.DEC)
Var_YY_T_FLOAT32_cada1991_7f52_43ea_a207_55ed98cc47a0 = ProtoField.float(
    "cop.Var_YY_T_FLOAT32_cada1991_7f52_43ea_a207_55ed98cc47a0",
    "Var_YY", base.DEC)
Cov_YZ_T_FLOAT32_32513bc2_4903_4f3a_8556_72cf2a7e8bc4 = ProtoField.float(
    "cop.Cov_YZ_T_FLOAT32_32513bc2_4903_4f3a_8556_72cf2a7e8bc4",
    "Cov_YZ", base.DEC)
Var_ZZ_T_FLOAT32_a3d2fc24_cf35_4818_b74a_d6484b41c089 = ProtoField.float(
    "cop.Var_ZZ_T_FLOAT32_a3d2fc24_cf35_4818_b74a_d6484b41c089",
    "Var_ZZ", base.DEC)
Longitude_T_FLOAT32_0ab63f31_0c48_44c4_a5be_e1befbf0158a = ProtoField.float(
    "cop.Longitude_T_FLOAT32_0ab63f31_0c48_44c4_a5be_e1befbf0158a",
    "Longitude",
    base.DEC)
Latitude_T_FLOAT32_97d88032_2388_41cb_9fc4_e4e28f850f56 = ProtoField.float(
    "cop.Latitude_T_FLOAT32_97d88032_2388_41cb_9fc4_e4e28f850f56",
    "Latitude",
    base.DEC)
Altitude_T_FLOAT32_eedeb579_e98f_4a29_b6e5_9f1379cf7333 = ProtoField.float(
    "cop.Altitude_T_FLOAT32_eedeb579_e98f_4a29_b6e5_9f1379cf7333",
    "Altitude",
    base.DEC)
Track_ID_T_UINT16_3c7114b2_8fa6_444e_94de_072fb7f8d86e = ProtoField.uint16(
    "cop.Track_ID_T_UINT16_3c7114b2_8fa6_444e_94de_072fb7f8d86e",
    "Track_ID",
    base.HEX)
target_type_T_UINT8_9e212e03_2885_4322_a6e0_3892f7b69890 = ProtoField.uint8(
    "cop.target_type_T_UINT8_9e212e03_2885_4322_a6e0_3892f7b69890",
    "target_type",
    base.HEX, {
        [0] = "Fighter",
        [1] = "Helicopter",
        [2] = "UAV",
        [3] = "Cruise_Missile",
        [4] = "Spare",
        [5] = "Commercial_Jet",
        [6] = "POS",
        [7] = "Propelled_POS",
        [8] = "Large_UAV",
        [100] = "ICP",
        [255] = "Unknown"
    })
track_update_time_T_FLOAT32_4074622e_8b0d_421e_aa3d_9eaa50306ecf =
    ProtoField.float(
        "cop.track_update_time_T_FLOAT32_4074622e_8b0d_421e_aa3d_9eaa50306ecf",
        "track_update_time", base.DEC)
Sensor_To_Target_Azimuth_T_FLOAT32_8e633cc5_05d9_4182_ae67_d6bc7a6e45f1 =
    ProtoField.float(
        "cop.Sensor_To_Target_Azimuth_T_FLOAT32_8e633cc5_05d9_4182_ae67_d6bc7a6e45f1",
        "Sensor_To_Target_Azimuth", base.DEC)
Sensor_To_Target_Elevation_T_FLOAT32_1804bee6_be13_4448_82ae_499fe26e8b5a =
    ProtoField.float(
        "cop.Sensor_To_Target_Elevation_T_FLOAT32_1804bee6_be13_4448_82ae_499fe26e8b5a",
        "Sensor_To_Target_Elevation", base.DEC)
Target_Source_Radar_type_T_UINT8_b12bbfc0_ce15_4576_ad6c_8d6a607761a5 =
    ProtoField.uint8(
        "cop.Target_Source_Radar_type_T_UINT8_b12bbfc0_ce15_4576_ad6c_8d6a607761a5",
        "Target_Source_Radar_type", base.HEX, {
            [0] = "ELM2106",
            [1] = "A_MMR",
            [2] = "B_Giraf",
            [3] = "C_Tirion3",
            [4] = "D_Tirion4",
            [5] = "E_Tirion5",
            [6] = "Toplite",
            [7] = "ManualTargetNoRadarOper",
            [8] = "ELM2138"
        })
Spare_T_UINT16_3bdb05fd_6301_4be4_9df8_3509faf4dc2a = ProtoField.uint16(
    "cop.Spare_T_UINT16_3bdb05fd_6301_4be4_9df8_3509faf4dc2a",
    "Spare", base.HEX)
Radar_Type_T_UINT8_6133cd1d_789d_4e0c_b480_c940446e9c3a = ProtoField.uint8(
    "cop.Radar_Type_T_UINT8_6133cd1d_789d_4e0c_b480_c940446e9c3a",
    "Radar_Type",
    base.HEX, {
        [0] = "MMR",
        [1] = "ATAR_FCR",
        [2] = "ATAR_SSR",
        [3] = "ELM2138"
    })
COP_Logical_ID1_T_1_BIT_UINT8_a9860ee3_64cc_485a_904e_1f93b530d807 =
    ProtoField.uint8(
        "cop.COP_Logical_ID1_T_1_BIT_UINT8_a9860ee3_64cc_485a_904e_1f93b530d807",
        "COP_Logical_ID1", base.HEX, { [0] = "0", [1] = "1" }, 1)
COP_Logical_ID2_T_1_BIT_UINT8_efef2ea0_7b7b_4732_8db4_86126e879a0b =
    ProtoField.uint8(
        "cop.COP_Logical_ID2_T_1_BIT_UINT8_efef2ea0_7b7b_4732_8db4_86126e879a0b",
        "COP_Logical_ID2", base.HEX, { [0] = "0", [1] = "1" }, 2)
COP_Logical_ID3_T_1_BIT_UINT8_cd2d0d32_39e2_481e_b939_85939f0c86fa =
    ProtoField.uint8(
        "cop.COP_Logical_ID3_T_1_BIT_UINT8_cd2d0d32_39e2_481e_b939_85939f0c86fa",
        "COP_Logical_ID3", base.HEX, { [0] = "0", [1] = "1" }, 4)
COP_Logical_ID4_T_1_BIT_UINT8_1c79cbd4_94a8_4892_8d66_cfa77149ec8e =
    ProtoField.uint8(
        "cop.COP_Logical_ID4_T_1_BIT_UINT8_1c79cbd4_94a8_4892_8d66_cfa77149ec8e",
        "COP_Logical_ID4", base.HEX, { [0] = "0", [1] = "1" }, 8)
COP_Logical_ID5_T_1_BIT_UINT8_2aa61d2a_5283_4c34_890b_87274e172466 =
    ProtoField.uint8(
        "cop.COP_Logical_ID5_T_1_BIT_UINT8_2aa61d2a_5283_4c34_890b_87274e172466",
        "COP_Logical_ID5", base.HEX, { [0] = "0", [1] = "1" }, 16)
COP_Logical_ID6_T_1_BIT_UINT8_8b35b6f8_19fe_4970_825a_0fece632987b =
    ProtoField.uint8(
        "cop.COP_Logical_ID6_T_1_BIT_UINT8_8b35b6f8_19fe_4970_825a_0fece632987b",
        "COP_Logical_ID6", base.HEX, { [0] = "0", [1] = "1" }, 32)
bSpare1_T_1_BIT_UINT8_5c33d296_9343_4f4d_9635_3137a204e306 = ProtoField.uint8(
    "cop.bSpare1_T_1_BIT_UINT8_5c33d296_9343_4f4d_9635_3137a204e306",
    "bSpare1",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 64)
bSpare2_T_1_BIT_UINT8_eb9186c3_7512_4cdc_8a41_3a8c0cce43f8 = ProtoField.uint8(
    "cop.bSpare2_T_1_BIT_UINT8_eb9186c3_7512_4cdc_8a41_3a8c0cce43f8",
    "bSpare2",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 128)
IBIT_Action_T_UINT8_f92c415d_0e2c_4dbf_a93e_61baa472e253 = ProtoField.uint8(
    "cop.IBIT_Action_T_UINT8_f92c415d_0e2c_4dbf_a93e_61baa472e253",
    "IBIT_Action",
    base.HEX, {
        [0] = "Stop",
        [1] = "Start"
    })
Link_Type_T_1_BIT_UINT8_c82e636a_dc1a_47c7_94bb_5d8f4f0eb041 = ProtoField.uint8(
    "cop.Link_Type_T_1_BIT_UINT8_c82e636a_dc1a_47c7_94bb_5d8f4f0eb041",
    "Link_Type",
    base.HEX, {
        [0] = "Wired",
        [1] = "Wireless"
    }, 1)
Locking_Policy_T_1_BIT_UINT8_7ac23c2a_cd0c_480f_9a4e_9f2050f424f3 =
    ProtoField.uint8(
        "cop.Locking_Policy_T_1_BIT_UINT8_7ac23c2a_cd0c_480f_9a4e_9f2050f424f3",
        "Locking_Policy", base.HEX, { [0] = "LOBL", [1] = "LOAL" }, 2)
Fire_Source_T_1_BIT_UINT8_e3463b4b_32f2_4a0e_ab0f_e0766ec49b3a =
    ProtoField.uint8(
        "cop.Fire_Source_T_1_BIT_UINT8_e3463b4b_32f2_4a0e_ab0f_e0766ec49b3a",
        "Fire_Source", base.HEX, { [0] = "Local", [1] = "Remote" }, 4)
Spare1_T_1_BIT_UINT8_92e1d649_7988_4ecd_ae9f_76437edc0934 = ProtoField.uint8(
    "cop.Spare1_T_1_BIT_UINT8_92e1d649_7988_4ecd_ae9f_76437edc0934",
    "Spare1",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 8)
Spare2_T_1_BIT_UINT8_610b035b_d712_4253_a3f2_011cd04be7b2 = ProtoField.uint8(
    "cop.Spare2_T_1_BIT_UINT8_610b035b_d712_4253_a3f2_011cd04be7b2",
    "Spare2",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 16)
Spare3_T_1_BIT_UINT8_e2e21eaa_db8c_469c_a903_606418b0e983 = ProtoField.uint8(
    "cop.Spare3_T_1_BIT_UINT8_e2e21eaa_db8c_469c_a903_606418b0e983",
    "Spare3",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 32)
Spare4_T_1_BIT_UINT8_3900936c_1297_4188_b6ea_c850b60ffb13 = ProtoField.uint8(
    "cop.Spare4_T_1_BIT_UINT8_3900936c_1297_4188_b6ea_c850b60ffb13",
    "Spare4",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 64)
Spare5_T_1_BIT_UINT8_31c18657_ee50_4816_9585_04bf3c316624 = ProtoField.uint8(
    "cop.Spare5_T_1_BIT_UINT8_31c18657_ee50_4816_9585_04bf3c316624",
    "Spare5",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 128)
COP_Logical_ID_T_UINT8_1af78059_9f4b_458e_867d_c63d0802db12 = ProtoField.uint8(
    "cop.COP_Logical_ID_T_UINT8_1af78059_9f4b_458e_867d_c63d0802db12",
    "COP_Logical_ID",
    base.HEX)
MCU_Serial_ID_T_UINT8_b49d07a1_dcc4_4f58_b5d0_89eb599cb446 = ProtoField.uint8(
    "cop.MCU_Serial_ID_T_UINT8_b49d07a1_dcc4_4f58_b5d0_89eb599cb446",
    "MCU_Serial_ID",
    base.HEX)
Site_Text_Length_T_UINT8_2867ba33_c1e0_4842_af8f_45dfa18d8099 =
    ProtoField.uint8(
        "cop.Site_Text_Length_T_UINT8_2867ba33_c1e0_4842_af8f_45dfa18d8099",
        "Site_Text_Length", base.HEX)
Site_Name_T_CHAR_6380a20b_f93e_4734_9fbe_97a5ae5e4338 = ProtoField.string(
    "cop.Site_Name_T_CHAR_6380a20b_f93e_4734_9fbe_97a5ae5e4338",
    "Site_Name",
    base.ASCII, base.HEX)
Area_Status_T_1_BIT_UINT8_3aa3b417_8c67_4beb_8b6f_f24d533d3002 =
    ProtoField.uint8(
        "cop.Area_Status_T_1_BIT_UINT8_3aa3b417_8c67_4beb_8b6f_f24d533d3002",
        "Area_Status", base.HEX, { [0] = "Not_Active", [1] = "Active" }, 1)
Area_Hostility_T_2_BIT_UINT8_7d393218_71ab_4061_9a22_00a75e818d66 =
    ProtoField.uint8(
        "cop.Area_Hostility_T_2_BIT_UINT8_7d393218_71ab_4061_9a22_00a75e818d66",
        "Area_Hostility", base.HEX,
        { [0] = "Friendly", [1] = "Hostile", [2] = "Neutral" }, 6)
SeverityOrWCOType_T_2_BIT_UINT8_3e74f626_80ae_4cfe_a6a9_9d29f7c585e0 =
    ProtoField.uint8(
        "cop.SeverityOrWCOType_T_2_BIT_UINT8_3e74f626_80ae_4cfe_a6a9_9d29f7c585e0",
        "SeverityOrWCOType", base.HEX,
        { [0] = "0", [1] = "1", [2] = "2", [3] = "3" }, 24)
Spare_T_3_BIT_UINT8_420452b1_0a20_435a_b9dd_b6184fe47c04 = ProtoField.uint8(
    "cop.Spare_T_3_BIT_UINT8_420452b1_0a20_435a_b9dd_b6184fe47c04",
    "Spare",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7"
    }, 224)
Area_ID_T_UINT16_d17c9257_3eb3_42d9_81bf_3c857fda9882 = ProtoField.uint16(
    "cop.Area_ID_T_UINT16_d17c9257_3eb3_42d9_81bf_3c857fda9882",
    "Area_ID", base.HEX)
Name_Length_T_UINT8_fed2df58_bb27_4d6f_94cd_3926646d177d = ProtoField.uint8(
    "cop.Name_Length_T_UINT8_fed2df58_bb27_4d6f_94cd_3926646d177d",
    "Name_Length",
    base.HEX)
Name_T_CHAR_7b925d3a_40d2_4be9_8c85_4cbe7a0b9d43 = ProtoField.string(
    "cop.Name_T_CHAR_7b925d3a_40d2_4be9_8c85_4cbe7a0b9d43",
    "Name", base.ASCII,
    base.HEX)
Object_Type_T_UINT8_d3cd4d2f_f675_4df6_b02a_b17c67b2f691 = ProtoField.uint8(
    "cop.Object_Type_T_UINT8_d3cd4d2f_f675_4df6_b02a_b17c67b2f691",
    "Object_Type",
    base.HEX, {
        [0] = "No_Statement",
        [1] = "Search",
        [2] = "Restricted",
        [3] = "Exercise",
        [4] = "Submarine_patrol",
        [5] = "Fighter_AOR",
        [6] = "Area_of_Responsibility",
        [7] = "Defended_Area",
        [8] = "Flight_Corridor",
        [9] = "Air_Defencse_Identification",
        [10] = "Danger",
        [11] = "Contaminated",
        [12] = "Allied_Engagement",
        [13] = "Hostile_Msl_Engagement",
        [14] = "Hostile_Weapon",
        [15] = "Hostile_Tactical",
        [16] = "SHORAD",
        [17] = "Kill_Zone",
        [18] = "Target_Area_Of_Interest",
        [19] = "Interest_Area",
        [20] = "Crossover_Point",
        [21] = "Air_Defense_Engagement",
        [22] = "Line_No_Statement",
        [23] = "Battle_Area_Front",
        [24] = "Gun_Target",
        [25] = "Corridor",
        [26] = "Hostile_Boundary",
        [27] = "Buffer_Zone_Boundary",
        [28] = "Low_Level_Transit_Route",
        [29] = "Tactical_Action_Line",
        [30] = "FSCL",
        [31] = "FLOT",
        [32] = "Approach_Line",
        [33] = "EW_Site",
        [34] = "Radar_Site",
        [35] = "C2_Site",
        [36] = "Launcher_Site",
        [37] = "Emergency_Point",
        [38] = "Aux_Point",
        [39] = "Rendezvous_Point",
        [40] = "Marshel_Point",
        [41] = "Tanker_Station",
        [42] = "Combat_Air_Patrol_Station",
        [43] = "Beacon_Site",
        [44] = "General_Point_Representaion"
    })
Vertices_Num_T_UINT8_ebf124d6_24d8_467c_9519_88dd38617473 = ProtoField.uint8(
    "cop.Vertices_Num_T_UINT8_ebf124d6_24d8_467c_9519_88dd38617473",
    "Vertices_Num",
    base.HEX)
SKOZ_Min_Height_T_UINT16_8036bfe1_2496_42ef_bcc9_94024d95915b =
    ProtoField.uint16(
        "cop.SKOZ_Min_Height_T_UINT16_8036bfe1_2496_42ef_bcc9_94024d95915b",
        "SKOZ_Min_Height", base.HEX)
SKOZ_Max_Height_T_UINT16_b51d428c_b6ed_4d95_92cd_c62d9ed81d96 =
    ProtoField.uint16(
        "cop.SKOZ_Max_Height_T_UINT16_b51d428c_b6ed_4d95_92cd_c62d9ed81d96",
        "SKOZ_Max_Height", base.HEX)
Start_Azimuth_T_UINT16_6421117f_3ef8_4b63_8f89_10a82ea1988d = ProtoField.uint16(
    "cop.Start_Azimuth_T_UINT16_6421117f_3ef8_4b63_8f89_10a82ea1988d",
    "Start_Azimuth",
    base.HEX)
End_Azimuth_T_UINT16_b9b2ff4b_d30f_422d_910a_f957ece0d615 = ProtoField.uint16(
    "cop.End_Azimuth_T_UINT16_b9b2ff4b_d30f_422d_910a_f957ece0d615",
    "End_Azimuth",
    base.HEX)
Radius_T_UINT16_7b78eff4_0cf2_4641_b6a9_262a34473cd1 = ProtoField.uint16(
    "cop.Radius_T_UINT16_7b78eff4_0cf2_4641_b6a9_262a34473cd1",
    "Radius", base.HEX)
Azimuth_T_UINT16_54c73401_1aca_4488_9c53_fc6e2eaee92f = ProtoField.uint16(
    "cop.Azimuth_T_UINT16_54c73401_1aca_4488_9c53_fc6e2eaee92f",
    "Azimuth", base.HEX)
Major_Radius_T_UINT16_b1d804ad_ffaa_48b5_a956_0b4374444c7f = ProtoField.uint16(
    "cop.Major_Radius_T_UINT16_b1d804ad_ffaa_48b5_a956_0b4374444c7f",
    "Major_Radius",
    base.HEX)
Minor_Radius_T_UINT16_6b70bb9b_54db_46e5_ae4f_9214ab06f624 = ProtoField.uint16(
    "cop.Minor_Radius_T_UINT16_6b70bb9b_54db_46e5_ae4f_9214ab06f624",
    "Minor_Radius",
    base.HEX)
Area_Type_T_UINT8_4a6d5f69_aea9_461d_b3b6_f98685adf04c = ProtoField.uint8(
    "cop.Area_Type_T_UINT8_4a6d5f69_aea9_461d_b3b6_f98685adf04c",
    "Area_Type",
    base.HEX, {
        [0] = "SKOZ_No_Fly",
        [1] = "SKOZ_No_Interception",
        [2] = "Deffended_Asset",
        [3] = "GSP_Polygon",
        [4] = "GSP_Line",
        [5] = "WCO_Polygon",
        [6] = "WCO_Sector",
        [7] = "WCO_Ellipse",
        [8] = "BDZ",
        [9] = "Sector",
        [10] = "Point",
        [11] = "Ellipse"
    })
Engagement_Priority_T_1_BIT_UINT8_70cdc9b2_33b5_437b_aa85_73d47946730a =
    ProtoField.uint8(
        "cop.Engagement_Priority_T_1_BIT_UINT8_70cdc9b2_33b5_437b_aa85_73d47946730a",
        "Engagement_Priority", base.HEX, { [0] = "High", [1] = "Low" }, 1)
Type_Of_ICP_T_2_BIT_UINT8_db39df11_e3e5_41ac_8058_32da1e2fa49e =
    ProtoField.uint8(
        "cop.Type_Of_ICP_T_2_BIT_UINT8_db39df11_e3e5_41ac_8058_32da1e2fa49e",
        "Type_Of_ICP", base.HEX,
        { [0] = "objP", [1] = "Derby", [2] = "ER_Derby", [3] = "LR_Derby" }, 6)
Locking_Policy_T_1_BIT_UINT8_e9d65c05_7608_43fb_a582_3a4127e4fdb4 =
    ProtoField.uint8(
        "cop.Locking_Policy_T_1_BIT_UINT8_e9d65c05_7608_43fb_a582_3a4127e4fdb4",
        "Locking_Policy", base.HEX, { [0] = "LOBL", [1] = "LOAL" }, 8)
Engagement_Request_T_2_BIT_UINT8_0354bfb4_34b3_4dcc_9591_f6ad0a7d4f09 =
    ProtoField.uint8(
        "cop.Engagement_Request_T_2_BIT_UINT8_0354bfb4_34b3_4dcc_9591_f6ad0a7d4f09",
        "Engagement_Request", base.HEX, {
            [0] = "New",
            [1] = "Update",
            [2] = "Delete_Ground_Engagement",
            [3] = "Delete_Air_Engagement"
        }, 48)
Spare1_T_1_BIT_UINT8_9af521db_87f6_45b9_84a3_87587428561b = ProtoField.uint8(
    "cop.Spare1_T_1_BIT_UINT8_9af521db_87f6_45b9_84a3_87587428561b",
    "Spare1",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 64)
Spare2_T_1_BIT_UINT8_9be6326a_ba97_46a5_8295_c7c733f142df = ProtoField.uint8(
    "cop.Spare2_T_1_BIT_UINT8_9be6326a_ba97_46a5_8295_c7c733f142df",
    "Spare2",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 128)
Plan_Status_T_2_BIT_UINT8_118c9b08_60ef_4fc1_9b5f_10cdedfdb8e8 =
    ProtoField.uint8(
        "cop.Plan_Status_T_2_BIT_UINT8_118c9b08_60ef_4fc1_9b5f_10cdedfdb8e8",
        "Plan_Status", base.HEX, {
            [0] = "Plan_Exists",
            [1] = "No_Valid_Plan",
            [2] = "Not_In_Overall_Solution",
            [3] = "Not_Calculated_Yet"
        }, 3)
Engagement_Status_T_3_BIT_UINT8_8385ada5_2dcb_4858_8564_4836980eff19 =
    ProtoField.uint8(
        "cop.Engagement_Status_T_3_BIT_UINT8_8385ada5_2dcb_4858_8564_4836980eff19",
        "Engagement_Status", base.HEX, {
            [0] = "Plan_Only",
            [1] = "Prelaunch",
            [2] = "Sent_To_MFU",
            [3] = "On_Air",
            [4] = "After_Flyby",
            [5] = "After_Abort",
            [6] = "Closed"
        }, 28)
Spare1_T_1_BIT_UINT8_c73e311a_14e3_40a4_b363_5517a8e267f4 = ProtoField.uint8(
    "cop.Spare1_T_1_BIT_UINT8_c73e311a_14e3_40a4_b363_5517a8e267f4",
    "Spare1",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 32)
Spare2_T_1_BIT_UINT8_bc2407e3_8f39_4842_a11c_c45b0fd8c3ab = ProtoField.uint8(
    "cop.Spare2_T_1_BIT_UINT8_bc2407e3_8f39_4842_a11c_c45b0fd8c3ab",
    "Spare2",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 64)
Spare3_T_1_BIT_UINT8_64d91827_f7c1_4504_a88b_61475d2c071c = ProtoField.uint8(
    "cop.Spare3_T_1_BIT_UINT8_64d91827_f7c1_4504_a88b_61475d2c071c",
    "Spare3",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 128)
Fire_Mode_T_1_BIT_UINT8_8bdf62d1_45f7_41ab_b805_5a0e1594df88 = ProtoField.uint8(
    "cop.Fire_Mode_T_1_BIT_UINT8_8bdf62d1_45f7_41ab_b805_5a0e1594df88",
    "Fire_Mode",
    base.HEX, {
        [0] = "Normal",
        [1] = "Salvo"
    }, 1)
wSpare_T_7_BIT_UINT8_d7562fed_bf78_46ff_a119_af7d01feb6d1 = ProtoField.uint8(
    "cop.wSpare_T_7_BIT_UINT8_d7562fed_bf78_46ff_a119_af7d01feb6d1",
    "wSpare",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15",
        [16] = "16",
        [17] = "17",
        [18] = "18",
        [19] = "19",
        [20] = "20",
        [21] = "21",
        [22] = "22",
        [23] = "23",
        [24] = "24",
        [25] = "25",
        [26] = "26",
        [27] = "27",
        [28] = "28",
        [29] = "29",
        [30] = "30",
        [31] = "31",
        [32] = "32",
        [33] = "33",
        [34] = "34",
        [35] = "35",
        [36] = "36",
        [37] = "37",
        [38] = "38",
        [39] = "39",
        [40] = "40",
        [41] = "41",
        [42] = "42",
        [43] = "43",
        [44] = "44",
        [45] = "45",
        [46] = "46",
        [47] = "47",
        [48] = "48",
        [49] = "49",
        [50] = "50",
        [51] = "51",
        [52] = "52",
        [53] = "53",
        [54] = "54",
        [55] = "55",
        [56] = "56",
        [57] = "57",
        [58] = "58",
        [59] = "59",
        [60] = "60",
        [61] = "61",
        [62] = "62",
        [63] = "63",
        [64] = "64",
        [65] = "65",
        [66] = "66",
        [67] = "67",
        [68] = "68",
        [69] = "69",
        [70] = "70",
        [71] = "71",
        [72] = "72",
        [73] = "73",
        [74] = "74",
        [75] = "75",
        [76] = "76",
        [77] = "77",
        [78] = "78",
        [79] = "79",
        [80] = "80",
        [81] = "81",
        [82] = "82",
        [83] = "83",
        [84] = "84",
        [85] = "85",
        [86] = "86",
        [87] = "87",
        [88] = "88",
        [89] = "89",
        [90] = "90",
        [91] = "91",
        [92] = "92",
        [93] = "93",
        [94] = "94",
        [95] = "95",
        [96] = "96",
        [97] = "97",
        [98] = "98",
        [99] = "99",
        [100] = "100",
        [101] = "101",
        [102] = "102",
        [103] = "103",
        [104] = "104",
        [105] = "105",
        [106] = "106",
        [107] = "107",
        [108] = "108",
        [109] = "109",
        [110] = "110",
        [111] = "111",
        [112] = "112",
        [113] = "113",
        [114] = "114",
        [115] = "115",
        [116] = "116",
        [117] = "117",
        [118] = "118",
        [119] = "119",
        [120] = "120",
        [121] = "121",
        [122] = "122",
        [123] = "123",
        [124] = "124",
        [125] = "125",
        [126] = "126",
        [127] = "127"
    }, 254)
X_Pos_T_SINT32_eeaf4845_fbc3_4b07_91ed_93d08d5b917a = ProtoField.int32(
    "cop.X_Pos_T_SINT32_eeaf4845_fbc3_4b07_91ed_93d08d5b917a",
    "X_Pos", base.DEC)
Y_Pos_T_SINT32_a1a02a5b_9346_4f1b_92ff_f715cd74378d = ProtoField.int32(
    "cop.Y_Pos_T_SINT32_a1a02a5b_9346_4f1b_92ff_f715cd74378d",
    "Y_Pos", base.DEC)
Z_Pos_T_SINT32_83ee88ca_2e06_4927_ba12_f36a9fae9be7 = ProtoField.int32(
    "cop.Z_Pos_T_SINT32_83ee88ca_2e06_4927_ba12_f36a9fae9be7",
    "Z_Pos", base.DEC)
Rel_Time_T_UINT32_92922097_86c3_45d3_a383_8c2e83dc1fdc = ProtoField.uint32(
    "cop.Rel_Time_T_UINT32_92922097_86c3_45d3_a383_8c2e83dc1fdc",
    "Rel_Time",
    base.HEX)
PIP_Pos_X_T_SINT32_8b8c452c_504f_4c8c_8fa1_53411700911c = ProtoField.int32(
    "cop.PIP_Pos_X_T_SINT32_8b8c452c_504f_4c8c_8fa1_53411700911c",
    "PIP_Pos_X",
    base.DEC)
PIP_Pos_Y_T_SINT32_5474ff6e_bab6_4b82_9744_22e253344294 = ProtoField.int32(
    "cop.PIP_Pos_Y_T_SINT32_5474ff6e_bab6_4b82_9744_22e253344294",
    "PIP_Pos_Y",
    base.DEC)
PIP_Pos_Z_T_SINT32_b2cbb2e6_7193_4d90_8e2d_71bf5d3c4bc3 = ProtoField.int32(
    "cop.PIP_Pos_Z_T_SINT32_b2cbb2e6_7193_4d90_8e2d_71bf5d3c4bc3",
    "PIP_Pos_Z",
    base.DEC)
MFU1_T_1_BIT_UINT8_a3c7c1a7_73ef_4f31_8bad_7490f0a4af04 = ProtoField.uint8(
    "cop.MFU1_T_1_BIT_UINT8_a3c7c1a7_73ef_4f31_8bad_7490f0a4af04",
    "MFU1", base.HEX,
    {
        [0] = "0",
        [1] = "1"
    }, 1)
MFU2_T_1_BIT_UINT8_eea01408_1f74_4e97_bf64_c13ca64cd3ea = ProtoField.uint8(
    "cop.MFU2_T_1_BIT_UINT8_eea01408_1f74_4e97_bf64_c13ca64cd3ea",
    "MFU2", base.HEX,
    {
        [0] = "0",
        [1] = "1"
    }, 2)
MFU3_T_1_BIT_UINT8_2e9836b1_bb25_4dc5_a9e9_b53ed0f3f73c = ProtoField.uint8(
    "cop.MFU3_T_1_BIT_UINT8_2e9836b1_bb25_4dc5_a9e9_b53ed0f3f73c",
    "MFU3", base.HEX,
    {
        [0] = "0",
        [1] = "1"
    }, 4)
MFU4_T_1_BIT_UINT8_62e44ae0_5e1a_4cfb_8152_7ce38218760b = ProtoField.uint8(
    "cop.MFU4_T_1_BIT_UINT8_62e44ae0_5e1a_4cfb_8152_7ce38218760b",
    "MFU4", base.HEX,
    {
        [0] = "0",
        [1] = "1"
    }, 8)
MFU5_T_1_BIT_UINT8_e01f00df_6c37_466d_9f9f_cfe46579416e = ProtoField.uint8(
    "cop.MFU5_T_1_BIT_UINT8_e01f00df_6c37_466d_9f9f_cfe46579416e",
    "MFU5", base.HEX,
    {
        [0] = "0",
        [1] = "1"
    }, 16)
MFU6_T_1_BIT_UINT8_5e5fd650_70e7_4adf_98aa_6ef06f7c3464 = ProtoField.uint8(
    "cop.MFU6_T_1_BIT_UINT8_5e5fd650_70e7_4adf_98aa_6ef06f7c3464",
    "MFU6", base.HEX,
    {
        [0] = "0",
        [1] = "1"
    }, 32)
Spare_T_2_BIT_UINT8_2d83fa0f_2ddb_42e5_ae4b_43695fb8a54f = ProtoField.uint8(
    "cop.Spare_T_2_BIT_UINT8_2d83fa0f_2ddb_42e5_ae4b_43695fb8a54f",
    "Spare",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3"
    }, 192)
q_MslID_TU8_0705e044_c6fe_49f7_ac3b_369ac51b4409 = ProtoField.uint8(
    "cop.q_MslID_TU8_0705e044_c6fe_49f7_ac3b_369ac51b4409",
    "q_MslID", base.HEX)
q_Engagement1ID_TU16_002_22b00d7c_536f_425a_ba40_c10efbc68f91 =
    ProtoField.uint16(
        "cop.q_Engagement1ID_TU16_002_22b00d7c_536f_425a_ba40_c10efbc68f91",
        "q_Engagement1ID", base.HEX)
q_RdrTime_TReal64_001_72428c46_1c27_4d1e_9e05_9ff0371b5e08 = ProtoField.double(
    "cop.q_RdrTime_TReal64_001_72428c46_1c27_4d1e_9e05_9ff0371b5e08",
    "q_RdrTime",
    base.DEC)
q_RdrHorizontalFom_TU16_bcf0ff7c_02db_4955_818e_709444ce9dc1 =
    ProtoField.uint16(
        "cop.q_RdrHorizontalFom_TU16_bcf0ff7c_02db_4955_818e_709444ce9dc1",
        "q_RdrHorizontalFom", base.HEX)
q_RdrVerticalFom_TU16_fd2499a5_1685_49fe_8732_5d1d5c118f60 = ProtoField.uint16(
    "cop.q_RdrVerticalFom_TU16_fd2499a5_1685_49fe_8732_5d1d5c118f60",
    "q_RdrVerticalFom",
    base.HEX)
q_RdrAzimuthToMsl1_TU16_85cc4d11_72be_48f1_81f9_373c4a282960 =
    ProtoField.uint16(
        "cop.q_RdrAzimuthToMsl1_TU16_85cc4d11_72be_48f1_81f9_373c4a282960",
        "q_RdrAzimuthToMsl1", base.HEX)
q_RdrElevationToMsl1_TU16_a8a03b58_160a_4d87_810d_2b29bd7e6788 =
    ProtoField.uint16(
        "cop.q_RdrElevationToMsl1_TU16_a8a03b58_160a_4d87_810d_2b29bd7e6788",
        "q_RdrElevationToMsl1", base.HEX)
q_RdrRangeToMsl1_TU16_d3c335ea_1137_4b1d_8879_b0354d41e974 = ProtoField.uint16(
    "cop.q_RdrRangeToMsl1_TU16_d3c335ea_1137_4b1d_8879_b0354d41e974",
    "q_RdrRangeToMsl1",
    base.HEX)
q_RdrMsl1Status_TU16_993d022b_54ef_4a82_8d01_34c512f0671d = ProtoField.uint16(
    "cop.q_RdrMsl1Status_TU16_993d022b_54ef_4a82_8d01_34c512f0671d",
    "q_RdrMsl1Status",
    base.HEX)
q_NumOfMslInAir_TU8_fd1f1675_6f51_467a_abc3_6e6aac7125e7 = ProtoField.uint8(
    "cop.q_NumOfMslInAir_TU8_fd1f1675_6f51_467a_abc3_6e6aac7125e7",
    "q_NumOfMslInAir",
    base.HEX)
Operability_T_2_BIT_UINT8_8f0ec644_2e0d_45e4_a569_96163fbf2440 =
    ProtoField.uint8(
        "cop.Operability_T_2_BIT_UINT8_8f0ec644_2e0d_45e4_a569_96163fbf2440",
        "Operability", base.HEX, { [0] = "OK", [1] = "Degraded", [2] = "Faulty" },
        3)
Mode_T_4_BIT_UINT8_f1a331a5_45e6_4d4c_bf85_5d5b3836533b = ProtoField.uint8(
    "cop.Mode_T_4_BIT_UINT8_f1a331a5_45e6_4d4c_bf85_5d5b3836533b",
    "Mode", base.HEX,
    {
        [0] = "Maintenance",
        [1] = "Training_Setup",
        [2] = "Training_Surveilance",
        [3] = "Training_Immediate",
        [4] = "Training_Conflict",
        [5] = "Operational_Setup",
        [6] = "Operational_Surveilance",
        [7] = "Operational_Immediate",
        [8] = "Operational_Conflict",
        [9] = "Navigation",
        [10] = "Autonomous"
    }, 60)
Type_T_1_BIT_UINT8_3ef3b326_8c14_4044_9a27_a6b7cc6ef570 = ProtoField.uint8(
    "cop.Type_T_1_BIT_UINT8_3ef3b326_8c14_4044_9a27_a6b7cc6ef570",
    "Type", base.HEX,
    {
        [0] = "SR",
        [1] = "MR"
    }, 64)
Spare_T_1_BIT_UINT8_04831496_c06d_4983_8db4_015ee2e3f73f = ProtoField.uint8(
    "cop.Spare_T_1_BIT_UINT8_04831496_c06d_4983_8db4_015ee2e3f73f",
    "Spare",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 128)
MCU_State_T_3_BIT_UINT8_5ca2eb9a_fcac_4499_af16_b1f2bcf68300 = ProtoField.uint8(
    "cop.MCU_State_T_3_BIT_UINT8_5ca2eb9a_fcac_4499_af16_b1f2bcf68300",
    "MCU_State",
    base.HEX, {
        [0] = "StandBy",
        [1] = "Operational",
        [2] = "Maintenance",
        [3] = "Training",
        [4] = "Disconnected"
    }, 7)
LM_Readiness_To_Engage_T_2_BIT_UINT8_9f19e764_62b7_411d_979c_d44c842d4779 =
    ProtoField.uint8(
        "cop.LM_Readiness_To_Engage_T_2_BIT_UINT8_9f19e764_62b7_411d_979c_d44c842d4779",
        "LM_Readiness_To_Engage", base.HEX, {
            [0] = "Ready_To_Engage",
            [1] = "Degraded_But_Ready_To_Engage",
            [2] = "Not_Ready_To_Engage"
        }, 24)
LM_Armed_T_1_BIT_UINT8_f58853bb_6101_4277_9a58_9cdb9a1e8750 = ProtoField.uint8(
    "cop.LM_Armed_T_1_BIT_UINT8_f58853bb_6101_4277_9a58_9cdb9a1e8750",
    "LM_Armed",
    base.HEX, {
        [0] = "Not_Armed",
        [1] = "Armed"
    }, 32)
MCU_Abort_Button_Pressed_T_1_BIT_UINT8_a59d2671_ad0e_4ace_b730_7e5f82739191 =
    ProtoField.uint8(
        "cop.MCU_Abort_Button_Pressed_T_1_BIT_UINT8_a59d2671_ad0e_4ace_b730_7e5f82739191",
        "MCU_Abort_Button_Pressed", base.HEX, { [0] = "No", [1] = "Yes" }, 64)
Spare2_T_1_BIT_UINT8_809f729f_5262_4eb9_a4c0_29b626b0e8e3 = ProtoField.uint8(
    "cop.Spare2_T_1_BIT_UINT8_809f729f_5262_4eb9_a4c0_29b626b0e8e3",
    "Spare2",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 128)
Not_In_Immediate_Status_T_1_BIT_UINT16_4a355c80_6097_4d53_863c_ae90f4607b1b =
    ProtoField.uint16(
        "cop.Not_In_Immediate_Status_T_1_BIT_UINT16_4a355c80_6097_4d53_863c_ae90f4607b1b",
        "Not_In_Immediate_Status", base.HEX, { [0] = "No", [1] = "Yes" }, 1)
Conflict_In_The_Fire_Source_T_1_BIT_UINT16_79ce371b_a418_48a6_9fcc_a9a5fac9c770 =
    ProtoField.uint16(
        "cop.Conflict_In_The_Fire_Source_T_1_BIT_UINT16_79ce371b_a418_48a6_9fcc_a9a5fac9c770",
        "Conflict_In_The_Fire_Source", base.HEX, { [0] = "No", [1] = "Yes" }, 2)
No_Missile_Available_T_1_BIT_UINT16_7f0dc95e_7526_4592_8104_9dc2480fcea6 =
    ProtoField.uint16(
        "cop.No_Missile_Available_T_1_BIT_UINT16_7f0dc95e_7526_4592_8104_9dc2480fcea6",
        "No_Missile_Available", base.HEX, { [0] = "No", [1] = "Yes" }, 4)
Uplink_Activated_T_1_BIT_UINT16_8df89a14_b8dc_4ba3_a122_aad21091404f =
    ProtoField.uint16(
        "cop.Uplink_Activated_T_1_BIT_UINT16_8df89a14_b8dc_4ba3_a122_aad21091404f",
        "Uplink_Activated", base.HEX, { [0] = "No", [1] = "Yes" }, 8)
Launch_Fail_T_1_BIT_UINT16_032f85a2_f2d3_4fb6_9388_f53eb00758c7 =
    ProtoField.uint16(
        "cop.Launch_Fail_T_1_BIT_UINT16_032f85a2_f2d3_4fb6_9388_f53eb00758c7",
        "Launch_Fail", base.HEX, { [0] = "No", [1] = "Yes" }, 16)
MCU_Failure_T_1_BIT_UINT16_25c4d462_f109_4023_8d65_4d64922cd1a5 =
    ProtoField.uint16(
        "cop.MCU_Failure_T_1_BIT_UINT16_25c4d462_f109_4023_8d65_4d64922cd1a5",
        "MCU_Failure", base.HEX, { [0] = "No", [1] = "Yes" }, 32)
LCU_Failure_T_1_BIT_UINT16_0a422ea4_6da8_466d_be10_5c904de3158a =
    ProtoField.uint16(
        "cop.LCU_Failure_T_1_BIT_UINT16_0a422ea4_6da8_466d_be10_5c904de3158a",
        "LCU_Failure", base.HEX, { [0] = "No", [1] = "Yes" }, 64)
MDCU_Failure_T_1_BIT_UINT16_a37b2c3f_a5d2_4087_af33_fadb4b703fa5 =
    ProtoField.uint16(
        "cop.MDCU_Failure_T_1_BIT_UINT16_a37b2c3f_a5d2_4087_af33_fadb4b703fa5",
        "MDCU_Failure", base.HEX, { [0] = "No", [1] = "Yes" }, 128)
BNET_Failure_And_LOAL_T_1_BIT_UINT16_b3ae9f56_ae99_426a_9228_374528b12b71 =
    ProtoField.uint16(
        "cop.BNET_Failure_And_LOAL_T_1_BIT_UINT16_b3ae9f56_ae99_426a_9228_374528b12b71",
        "BNET_Failure_And_LOAL", base.HEX, { [0] = "No", [1] = "Yes" }, 256)
INS_Failure_T_1_BIT_UINT16_da0e381a_f810_4ce5_a2e8_a586de26503c =
    ProtoField.uint16(
        "cop.INS_Failure_T_1_BIT_UINT16_da0e381a_f810_4ce5_a2e8_a586de26503c",
        "INS_Failure", base.HEX, { [0] = "No", [1] = "Yes" }, 512)
GPS_Failure_T_1_BIT_UINT16_c2d5e81a_c750_467d_ac6a_6d5eedde2e85 =
    ProtoField.uint16(
        "cop.GPS_Failure_T_1_BIT_UINT16_c2d5e81a_c750_467d_ac6a_6d5eedde2e85",
        "GPS_Failure", base.HEX, { [0] = "No", [1] = "Yes" }, 1024)
Time_Validity_Failure_T_1_BIT_UINT16_d071aee3_acae_42c3_990e_647eccfd4d9d =
    ProtoField.uint16(
        "cop.Time_Validity_Failure_T_1_BIT_UINT16_d071aee3_acae_42c3_990e_647eccfd4d9d",
        "Time_Validity_Failure", base.HEX, { [0] = "No", [1] = "Yes" }, 2048)
Pwr_Supply_Failure_T_1_BIT_UINT16_7730ca4b_ef02_456b_84bd_677ff32644a6 =
    ProtoField.uint16(
        "cop.Pwr_Supply_Failure_T_1_BIT_UINT16_7730ca4b_ef02_456b_84bd_677ff32644a6",
        "Pwr_Supply_Failure", base.HEX, { [0] = "No", [1] = "Yes" }, 4096)
MCU_disconnected_T_1_BIT_UINT16_453f4827_4166_442e_a2bd_9db2cdd043df =
    ProtoField.uint16(
        "cop.MCU_disconnected_T_1_BIT_UINT16_453f4827_4166_442e_a2bd_9db2cdd043df",
        "MCU_disconnected", base.HEX, { [0] = "No", [1] = "Yes" }, 8192)
Turret_In_No_Launch_Sector_T_1_BIT_UINT16_b0af6ead_e494_40c8_aa6d_07fd32abbfe8 =
    ProtoField.uint16(
        "cop.Turret_In_No_Launch_Sector_T_1_BIT_UINT16_b0af6ead_e494_40c8_aa6d_07fd32abbfe8",
        "Turret_In_No_Launch_Sector", base.HEX, { [0] = "No", [1] = "Yes" }, 16384)
Spare3_T_1_BIT_UINT16_4675fbd1_d679_44c6_aed5_fe7a4d40644d = ProtoField.uint16(
    "cop.Spare3_T_1_BIT_UINT16_4675fbd1_d679_44c6_aed5_fe7a4d40644d",
    "Spare3",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 32768)
COP_Client_Disconnected_T_1_BIT_UINT16_536efe2a_3b0a_448e_b1be_31f57d9d932a =
    ProtoField.uint16(
        "cop.COP_Client_Disconnected_T_1_BIT_UINT16_536efe2a_3b0a_448e_b1be_31f57d9d932a",
        "COP_Client_Disconnected", base.HEX, { [0] = "No", [1] = "Yes" }, 1)
COP_Server_Disconnected_T_1_BIT_UINT16_b744bef3_164d_40f7_8531_34695e678349 =
    ProtoField.uint16(
        "cop.COP_Server_Disconnected_T_1_BIT_UINT16_b744bef3_164d_40f7_8531_34695e678349",
        "COP_Server_Disconnected", base.HEX, { [0] = "No", [1] = "Yes" }, 2)
CCU_Server_Disconnected_T_1_BIT_UINT16_17ed85a9_6107_4850_a64a_93e10761964a =
    ProtoField.uint16(
        "cop.CCU_Server_Disconnected_T_1_BIT_UINT16_17ed85a9_6107_4850_a64a_93e10761964a",
        "CCU_Server_Disconnected", base.HEX, { [0] = "No", [1] = "Yes" }, 4)
COP_CCU_Desync_T_1_BIT_UINT16_2cccd484_b5dd_4aad_9942_332fab68f51d =
    ProtoField.uint16(
        "cop.COP_CCU_Desync_T_1_BIT_UINT16_2cccd484_b5dd_4aad_9942_332fab68f51d",
        "COP_CCU_Desync", base.HEX, { [0] = "No", [1] = "Yes" }, 8)
COP_MCU_Desync_T_1_BIT_UINT16_07f19728_8baa_495a_8449_b9f255e6e89e =
    ProtoField.uint16(
        "cop.COP_MCU_Desync_T_1_BIT_UINT16_07f19728_8baa_495a_8449_b9f255e6e89e",
        "COP_MCU_Desync", base.HEX, { [0] = "No", [1] = "Yes" }, 16)
DLQ_Smaller_Than_Threashold_T_1_BIT_UINT16_9b8a9c2b_87c7_49ef_9ce4_6b7952e7bf82 =
    ProtoField.uint16(
        "cop.DLQ_Smaller_Than_Threashold_T_1_BIT_UINT16_9b8a9c2b_87c7_49ef_9ce4_6b7952e7bf82",
        "DLQ_Smaller_Than_Threashold", base.HEX, { [0] = "No", [1] = "Yes" }, 32)
COP_Client_Faulty_T_1_BIT_UINT16_6aea280b_43a8_43a5_98e8_555b95f232b3 =
    ProtoField.uint16(
        "cop.COP_Client_Faulty_T_1_BIT_UINT16_6aea280b_43a8_43a5_98e8_555b95f232b3",
        "COP_Client_Faulty", base.HEX, { [0] = "No", [1] = "Yes" }, 64)
COP_Server_Faulty_T_1_BIT_UINT16_6704a723_0520_44f3_92f1_8da9b7dd47e6 =
    ProtoField.uint16(
        "cop.COP_Server_Faulty_T_1_BIT_UINT16_6704a723_0520_44f3_92f1_8da9b7dd47e6",
        "COP_Server_Faulty", base.HEX, { [0] = "No", [1] = "Yes" }, 128)
CCU_Server_Faulty_T_1_BIT_UINT16_7a3138ac_58e1_4430_8488_6aaaca0e7eef =
    ProtoField.uint16(
        "cop.CCU_Server_Faulty_T_1_BIT_UINT16_7a3138ac_58e1_4430_8488_6aaaca0e7eef",
        "CCU_Server_Faulty", base.HEX, { [0] = "No", [1] = "Yes" }, 256)
DTM_Comp_T_1_BIT_UINT16_0315f382_1898_4396_99bd_6bcefe426234 =
    ProtoField.uint16(
        "cop.DTM_Comp_T_1_BIT_UINT16_0315f382_1898_4396_99bd_6bcefe426234",
        "DTM_Comp", base.HEX, { [0] = "No", [1] = "Yes" }, 512)
SICS_Faulty_T_1_BIT_UINT16_a93b4214_4d00_461b_b9a5_872ad4c264b5 =
    ProtoField.uint16(
        "cop.SICS_Faulty_T_1_BIT_UINT16_a93b4214_4d00_461b_b9a5_872ad4c264b5",
        "SICS_Faulty", base.HEX, { [0] = "No", [1] = "Yes" }, 1024)
Toplite_Faulty_T_1_BIT_UINT16_41badab3_c512_451a_904e_7934c443dded =
    ProtoField.uint16(
        "cop.Toplite_Faulty_T_1_BIT_UINT16_41badab3_c512_451a_904e_7934c443dded",
        "Toplite_Faulty", base.HEX, { [0] = "No", [1] = "Yes" }, 2048)
Spare4_T_1_BIT_UINT16_648b2ef4_7aa8_4615_91bd_0ff05c3ee5f8 = ProtoField.uint16(
    "cop.Spare4_T_1_BIT_UINT16_648b2ef4_7aa8_4615_91bd_0ff05c3ee5f8",
    "Spare4",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 4096)
Spare5_T_1_BIT_UINT16_38ce9280_ba32_4ad8_8e3a_db38872ac141 = ProtoField.uint16(
    "cop.Spare5_T_1_BIT_UINT16_38ce9280_ba32_4ad8_8e3a_db38872ac141",
    "Spare5",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 8192)
Spare6_T_1_BIT_UINT16_9bfb9f5b_c2de_4da3_8b52_2d5f330bf17f = ProtoField.uint16(
    "cop.Spare6_T_1_BIT_UINT16_9bfb9f5b_c2de_4da3_8b52_2d5f330bf17f",
    "Spare6",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 16384)
Spare7_T_1_BIT_UINT16_ce611c65_5098_4e9e_9099_7e46c2249770 = ProtoField.uint16(
    "cop.Spare7_T_1_BIT_UINT16_ce611c65_5098_4e9e_9099_7e46c2249770",
    "Spare7",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 32768)
objP_T_4_BIT_UINT8_76a5044d_1da7_480b_9764_4ab082483c8f = ProtoField.uint8(
    "cop.objP_T_4_BIT_UINT8_76a5044d_1da7_480b_9764_4ab082483c8f",
    "objP", base.HEX,
    {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 15)
Derby_T_4_BIT_UINT8_e59be195_4a49_44db_8123_9c9498f14a72 = ProtoField.uint8(
    "cop.Derby_T_4_BIT_UINT8_e59be195_4a49_44db_8123_9c9498f14a72",
    "Derby",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 240)
ER_Derby_T_4_BIT_UINT8_d1e609d8_23d4_4287_a50c_109336f72a25 = ProtoField.uint8(
    "cop.ER_Derby_T_4_BIT_UINT8_d1e609d8_23d4_4287_a50c_109336f72a25",
    "ER_Derby",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 3840)
LR_Derby_T_4_BIT_UINT8_2a43448e_a83a_48c7_ba96_5e7215e34183 = ProtoField.uint8(
    "cop.LR_Derby_T_4_BIT_UINT8_2a43448e_a83a_48c7_ba96_5e7215e34183",
    "LR_Derby",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 61440)
objP_T_4_BIT_UINT8_1cac2eb5_774f_4b20_b932_3d9732f4bfc1 = ProtoField.uint8(
    "cop.objP_T_4_BIT_UINT8_1cac2eb5_774f_4b20_b932_3d9732f4bfc1",
    "objP", base.HEX,
    {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 15)
Derby_T_4_BIT_UINT8_38971f62_9323_43aa_92c1_42c5c3146324 = ProtoField.uint8(
    "cop.Derby_T_4_BIT_UINT8_38971f62_9323_43aa_92c1_42c5c3146324",
    "Derby",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 240)
ER_Derby_T_4_BIT_UINT8_0f7d8506_324e_498e_8ac9_cc184209c021 = ProtoField.uint8(
    "cop.ER_Derby_T_4_BIT_UINT8_0f7d8506_324e_498e_8ac9_cc184209c021",
    "ER_Derby",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 3840)
LR_Derby_T_4_BIT_UINT8_d14653ee_6a51_46c3_96bd_d818f66f1b68 = ProtoField.uint8(
    "cop.LR_Derby_T_4_BIT_UINT8_d14653ee_6a51_46c3_96bd_d818f66f1b68",
    "LR_Derby",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 61440)
Latitude_T_FLOAT32_3e4bba20_c47e_4474_8dfc_50cd33f31c8c = ProtoField.float(
    "cop.Latitude_T_FLOAT32_3e4bba20_c47e_4474_8dfc_50cd33f31c8c",
    "Latitude",
    base.DEC)
Longitude_T_FLOAT32_80b0933d_82df_45f8_8d36_6e7a3a252da4 = ProtoField.float(
    "cop.Longitude_T_FLOAT32_80b0933d_82df_45f8_8d36_6e7a3a252da4",
    "Longitude",
    base.DEC)
Altitude_T_FLOAT32_1c4df4f1_6d94_4863_88a0_31e8b8f68e07 = ProtoField.float(
    "cop.Altitude_T_FLOAT32_1c4df4f1_6d94_4863_88a0_31e8b8f68e07",
    "Altitude",
    base.DEC)
Start_Azimuth_T_UINT16_12d0a5dd_a23d_451e_ac42_da8321265c43 = ProtoField.uint16(
    "cop.Start_Azimuth_T_UINT16_12d0a5dd_a23d_451e_ac42_da8321265c43",
    "Start_Azimuth",
    base.HEX)
Sector_Size_T_UINT16_eff07ec7_621d_4c42_94b1_d51905c80094 = ProtoField.uint16(
    "cop.Sector_Size_T_UINT16_eff07ec7_621d_4c42_94b1_d51905c80094",
    "Sector_Size",
    base.HEX)
MCU_T_2_BIT_UINT32_caf15730_e070_4029_bb8d_57c29393eb31 = ProtoField.uint32(
    "cop.MCU_T_2_BIT_UINT32_caf15730_e070_4029_bb8d_57c29393eb31",
    "MCU", base.HEX,
    {
        [0] = "OK",
        [1] = "Fail"
    }, 3)
INS_T_2_BIT_UINT32_6f079d30_a488_43da_9d5c_a7c87be7305c = ProtoField.uint32(
    "cop.INS_T_2_BIT_UINT32_6f079d30_a488_43da_9d5c_a7c87be7305c",
    "INS", base.HEX,
    {
        [0] = "OK",
        [1] = "Fail"
    }, 12)
GPS_T_2_BIT_UINT32_f56bcf78_a874_4d44_9fb3_e2741c20201a = ProtoField.uint32(
    "cop.GPS_T_2_BIT_UINT32_f56bcf78_a874_4d44_9fb3_e2741c20201a",
    "GPS", base.HEX,
    {
        [0] = "OK",
        [1] = "Fail"
    }, 48)
MDCU_T_2_BIT_UINT32_56063872_6515_401a_87da_58d32fdebce7 = ProtoField.uint32(
    "cop.MDCU_T_2_BIT_UINT32_56063872_6515_401a_87da_58d32fdebce7",
    "MDCU", base.HEX,
    {
        [0] = "OK",
        [1] = "Fail"
    }, 192)
LCU1_T_2_BIT_UINT32_a96fe027_0216_440f_a2f6_22a6460b6c5d = ProtoField.uint32(
    "cop.LCU1_T_2_BIT_UINT32_a96fe027_0216_440f_a2f6_22a6460b6c5d",
    "LCU1", base.HEX,
    {
        [0] = "OK",
        [1] = "Fail"
    }, 768)
LCU2_T_2_BIT_UINT32_04b8dc04_b14d_4b4f_8df7_c1caeef39317 = ProtoField.uint32(
    "cop.LCU2_T_2_BIT_UINT32_04b8dc04_b14d_4b4f_8df7_c1caeef39317",
    "LCU2", base.HEX,
    {
        [0] = "OK",
        [1] = "Fail"
    }, 3072)
BNET_T_2_BIT_UINT32_7c9a2ef9_52ee_4a07_b073_7e948a2f5653 = ProtoField.uint32(
    "cop.BNET_T_2_BIT_UINT32_7c9a2ef9_52ee_4a07_b073_7e948a2f5653",
    "BNET", base.HEX,
    {
        [0] = "OK",
        [1] = "Fail"
    }, 12288)
Time_Validity_T_2_BIT_UINT32_d70e3537_70d0_49a2_be66_8785e7405a37 =
    ProtoField.uint32(
        "cop.Time_Validity_T_2_BIT_UINT32_d70e3537_70d0_49a2_be66_8785e7405a37",
        "Time_Validity", base.HEX, { [0] = "OK", [1] = "Fail" }, 49152)
mDRS_T_2_BIT_UINT32_ce745225_84f9_4644_bfde_1ac6b1ddff3f = ProtoField.uint32(
    "cop.mDRS_T_2_BIT_UINT32_ce745225_84f9_4644_bfde_1ac6b1ddff3f",
    "mDRS", base.HEX,
    {
        [0] = "OK",
        [1] = "Fail"
    }, 196608)
TopLite_T_2_BIT_UINT32_01b86a96_9ce6_4958_8837_83413a5b533a = ProtoField.uint32(
    "cop.TopLite_T_2_BIT_UINT32_01b86a96_9ce6_4958_8837_83413a5b533a",
    "TopLite",
    base.HEX, {
        [0] = "OK",
        [1] = "Fail"
    }, 786432)
Rubidium_T_2_BIT_UINT32_54951abc_fcb6_4f3c_875e_e958fe285cfa =
    ProtoField.uint32(
        "cop.Rubidium_T_2_BIT_UINT32_54951abc_fcb6_4f3c_875e_e958fe285cfa",
        "Rubidium", base.HEX, { [0] = "OK", [1] = "Fail" }, 3145728)
Spare3_T_2_BIT_UINT32_62c66e30_bce3_4562_8cb6_8d51da9cefcd = ProtoField.uint32(
    "cop.Spare3_T_2_BIT_UINT32_62c66e30_bce3_4562_8cb6_8d51da9cefcd",
    "Spare3",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3"
    }, 12582912)
Spare4_T_2_BIT_UINT32_d0c0793b_ebcb_4ffc_a67b_9437c97609fc = ProtoField.uint32(
    "cop.Spare4_T_2_BIT_UINT32_d0c0793b_ebcb_4ffc_a67b_9437c97609fc",
    "Spare4",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3"
    }, 50331648)
Spare5_T_2_BIT_UINT32_ecd4f668_b0de_4567_b829_b78d598d2f69 = ProtoField.uint32(
    "cop.Spare5_T_2_BIT_UINT32_ecd4f668_b0de_4567_b829_b78d598d2f69",
    "Spare5",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3"
    }, 201326592)
Spare6_T_2_BIT_UINT32_697f69d4_09f5_4a79_9153_195d92fd1098 = ProtoField.uint32(
    "cop.Spare6_T_2_BIT_UINT32_697f69d4_09f5_4a79_9153_195d92fd1098",
    "Spare6",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3"
    }, 805306368)
Spare7_T_2_BIT_UINT32_884245f3_6c4a_4c2a_9ee9_d2f7c840edc8 = ProtoField.uint32(
    "cop.Spare7_T_2_BIT_UINT32_884245f3_6c4a_4c2a_9ee9_d2f7c840edc8",
    "Spare7",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3"
    }, 3221225472)
Data_Source_T_1_BIT_UINT8_a2c4ee7a_6c45_4235_be85_ac88d09255dd =
    ProtoField.uint8(
        "cop.Data_Source_T_1_BIT_UINT8_a2c4ee7a_6c45_4235_be85_ac88d09255dd",
        "Data_Source", base.HEX, { [0] = "CBIT", [1] = "IBIT_Request" }, 1)
DLQ_Value_T_7_BIT_UINT8_c93a8896_b2c5_4c7a_b3d7_5851824ef00f = ProtoField.uint8(
    "cop.DLQ_Value_T_7_BIT_UINT8_c93a8896_b2c5_4c7a_b3d7_5851824ef00f",
    "DLQ_Value",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15",
        [16] = "16",
        [17] = "17",
        [18] = "18",
        [19] = "19",
        [20] = "20",
        [21] = "21",
        [22] = "22",
        [23] = "23",
        [24] = "24",
        [25] = "25",
        [26] = "26",
        [27] = "27",
        [28] = "28",
        [29] = "29",
        [30] = "30",
        [31] = "31",
        [32] = "32",
        [33] = "33",
        [34] = "34",
        [35] = "35",
        [36] = "36",
        [37] = "37",
        [38] = "38",
        [39] = "39",
        [40] = "40",
        [41] = "41",
        [42] = "42",
        [43] = "43",
        [44] = "44",
        [45] = "45",
        [46] = "46",
        [47] = "47",
        [48] = "48",
        [49] = "49",
        [50] = "50",
        [51] = "51",
        [52] = "52",
        [53] = "53",
        [54] = "54",
        [55] = "55",
        [56] = "56",
        [57] = "57",
        [58] = "58",
        [59] = "59",
        [60] = "60",
        [61] = "61",
        [62] = "62",
        [63] = "63",
        [64] = "64",
        [65] = "65",
        [66] = "66",
        [67] = "67",
        [68] = "68",
        [69] = "69",
        [70] = "70",
        [71] = "71",
        [72] = "72",
        [73] = "73",
        [74] = "74",
        [75] = "75",
        [76] = "76",
        [77] = "77",
        [78] = "78",
        [79] = "79",
        [80] = "80",
        [81] = "81",
        [82] = "82",
        [83] = "83",
        [84] = "84",
        [85] = "85",
        [86] = "86",
        [87] = "87",
        [88] = "88",
        [89] = "89",
        [90] = "90",
        [91] = "91",
        [92] = "92",
        [93] = "93",
        [94] = "94",
        [95] = "95",
        [96] = "96",
        [97] = "97",
        [98] = "98",
        [99] = "99",
        [100] = "100",
        [101] = "101",
        [102] = "102",
        [103] = "103",
        [104] = "104",
        [105] = "105",
        [106] = "106",
        [107] = "107",
        [108] = "108",
        [109] = "109",
        [110] = "110",
        [111] = "111",
        [112] = "112",
        [113] = "113",
        [114] = "114",
        [115] = "115",
        [116] = "116",
        [117] = "117",
        [118] = "118",
        [119] = "119",
        [120] = "120",
        [121] = "121",
        [122] = "122",
        [123] = "123",
        [124] = "124",
        [125] = "125",
        [126] = "126",
        [127] = "127"
    }, 254)
MCU_T_UINT16_0b0a8911_07cd_4666_b28f_b45fed74f1b1 = ProtoField.uint16(
    "cop.MCU_T_UINT16_0b0a8911_07cd_4666_b28f_b45fed74f1b1",
    "MCU", base.HEX)
BNET_T_UINT16_0f95abc8_7178_4f3f_ac54_c77cd1d218ab = ProtoField.uint16(
    "cop.BNET_T_UINT16_0f95abc8_7178_4f3f_ac54_c77cd1d218ab",
    "BNET", base.HEX)
INS_T_UINT16_22508071_942d_4627_b2f6_a193367373b1 = ProtoField.uint16(
    "cop.INS_T_UINT16_22508071_942d_4627_b2f6_a193367373b1",
    "INS", base.HEX)
MDCU_T_UINT16_12dbb7dc_1da9_4141_a401_a7f59875ffd2 = ProtoField.uint16(
    "cop.MDCU_T_UINT16_12dbb7dc_1da9_4141_a401_a7f59875ffd2",
    "MDCU", base.HEX)
LCU_T_UINT16_61dcdb3c_3fe2_4f0a_b99a_2e55eb01ce72 = ProtoField.uint16(
    "cop.LCU_T_UINT16_61dcdb3c_3fe2_4f0a_b99a_2e55eb01ce72",
    "LCU", base.HEX)
COP_Server_Version_T_UINT16_6d8f95d9_8274_41be_aac6_8b0119c42f53 =
    ProtoField.uint16(
        "cop.COP_Server_Version_T_UINT16_6d8f95d9_8274_41be_aac6_8b0119c42f53",
        "COP_Server_Version", base.HEX)
COP_Client_Version_T_UINT16_7ca443a5_91a7_4843_804a_eb5a41612978 =
    ProtoField.uint16(
        "cop.COP_Client_Version_T_UINT16_7ca443a5_91a7_4843_804a_eb5a41612978",
        "COP_Client_Version", base.HEX)
ICS_DTM_Version_T_UINT16_d2f4751f_a4a9_4e5a_9ba8_cdbabe86705d =
    ProtoField.uint16(
        "cop.ICS_DTM_Version_T_UINT16_d2f4751f_a4a9_4e5a_9ba8_cdbabe86705d",
        "ICS_DTM_Version", base.HEX)
MCU_Engagement_Handling_T_3_BIT_UINT8_cbfc1b00_acd3_4d65_9dab_6b9d5af6fc45 =
    ProtoField.uint8(
        "cop.MCU_Engagement_Handling_T_3_BIT_UINT8_cbfc1b00_acd3_4d65_9dab_6b9d5af6fc45",
        "MCU_Engagement_Handling", base.HEX, {
            [0] = "Will_Pro",
            [1] = "Cant_Pro",
            [2] = "Will_Comply",
            [3] = "Cant_Comply",
            [4] = "Engagement_Cannot_Be_Created"
        }, 7)
MCU_Engagement_Status_T_2_BIT_UINT8_b8036856_9bcb_480a_8e24_67c5f91e3fc9 =
    ProtoField.uint8(
        "cop.MCU_Engagement_Status_T_2_BIT_UINT8_b8036856_9bcb_480a_8e24_67c5f91e3fc9",
        "MCU_Engagement_Status", base.HEX,
        { [0] = "Closed", [1] = "Opened", [2] = "Abort_Pending" }, 24)
Spare_T_1_BIT_UINT8_af648298_7501_467f_8ffa_efcac15fb17f = ProtoField.uint8(
    "cop.Spare_T_1_BIT_UINT8_af648298_7501_467f_8ffa_efcac15fb17f",
    "Spare",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 32)
Engagement_Abort_Source_T_2_BIT_UINT8_73bf31a8_8bbe_456d_888e_1281176f8dcb =
    ProtoField.uint8(
        "cop.Engagement_Abort_Source_T_2_BIT_UINT8_73bf31a8_8bbe_456d_888e_1281176f8dcb",
        "Engagement_Abort_Source", base.HEX,
        { [0] = "No_Abort", [1] = "CM", [2] = "COP", [3] = "CP" }, 192)
ICP_Allocated_T_2_BIT_UINT8_6dde0994_689b_43fb_8371_3982be1fd4c3 =
    ProtoField.uint8(
        "cop.ICP_Allocated_T_2_BIT_UINT8_6dde0994_689b_43fb_8371_3982be1fd4c3",
        "ICP_Allocated", base.HEX,
        { [0] = "objP", [1] = "Derby", [2] = "ER_Derby", [3] = "LR_Derby" }, 3)
objP_ICP_Lock_T_1_BIT_UINT8_8585583a_1a14_4bfb_ab97_6991ab763abd =
    ProtoField.uint8(
        "cop.objP_ICP_Lock_T_1_BIT_UINT8_8585583a_1a14_4bfb_ab97_6991ab763abd",
        "objP_ICP_Lock", base.HEX, { [0] = "Not_Locked", [1] = "Locked" }, 4)
Derby_ICP_Lock_T_1_BIT_UINT8_8617f769_e03b_4dee_ae83_f4dcc83a9a28 =
    ProtoField.uint8(
        "cop.Derby_ICP_Lock_T_1_BIT_UINT8_8617f769_e03b_4dee_ae83_f4dcc83a9a28",
        "Derby_ICP_Lock", base.HEX, { [0] = "Not_Locked", [1] = "Locked" }, 8)
ER_Derby_ICP_Lock_T_1_BIT_UINT8_f6c09481_eaba_44f5_8161_b53dd93d9e3f =
    ProtoField.uint8(
        "cop.ER_Derby_ICP_Lock_T_1_BIT_UINT8_f6c09481_eaba_44f5_8161_b53dd93d9e3f",
        "ER_Derby_ICP_Lock", base.HEX, { [0] = "Not_Locked", [1] = "Locked" }, 16)
LR_Derby_ICP_Lock_T_1_BIT_UINT8_fa7d9d08_1449_494c_95ba_29fc6c229f66 =
    ProtoField.uint8(
        "cop.LR_Derby_ICP_Lock_T_1_BIT_UINT8_fa7d9d08_1449_494c_95ba_29fc6c229f66",
        "LR_Derby_ICP_Lock", base.HEX, { [0] = "Not_Locked", [1] = "Locked" }, 32)
ICP_Stage_T_1_BIT_UINT8_f5fc65a6_b3df_4f8d_b991_b7628f8ada58 = ProtoField.uint8(
    "cop.ICP_Stage_T_1_BIT_UINT8_f5fc65a6_b3df_4f8d_b991_b7628f8ada58",
    "ICP_Stage",
    base.HEX, {
        [0] = "On_Ground",
        [1] = "On_Air"
    }, 64)
Lock_Type_T_1_BIT_UINT8_bbf19f87_340b_45af_845e_953a8528d084 = ProtoField.uint8(
    "cop.Lock_Type_T_1_BIT_UINT8_bbf19f87_340b_45af_845e_953a8528d084",
    "Lock_Type",
    base.HEX, {
        [0] = "LOBL",
        [1] = "LOAL"
    }, 128)
Misfire_Indication_T_1_BIT_UINT8_3c6348db_5929_4c23_953d_982b441258c9 =
    ProtoField.uint8(
        "cop.Misfire_Indication_T_1_BIT_UINT8_3c6348db_5929_4c23_953d_982b441258c9",
        "Misfire_Indication", base.HEX, { [0] = "No", [1] = "Yes" }, 256)
Misfire_Reason_T_2_BIT_UINT8_fecf3e6b_a781_45a5_a121_a26893a6f5ad =
    ProtoField.uint8(
        "cop.Misfire_Reason_T_2_BIT_UINT8_fecf3e6b_a781_45a5_a121_a26893a6f5ad",
        "Misfire_Reason", base.HEX, {
            [0] = "Empty",
            [1] = "Launch_Failed",
            [2] = "Misfire",
            [3] = "No_Fire"
        }, 1536)
spare_T_5_BIT_UINT8_81879ce0_69f8_4726_9683_cc364c591aee = ProtoField.uint8(
    "cop.spare_T_5_BIT_UINT8_81879ce0_69f8_4726_9683_cc364c591aee",
    "spare",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15",
        [16] = "16",
        [17] = "17",
        [18] = "18",
        [19] = "19",
        [20] = "20",
        [21] = "21",
        [22] = "22",
        [23] = "23",
        [24] = "24",
        [25] = "25",
        [26] = "26",
        [27] = "27",
        [28] = "28",
        [29] = "29",
        [30] = "30",
        [31] = "31"
    }, 63488)
Seeker_Mode_T_1_BIT_UINT8_58d5e937_3acf_48ae_924a_f7835b9d481a =
    ProtoField.uint8(
        "cop.Seeker_Mode_T_1_BIT_UINT8_58d5e937_3acf_48ae_924a_f7835b9d481a",
        "Seeker_Mode", base.HEX, { [0] = "Free", [1] = "Follows_Designation" }, 1)
SPare1_T_1_BIT_UINT8_ea828b75_d6c2_442c_9d35_c9ba15bd0a9c = ProtoField.uint8(
    "cop.SPare1_T_1_BIT_UINT8_ea828b75_d6c2_442c_9d35_c9ba15bd0a9c",
    "SPare1",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 2)
Spare2_T_1_BIT_UINT8_7698d824_3711_455d_9aab_4a02dad53d2d = ProtoField.uint8(
    "cop.Spare2_T_1_BIT_UINT8_7698d824_3711_455d_9aab_4a02dad53d2d",
    "Spare2",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 4)
Spare3_T_1_BIT_UINT8_7ee8cecf_e007_4860_ad08_c4a8d216b23a = ProtoField.uint8(
    "cop.Spare3_T_1_BIT_UINT8_7ee8cecf_e007_4860_ad08_c4a8d216b23a",
    "Spare3",
    base.HEX, {
        [0] = "0",
        [1] = "1"
    }, 8)
Spare4_T_2_BIT_UINT8_05454b7a_52da_4704_83c1_93338d49629c = ProtoField.uint8(
    "cop.Spare4_T_2_BIT_UINT8_05454b7a_52da_4704_83c1_93338d49629c",
    "Spare4",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3"
    }, 48)
Spare5_T_2_BIT_UINT8_a43ee928_d428_4381_a6c1_7d1ec39e598f = ProtoField.uint8(
    "cop.Spare5_T_2_BIT_UINT8_a43ee928_d428_4381_a6c1_7d1ec39e598f",
    "Spare5",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3"
    }, 192)
Track_ID_T_UINT16_fcd5d2c6_cdaa_49c1_ada9_6e0f48ea1c9b = ProtoField.uint16(
    "cop.Track_ID_T_UINT16_fcd5d2c6_cdaa_49c1_ada9_6e0f48ea1c9b",
    "Track_ID",
    base.HEX)
CCU_Engagement_ID_T_UINT16_08829c96_1c3a_4f5f_b1e6_8273eeb2090a =
    ProtoField.uint16(
        "cop.CCU_Engagement_ID_T_UINT16_08829c96_1c3a_4f5f_b1e6_8273eeb2090a",
        "CCU_Engagement_ID", base.HEX)
COP_Engagement_ID_T_UINT16_508c88cf_4f0c_489b_86dd_465bd4f232eb =
    ProtoField.uint16(
        "cop.COP_Engagement_ID_T_UINT16_508c88cf_4f0c_489b_86dd_465bd4f232eb",
        "COP_Engagement_ID", base.HEX)
Engagement_Launch_Time_T_UINT32_fd534a87_ae89_49e3_b23f_6680c6acb774 =
    ProtoField.uint32(
        "cop.Engagement_Launch_Time_T_UINT32_fd534a87_ae89_49e3_b23f_6680c6acb774",
        "Engagement_Launch_Time", base.HEX)
Turret_Launching_Azimuth_T_UINT16_ac186391_2e73_4de7_83e8_985cc84c6251 =
    ProtoField.uint16(
        "cop.Turret_Launching_Azimuth_T_UINT16_ac186391_2e73_4de7_83e8_985cc84c6251",
        "Turret_Launching_Azimuth", base.HEX)
q_Spare2Bits_TU16_2_001_da2afb7c_83af_4205_a505_ab5f9bc3c3e8 =
    ProtoField.uint16(
        "cop.q_Spare2Bits_TU16_2_001_da2afb7c_83af_4205_a505_ab5f9bc3c3e8",
        "q_Spare2Bits", base.HEX, { [0] = "0", [1] = "1", [2] = "2", [3] = "3" },
        3)
q_Abort_TU16_2_001_ff638045_4260_4e1a_97f3_b01bfa34eeea = ProtoField.uint16(
    "cop.q_Abort_TU16_2_001_ff638045_4260_4e1a_97f3_b01bfa34eeea",
    "q_Abort",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3"
    }, 12)
q_PfStatus_TU16_4_001_2de6e39b_c651_4a7b_bc33_4c1f71cf2296 = ProtoField.uint16(
    "cop.q_PfStatus_TU16_4_001_2de6e39b_c651_4a7b_bc33_4c1f71cf2296",
    "q_PfStatus",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 240)
q_Pointingstatus_TU16_4_001_edfd44dc_7c13_4e2d_a2be_38ab77231d58 =
    ProtoField.uint16(
        "cop.q_Pointingstatus_TU16_4_001_edfd44dc_7c13_4e2d_a2be_38ab77231d58",
        "q_Pointingstatus", base.HEX, {
            [0] = "0",
            [1] = "1",
            [2] = "2",
            [3] = "3",
            [4] = "4",
            [5] = "5",
            [6] = "6",
            [7] = "7",
            [8] = "8",
            [9] = "9",
            [10] = "10",
            [11] = "11",
            [12] = "12",
            [13] = "13",
            [14] = "14",
            [15] = "15"
        }, 3840)
q_PropulsionStage_TU16_4_001_5c16d5d7_c7fc_484d_af75_9455530fd1b1 =
    ProtoField.uint16(
        "cop.q_PropulsionStage_TU16_4_001_5c16d5d7_c7fc_484d_af75_9455530fd1b1",
        "q_PropulsionStage", base.HEX, {
            [0] = "0",
            [1] = "1",
            [2] = "2",
            [3] = "3",
            [4] = "4",
            [5] = "5",
            [6] = "6",
            [7] = "7",
            [8] = "8",
            [9] = "9",
            [10] = "10",
            [11] = "11",
            [12] = "12",
            [13] = "13",
            [14] = "14",
            [15] = "15"
        }, 61440)
q_UpLinkStatus_TU8_4_001_7f6df6bc_73ce_41d6_8e15_0e6e360e105f =
    ProtoField.uint8(
        "cop.q_UpLinkStatus_TU8_4_001_7f6df6bc_73ce_41d6_8e15_0e6e360e105f",
        "q_UpLinkStatus", base.HEX, {
            [0] = "0",
            [1] = "1",
            [2] = "2",
            [3] = "3",
            [4] = "4",
            [5] = "5",
            [6] = "6",
            [7] = "7",
            [8] = "8",
            [9] = "9",
            [10] = "10",
            [11] = "11",
            [12] = "12",
            [13] = "13",
            [14] = "14",
            [15] = "15"
        }, 15)
q_SeekerState_TU8_4_001_ef6da7bc_0f67_43ff_927e_0ba66a00c86c = ProtoField.uint8(
    "cop.q_SeekerState_TU8_4_001_ef6da7bc_0f67_43ff_927e_0ba66a00c86c",
    "q_SeekerState",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 240)
q_EcmStatus_TU8_4_001_517dd728_5a87_4146_914a_1ee45917cda2 = ProtoField.uint8(
    "cop.q_EcmStatus_TU8_4_001_517dd728_5a87_4146_914a_1ee45917cda2",
    "q_EcmStatus",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 15)
q_Spare_TU8_4_001_b817ca5f_e633_43b3_9b8b_806a4b610ac3 = ProtoField.uint8(
    "cop.q_Spare_TU8_4_001_b817ca5f_e633_43b3_9b8b_806a4b610ac3",
    "q_Spare",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 240)
q_EsadStatus_TU8_4_001_35c474a3_b55e_4870_8cb4_0e1d487435a8 = ProtoField.uint8(
    "cop.q_EsadStatus_TU8_4_001_35c474a3_b55e_4870_8cb4_0e1d487435a8",
    "q_EsadStatus",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 15)
q_EIsadStatus_TU8_4_001_47e06d9e_9b4b_4fbf_84a6_7113bba1c63c = ProtoField.uint8(
    "cop.q_EIsadStatus_TU8_4_001_47e06d9e_9b4b_4fbf_84a6_7113bba1c63c",
    "q_EIsadStatus",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15"
    }, 240)
q_MslID_T_UINT8_292715fb_4b48_4e51_9d40_ed026488aa91 = ProtoField.uint8(
    "cop.q_MslID_T_UINT8_292715fb_4b48_4e51_9d40_ed026488aa91",
    "q_MslID", base.HEX)
q_NavTimeTag_TU16_002_c0097ec7_7600_4a85_a878_87fa333d8af0 = ProtoField.uint16(
    "cop.q_NavTimeTag_TU16_002_c0097ec7_7600_4a85_a878_87fa333d8af0",
    "q_NavTimeTag",
    base.HEX)
q_MslXL0_TS16_001_44f181dc_7682_4eb1_87ad_c0000b3d3ba1 = ProtoField.int16(
    "cop.q_MslXL0_TS16_001_44f181dc_7682_4eb1_87ad_c0000b3d3ba1",
    "q_MslXL0",
    base.DEC)
q_MslYL0_TS16_001_19262d37_4a60_430f_ad6b_dcdcbeccc6fa = ProtoField.int16(
    "cop.q_MslYL0_TS16_001_19262d37_4a60_430f_ad6b_dcdcbeccc6fa",
    "q_MslYL0",
    base.DEC)
q_MslZL0_TS16_001_97f7d429_ea86_4d81_ad14_5775df755beb = ProtoField.int16(
    "cop.q_MslZL0_TS16_001_97f7d429_ea86_4d81_ad14_5775df755beb",
    "q_MslZL0",
    base.DEC)
q_MslVxL0_TS16_001_b3213180_81f3_4095_96d3_cc96b7af9cf8 = ProtoField.int16(
    "cop.q_MslVxL0_TS16_001_b3213180_81f3_4095_96d3_cc96b7af9cf8",
    "q_MslVxL0",
    base.DEC)
q_MslVyL0_TS16_001_0836e383_381d_44d4_8499_1c19fd0664d9 = ProtoField.int16(
    "cop.q_MslVyL0_TS16_001_0836e383_381d_44d4_8499_1c19fd0664d9",
    "q_MslVyL0",
    base.DEC)
q_MslVzL0_TS16_001_a3ffcc3a_999d_4f13_b4f1_2ff3861704a5 = ProtoField.int16(
    "cop.q_MslVzL0_TS16_001_a3ffcc3a_999d_4f13_b4f1_2ff3861704a5",
    "q_MslVzL0",
    base.DEC)
q_EulerAlpha_TS16_001_60d39747_bea2_4054_9b7c_d08a16b5bace = ProtoField.int16(
    "cop.q_EulerAlpha_TS16_001_60d39747_bea2_4054_9b7c_d08a16b5bace",
    "q_EulerAlpha",
    base.DEC)
q_EulerBeta_TS16_001_d1a94e4c_e0ff_4e9c_b18d_e3cd56dc1bf8 = ProtoField.int16(
    "cop.q_EulerBeta_TS16_001_d1a94e4c_e0ff_4e9c_b18d_e3cd56dc1bf8",
    "q_EulerBeta",
    base.DEC)
q_EulerGama_TS16_001_768e354c_b9c0_4e71_8c3e_95d27f6a0203 = ProtoField.int16(
    "cop.q_EulerGama_TS16_001_768e354c_b9c0_4e71_8c3e_95d27f6a0203",
    "q_EulerGama",
    base.DEC)
MslNavPosUncertainty_TU8_cb49c6a7_afef_499d_91c9_2ae9a08ff0a5 =
    ProtoField.uint8(
        "cop.MslNavPosUncertainty_TU8_cb49c6a7_afef_499d_91c9_2ae9a08ff0a5",
        "MslNavPosUncertainty", base.HEX)
MslNavVelUncertainty_TU8_b1e54f26_1053_4aa7_bf23_97a41e657a4a =
    ProtoField.uint8(
        "cop.MslNavVelUncertainty_TU8_b1e54f26_1053_4aa7_bf23_97a41e657a4a",
        "MslNavVelUncertainty", base.HEX)
q_RangeMslTarget_TS16_a32fa25a_baa6_4910_8950_64a155cf8104 = ProtoField.int16(
    "cop.q_RangeMslTarget_TS16_a32fa25a_baa6_4910_8950_64a155cf8104",
    "q_RangeMslTarget",
    base.DEC)
q_Tgo_TU8_5ef3d2cb_48d8_4f05_b33a_8a8262464b05 = ProtoField.uint8(
    "cop.q_Tgo_TU8_5ef3d2cb_48d8_4f05_b33a_8a8262464b05",
    "q_Tgo", base.HEX)
q_TtoActivateSeeker_TU8_1c8d6b99_17ac_4096_97f4_5082f44ae623 = ProtoField.uint8(
    "cop.q_TtoActivateSeeker_TU8_1c8d6b99_17ac_4096_97f4_5082f44ae623",
    "q_TtoActivateSeeker",
    base.HEX)
q_GuidanceMode_TU8_b4178bc2_23aa_406b_acd8_4605d2066163 = ProtoField.uint8(
    "cop.q_GuidanceMode_TU8_b4178bc2_23aa_406b_acd8_4605d2066163",
    "q_GuidanceMode",
    base.HEX)
q_GimbalTheta_TU8_2d8ce243_aeb4_420f_a203_8b3739fb9005 = ProtoField.uint8(
    "cop.q_GimbalTheta_TU8_2d8ce243_aeb4_420f_a203_8b3739fb9005",
    "q_GimbalTheta",
    base.HEX)
q_GimbalPsi_TU8_5cf3fd22_2264_4ff8_a911_9f97b81ce4d8 = ProtoField.uint8(
    "cop.q_GimbalPsi_TU8_5cf3fd22_2264_4ff8_a911_9f97b81ce4d8",
    "q_GimbalPsi",
    base.HEX)
q_Range_TU8_0b84bbf2_4703_462e_9e18_543f1b74644f = ProtoField.uint8(
    "cop.q_Range_TU8_0b84bbf2_4703_462e_9e18_543f1b74644f",
    "q_Range", base.HEX)
q_RDOT_TU8_d6097dd2_9f94_4efe_88da_041b9cd6053e = ProtoField.uint8(
    "cop.q_RDOT_TU8_d6097dd2_9f94_4efe_88da_041b9cd6053e",
    "q_RDOT", base.HEX)
q_ImuStatus_TU8_6700d067_ce31_45bc_b4af_7c4c866a0c6f = ProtoField.uint8(
    "cop.q_ImuStatus_TU8_6700d067_ce31_45bc_b4af_7c4c866a0c6f",
    "q_ImuStatus",
    base.HEX)
q_SeekerStatus_TU8_6fa54310_e49c_4365_956e_c182a6f76472 = ProtoField.uint8(
    "cop.q_SeekerStatus_TU8_6fa54310_e49c_4365_956e_c182a6f76472",
    "q_SeekerStatus",
    base.HEX)
q_VesselPres_TU8_41828f4f_c33a_454a_861a_27b68a56047a = ProtoField.uint8(
    "cop.q_VesselPres_TU8_41828f4f_c33a_454a_861a_27b68a56047a",
    "q_VesselPres",
    base.HEX)
q_MslData1_TU32_184f33df_fc78_472b_bf6f_27145bae347c = ProtoField.uint32(
    "cop.q_MslData1_TU32_184f33df_fc78_472b_bf6f_27145bae347c",
    "q_MslData1",
    base.HEX)
q_MslData2_TU32_8ad05827_c87f_4df2_8f3a_1947255cd2d9 = ProtoField.uint32(
    "cop.q_MslData2_TU32_8ad05827_c87f_4df2_8f3a_1947255cd2d9",
    "q_MslData2",
    base.HEX)
q_MslData3_TU32_3dc239bb_ebbf_4faa_bcc0_1489ae9887db = ProtoField.uint32(
    "cop.q_MslData3_TU32_3dc239bb_ebbf_4faa_bcc0_1489ae9887db",
    "q_MslData3",
    base.HEX)
q_MslData4_TU32_67a32579_a6cd_4271_ae43_db6bbacaf44b = ProtoField.uint32(
    "cop.q_MslData4_TU32_67a32579_a6cd_4271_ae43_db6bbacaf44b",
    "q_MslData4",
    base.HEX)
q_MslData5_TU32_b1a40e00_61e1_490b_8d08_a7662c5f6c39 = ProtoField.uint32(
    "cop.q_MslData5_TU32_b1a40e00_61e1_490b_8d08_a7662c5f6c39",
    "q_MslData5",
    base.HEX)
q_NumOfMslInAir_TU8_adfe5860_f7f4_4798_b916_8dd2d853d30a = ProtoField.uint8(
    "cop.q_NumOfMslInAir_TU8_adfe5860_f7f4_4798_b916_8dd2d853d30a",
    "q_NumOfMslInAir",
    base.HEX)
TargetUpdateTime_TU32_6b1820c3_626a_4184_a20e_623e6851610c = ProtoField.uint32(
    "cop.TargetUpdateTime_TU32_6b1820c3_626a_4184_a20e_623e6851610c",
    "TargetUpdateTime",
    base.HEX)
TargetPositionNorth_T_SINT32_670a12e9_99f4_4d8c_9505_52b752e6f231 =
    ProtoField.int32(
        "cop.TargetPositionNorth_T_SINT32_670a12e9_99f4_4d8c_9505_52b752e6f231",
        "TargetPositionNorth", base.DEC)
TargetPositionEast_T_SINT32_201eda6c_dadf_4511_a154_7953127a1c8a =
    ProtoField.int32(
        "cop.TargetPositionEast_T_SINT32_201eda6c_dadf_4511_a154_7953127a1c8a",
        "TargetPositionEast", base.DEC)
TargetPositionDown_T_SINT32_1ad667e5_2036_4602_9be0_cdb33b577630 =
    ProtoField.int32(
        "cop.TargetPositionDown_T_SINT32_1ad667e5_2036_4602_9be0_cdb33b577630",
        "TargetPositionDown", base.DEC)
TargetVelocityNorth_T_SINT32_81fc3cba_4ea9_4278_82c0_e66f669c386c =
    ProtoField.int32(
        "cop.TargetVelocityNorth_T_SINT32_81fc3cba_4ea9_4278_82c0_e66f669c386c",
        "TargetVelocityNorth", base.DEC)
TargetVelocityEast_T_SINT32_244abd48_fb4f_438f_bfc3_f9cad677cff5 =
    ProtoField.int32(
        "cop.TargetVelocityEast_T_SINT32_244abd48_fb4f_438f_bfc3_f9cad677cff5",
        "TargetVelocityEast", base.DEC)
TargetVelocityDown_T_SINT32_0cbec0b7_45d6_460f_b844_456379d6a46b =
    ProtoField.int32(
        "cop.TargetVelocityDown_T_SINT32_0cbec0b7_45d6_460f_b844_456379d6a46b",
        "TargetVelocityDown", base.DEC)
AutonomousTargetID_T_UINT16_283c3389_5761_4e3a_a3d5_d8adf731dc8d =
    ProtoField.uint16(
        "cop.AutonomousTargetID_T_UINT16_283c3389_5761_4e3a_a3d5_d8adf731dc8d",
        "AutonomousTargetID", base.HEX)
AutonomousEngagementID_T_UINT16_3e1f17fa_4e08_488e_b678_328c5f2e1144 =
    ProtoField.uint16(
        "cop.AutonomousEngagementID_T_UINT16_3e1f17fa_4e08_488e_b678_328c5f2e1144",
        "AutonomousEngagementID", base.HEX)
TopliteTargetType_TU8_35f3e5ce_8052_4e06_872a_0e1533d20d06 = ProtoField.uint8(
    "cop.TopliteTargetType_TU8_35f3e5ce_8052_4e06_872a_0e1533d20d06",
    "TopliteTargetType",
    base.HEX, {
        [0] = "Plane",
        [1] = "Helicopter",
        [2] = "UAV",
        [3] = "Cruise_Missile",
        [4] = "NA",
        [5] = "Commercial_Jet",
        [6] = "PSO",
        [7] = "Propelled_PSO",
        [8] = "Large_UAV",
        [100] = "ICP",
        [255] = "Invalid_Type"
    })
Validity_T_1_BIT_UINT8_a73aa0b0_8e80_4725_a0f2_f9d7ed5ba7f6 = ProtoField.uint8(
    "cop.Validity_T_1_BIT_UINT8_a73aa0b0_8e80_4725_a0f2_f9d7ed5ba7f6",
    "Validity",
    base.HEX, {
        [0] = "No",
        [1] = "Yes"
    }, 256)
spare_T_7_BIT_UINT8_acdd4034_48b5_4ce6_b505_d2f8c9a3c181 = ProtoField.uint8(
    "cop.spare_T_7_BIT_UINT8_acdd4034_48b5_4ce6_b505_d2f8c9a3c181",
    "spare",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15",
        [16] = "16",
        [17] = "17",
        [18] = "18",
        [19] = "19",
        [20] = "20",
        [21] = "21",
        [22] = "22",
        [23] = "23",
        [24] = "24",
        [25] = "25",
        [26] = "26",
        [27] = "27",
        [28] = "28",
        [29] = "29",
        [30] = "30",
        [31] = "31",
        [32] = "32",
        [33] = "33",
        [34] = "34",
        [35] = "35",
        [36] = "36",
        [37] = "37",
        [38] = "38",
        [39] = "39",
        [40] = "40",
        [41] = "41",
        [42] = "42",
        [43] = "43",
        [44] = "44",
        [45] = "45",
        [46] = "46",
        [47] = "47",
        [48] = "48",
        [49] = "49",
        [50] = "50",
        [51] = "51",
        [52] = "52",
        [53] = "53",
        [54] = "54",
        [55] = "55",
        [56] = "56",
        [57] = "57",
        [58] = "58",
        [59] = "59",
        [60] = "60",
        [61] = "61",
        [62] = "62",
        [63] = "63",
        [64] = "64",
        [65] = "65",
        [66] = "66",
        [67] = "67",
        [68] = "68",
        [69] = "69",
        [70] = "70",
        [71] = "71",
        [72] = "72",
        [73] = "73",
        [74] = "74",
        [75] = "75",
        [76] = "76",
        [77] = "77",
        [78] = "78",
        [79] = "79",
        [80] = "80",
        [81] = "81",
        [82] = "82",
        [83] = "83",
        [84] = "84",
        [85] = "85",
        [86] = "86",
        [87] = "87",
        [88] = "88",
        [89] = "89",
        [90] = "90",
        [91] = "91",
        [92] = "92",
        [93] = "93",
        [94] = "94",
        [95] = "95",
        [96] = "96",
        [97] = "97",
        [98] = "98",
        [99] = "99",
        [100] = "100",
        [101] = "101",
        [102] = "102",
        [103] = "103",
        [104] = "104",
        [105] = "105",
        [106] = "106",
        [107] = "107",
        [108] = "108",
        [109] = "109",
        [110] = "110",
        [111] = "111",
        [112] = "112",
        [113] = "113",
        [114] = "114",
        [115] = "115",
        [116] = "116",
        [117] = "117",
        [118] = "118",
        [119] = "119",
        [120] = "120",
        [121] = "121",
        [122] = "122",
        [123] = "123",
        [124] = "124",
        [125] = "125",
        [126] = "126",
        [127] = "127"
    }, 65024)
live_tracks_T_UINT8_64847538_5bf5_472c_a938_58717e94957f = ProtoField.uint8(
    "cop.live_tracks_T_UINT8_64847538_5bf5_472c_a938_58717e94957f",
    "live_tracks",
    base.HEX, {
        [17] = "False",
        [85] = "True"
    })
number_of_tracks_T_UINT8_48201a7b_7261_45d0_89be_28ba2ed080e2 =
    ProtoField.uint8(
        "cop.number_of_tracks_T_UINT8_48201a7b_7261_45d0_89be_28ba2ed080e2",
        "number_of_tracks", base.HEX)
Spare_T_UINT16_ee8724aa_8840_4d5f_8bac_ed413ca99577 = ProtoField.uint16(
    "cop.Spare_T_UINT16_ee8724aa_8840_4d5f_8bac_ed413ca99577",
    "Spare", base.HEX)
Live_Tracks_T_UINT8_490932e5_c337_4781_b38e_24d82ba222a9 = ProtoField.uint8(
    "cop.Live_Tracks_T_UINT8_490932e5_c337_4781_b38e_24d82ba222a9",
    "Live_Tracks",
    base.HEX, {
        [17] = "False",
        [85] = "True"
    })
C2_Mode_T_UINT8_f52f8bad_1cfa_48aa_bc19_ba49e708462f = ProtoField.uint8(
    "cop.C2_Mode_T_UINT8_f52f8bad_1cfa_48aa_bc19_ba49e708462f",
    "C2_Mode", base.HEX,
    {
        [0] = "Not_Relevent",
        [1] = "Operational",
        [2] = "Simulation"
    })
C2_Substate_T_UINT8_bdebfd51_853b_4460_b7f8_05c477188755 = ProtoField.uint8(
    "cop.C2_Substate_T_UINT8_bdebfd51_853b_4460_b7f8_05c477188755",
    "C2_Substate",
    base.HEX, {
        [0] = "Unknown",
        [1] = "Setup",
        [2] = "Surveillance",
        [3] = "Immediate"
    })
C2_Safety_State_T_UINT8_6e7e5779_4e59_4f89_b4b2_12f1e4c46c40 = ProtoField.uint8(
    "cop.C2_Safety_State_T_UINT8_6e7e5779_4e59_4f89_b4b2_12f1e4c46c40",
    "C2_Safety_State",
    base.HEX, {
        [0] = "Unknown",
        [1] = "Disarmed",
        [2] = "Armed"
    })
C2_Fire_Readiness_Status_T_UINT8_d2c24998_784e_4f0a_83af_88416eefa57c =
    ProtoField.uint8(
        "cop.C2_Fire_Readiness_Status_T_UINT8_d2c24998_784e_4f0a_83af_88416eefa57c",
        "C2_Fire_Readiness_Status", base.HEX,
        { [0] = "Unknown", [1] = "Ready", [2] = "Not_Ready" })
C2_Status_T_UINT8_44e4c68f_3f7a_441b_a982_626bdc2e1198 = ProtoField.uint8(
    "cop.C2_Status_T_UINT8_44e4c68f_3f7a_441b_a982_626bdc2e1198",
    "C2_Status",
    base.HEX, {
        [0] = "Disconnected",
        [1] = "OK",
        [2] = "Degraded",
        [3] = "Faulty"
    })
Num_Of_Conneted_Sensors_T_UINT8_adfe01e9_51f5_4873_84c8_c0e103011244 =
    ProtoField.uint8(
        "cop.Num_Of_Conneted_Sensors_T_UINT8_adfe01e9_51f5_4873_84c8_c0e103011244",
        "Num_Of_Conneted_Sensors", base.HEX)
Spare_T_UINT16_cdfbff38_0dde_4848_a81c_f975bd7860c2 = ProtoField.uint16(
    "cop.Spare_T_UINT16_cdfbff38_0dde_4848_a81c_f975bd7860c2",
    "Spare", base.HEX)
Num_Of_Connected_MFUs_T_UINT8_f645320b_f749_4e9b_afb6_62d481b86f36 =
    ProtoField.uint8(
        "cop.Num_Of_Connected_MFUs_T_UINT8_f645320b_f749_4e9b_afb6_62d481b86f36",
        "Num_Of_Connected_MFUs", base.HEX)
Sequence_num_T_UINT32_313a04c1_e7ab_486a_a0ba_6ee276ad08f2 = ProtoField.uint32(
    "cop.Sequence_num_T_UINT32_313a04c1_e7ab_486a_a0ba_6ee276ad08f2",
    "Sequence_num",
    base.HEX)
Num_Of_GSPs_T_UINT8_c2db8f8a_ae73_4127_8f40_a8a30801c057 = ProtoField.uint8(
    "cop.Num_Of_GSPs_T_UINT8_c2db8f8a_ae73_4127_8f40_a8a30801c057",
    "Num_Of_GSPs",
    base.HEX)
Last_In_Cycle_T_UINT8_97c5e6fd_2ed3_4dc1_a294_f6ff5ea19993 = ProtoField.uint8(
    "cop.Last_In_Cycle_T_UINT8_97c5e6fd_2ed3_4dc1_a294_f6ff5ea19993",
    "Last_In_Cycle",
    base.HEX, {
        [0] = "No",
        [1] = "Yes"
    })
Spare_T_UINT16_a96ccb41_39be_412e_801f_5d3df80e700f = ProtoField.uint16(
    "cop.Spare_T_UINT16_a96ccb41_39be_412e_801f_5d3df80e700f",
    "Spare", base.HEX)
Track_ID_T_UINT16_11fe0ff6_32da_474e_8c24_1edd8184f028 = ProtoField.uint16(
    "cop.Track_ID_T_UINT16_11fe0ff6_32da_474e_8c24_1edd8184f028",
    "Track_ID",
    base.HEX)
CCU_Engagement_ID_T_UINT16_6caa7cf7_6ede_4349_a3fb_87d34db5d494 =
    ProtoField.uint16(
        "cop.CCU_Engagement_ID_T_UINT16_6caa7cf7_6ede_4349_a3fb_87d34db5d494",
        "CCU_Engagement_ID", base.HEX)
Freq_Allocation_T_UINT8_f04bbe34_39fa_431e_9b21_17f28fa66e9f = ProtoField.uint8(
    "cop.Freq_Allocation_T_UINT8_f04bbe34_39fa_431e_9b21_17f28fa66e9f",
    "Freq_Allocation",
    base.HEX)
Channel_Allocation_Index_T_UINT8_a9b7e2b6_69e6_41bd_9fea_8a56a2f9dcd0 =
    ProtoField.uint8(
        "cop.Channel_Allocation_Index_T_UINT8_a9b7e2b6_69e6_41bd_9fea_8a56a2f9dcd0",
        "Channel_Allocation_Index", base.HEX)
Fire_Control2_T_UINT8_5633dd03_258c_4f9b_b7ca_9e5f33d1cde6 = ProtoField.uint8(
    "cop.Fire_Control2_T_UINT8_5633dd03_258c_4f9b_b7ca_9e5f33d1cde6",
    "Fire_Control2",
    base.HEX, {
        [0] = "Hold",
        [1] = "Ready",
        [2] = "Enable"
    })
Abort_Engagement_T_UINT8_84d75e19_ba48_475e_96d0_b4abbc38b854 =
    ProtoField.uint8(
        "cop.Abort_Engagement_T_UINT8_84d75e19_ba48_475e_96d0_b4abbc38b854",
        "Abort_Engagement", base.HEX, { [17] = "False", [85] = "True" })
CCU_Time_T_UINT32_dec03fdc_c194_41fe_9db6_90bfff3d2909 = ProtoField.uint32(
    "cop.CCU_Time_T_UINT32_dec03fdc_c194_41fe_9db6_90bfff3d2909",
    "CCU_Time",
    base.HEX)
Magic_Word_T_UINT32_231f0105_50f9_41d5_90d9_bbdaa4d70368 = ProtoField.uint32(
    "cop.Magic_Word_T_UINT32_231f0105_50f9_41d5_90d9_bbdaa4d70368",
    "Magic_Word",
    base.HEX)
Spare_T_UINT16_82e6aa77_9e6c_4698_8f75_a9e4873c9c7b = ProtoField.uint16(
    "cop.Spare_T_UINT16_82e6aa77_9e6c_4698_8f75_a9e4873c9c7b",
    "Spare", base.HEX)
ICP_Plan_Exists_T_UINT8_f2f01884_1420_4c74_9f06_9aad8c72b10c = ProtoField.uint8(
    "cop.ICP_Plan_Exists_T_UINT8_f2f01884_1420_4c74_9f06_9aad8c72b10c",
    "ICP_Plan_Exists",
    base.HEX, {
        [17] = "False",
        [85] = "True"
    })
Number_Of_Points_In_Traj_T_UINT8_e9e9754f_9268_4137_9ee8_e3a7aa061295 =
    ProtoField.uint8(
        "cop.Number_Of_Points_In_Traj_T_UINT8_e9e9754f_9268_4137_9ee8_e3a7aa061295",
        "Number_Of_Points_In_Traj", base.HEX)
Text_Length_T_UINT8_37113846_cb79_4a6b_966d_5a61be5ab191 = ProtoField.uint8(
    "cop.Text_Length_T_UINT8_37113846_cb79_4a6b_966d_5a61be5ab191",
    "Text_Length",
    base.HEX)
Chat_Text_T_CHAR_e2f96c40_0fc6_495b_b53b_ee71057010e3 = ProtoField.string(
    "cop.Chat_Text_T_CHAR_e2f96c40_0fc6_495b_b53b_ee71057010e3",
    "Chat_Text",
    base.ASCII, base.HEX)
Text_Length_T_UINT8_62609524_b521_4e80_8f65_a080d4a9adc3 = ProtoField.uint8(
    "cop.Text_Length_T_UINT8_62609524_b521_4e80_8f65_a080d4a9adc3",
    "Text_Length",
    base.HEX)
Text_T_CHAR_fe4888b2_ba47_4345_a916_757fa6e4c6d6 = ProtoField.string(
    "cop.Text_T_CHAR_fe4888b2_ba47_4345_a916_757fa6e4c6d6",
    "Text", base.ASCII,
    base.HEX)
Latitude_T_FLOAT32_5c9d5f03_dfc2_41a8_9107_a84149332eab = ProtoField.float(
    "cop.Latitude_T_FLOAT32_5c9d5f03_dfc2_41a8_9107_a84149332eab",
    "Latitude",
    base.DEC)
Longitude_T_FLOAT32_280114d1_733c_42fe_ad3e_decf3e4ad1bd = ProtoField.float(
    "cop.Longitude_T_FLOAT32_280114d1_733c_42fe_ad3e_decf3e4ad1bd",
    "Longitude",
    base.DEC)
MCU_Serial_ID_T_UINT8_7a3dee28_cc01_4963_b72a_7637cf690cb2 = ProtoField.uint8(
    "cop.MCU_Serial_ID_T_UINT8_7a3dee28_cc01_4963_b72a_7637cf690cb2",
    "MCU_Serial_ID",
    base.HEX)
Network_Size_T_UINT8_175879c1_f8f3_41a0_9a9c_403bad1070f5 = ProtoField.uint8(
    "cop.Network_Size_T_UINT8_175879c1_f8f3_41a0_9a9c_403bad1070f5",
    "Network_Size",
    base.HEX)
Turret_Azimuth_T_UINT16_dbdcaed5_c24a_4e7d_a4f1_8493835fc680 =
    ProtoField.uint16(
        "cop.Turret_Azimuth_T_UINT16_dbdcaed5_c24a_4e7d_a4f1_8493835fc680",
        "Turret_Azimuth", base.HEX)
Cabin_Azimuth_T_UINT32_7419cc7b_46dd_408b_a11c_355f388e28df = ProtoField.uint32(
    "cop.Cabin_Azimuth_T_UINT32_7419cc7b_46dd_408b_a11c_355f388e28df",
    "Cabin_Azimuth",
    base.HEX)
Number_Of_Sectors_T_UINT8_0b00ea4b_76bb_4c69_8296_21054c081d6b =
    ProtoField.uint8(
        "cop.Number_Of_Sectors_T_UINT8_0b00ea4b_76bb_4c69_8296_21054c081d6b",
        "Number_Of_Sectors", base.HEX)
Chat_Text_Length_T_UINT8_75275dbe_6d13_4310_bec4_1b9d920c61f0 =
    ProtoField.uint8(
        "cop.Chat_Text_Length_T_UINT8_75275dbe_6d13_4310_bec4_1b9d920c61f0",
        "Chat_Text_Length", base.HEX)
Chat_Text_T_CHAR_55b71ec7_3c64_46eb_806a_ca122f537aa6 = ProtoField.string(
    "cop.Chat_Text_T_CHAR_55b71ec7_3c64_46eb_806a_ca122f537aa6",
    "Chat_Text",
    base.ASCII, base.HEX)
Toplite_Azimuth_T_UINT16_3b8b8563_e5f2_42f7_85a0_819b6543dad5 =
    ProtoField.uint16(
        "cop.Toplite_Azimuth_T_UINT16_3b8b8563_e5f2_42f7_85a0_819b6543dad5",
        "Toplite_Azimuth", base.HEX)
Toplite_Elevation_T_UINT16_44083aad_4cd5_4b4c_8644_50cd1edec391 =
    ProtoField.uint16(
        "cop.Toplite_Elevation_T_UINT16_44083aad_4cd5_4b4c_8644_50cd1edec391",
        "Toplite_Elevation", base.HEX)
Toplite_Target_Range_T_UINT32_be358cf7_ba6e_4d7f_bf59_fa79ec904abe =
    ProtoField.uint32(
        "cop.Toplite_Target_Range_T_UINT32_be358cf7_ba6e_4d7f_bf59_fa79ec904abe",
        "Toplite_Target_Range", base.HEX)
COPTopliteEngagementIndication_T_2_BIT_UINT8_5f91e315_02d4_47f9_91cf_60df46fdff7d =
    ProtoField.uint8(
        "cop.COPTopliteEngagementIndication_T_2_BIT_UINT8_5f91e315_02d4_47f9_91cf_60df46fdff7d",
        "COPTopliteEngagementIndication", base.HEX, {
            [0] = "NoEngagement",
            [1] = "GroundEngagement",
            [2] = "AirEngagement"
        }, 196608)
Spare_T_6_BIT_UINT8_b962dcd7_18d9_45f9_8caa_7a3f89ae7b1c = ProtoField.uint8(
    "cop.Spare_T_6_BIT_UINT8_b962dcd7_18d9_45f9_8caa_7a3f89ae7b1c",
    "Spare",
    base.HEX, {
        [0] = "0",
        [1] = "1",
        [2] = "2",
        [3] = "3",
        [4] = "4",
        [5] = "5",
        [6] = "6",
        [7] = "7",
        [8] = "8",
        [9] = "9",
        [10] = "10",
        [11] = "11",
        [12] = "12",
        [13] = "13",
        [14] = "14",
        [15] = "15",
        [16] = "16",
        [17] = "17",
        [18] = "18",
        [19] = "19",
        [20] = "20",
        [21] = "21",
        [22] = "22",
        [23] = "23",
        [24] = "24",
        [25] = "25",
        [26] = "26",
        [27] = "27",
        [28] = "28",
        [29] = "29",
        [30] = "30",
        [31] = "31",
        [32] = "32",
        [33] = "33",
        [34] = "34",
        [35] = "35",
        [36] = "36",
        [37] = "37",
        [38] = "38",
        [39] = "39",
        [40] = "40",
        [41] = "41",
        [42] = "42",
        [43] = "43",
        [44] = "44",
        [45] = "45",
        [46] = "46",
        [47] = "47",
        [48] = "48",
        [49] = "49",
        [50] = "50",
        [51] = "51",
        [52] = "52",
        [53] = "53",
        [54] = "54",
        [55] = "55",
        [56] = "56",
        [57] = "57",
        [58] = "58",
        [59] = "59",
        [60] = "60",
        [61] = "61",
        [62] = "62",
        [63] = "63"
    }, 16515072)
Number_Of_Engagements_T_UINT8_4f8de101_74bd_48c6_a33f_c34afa14bf83 =
    ProtoField.uint8(
        "cop.Number_Of_Engagements_T_UINT8_4f8de101_74bd_48c6_a33f_c34afa14bf83",
        "Number_Of_Engagements", base.HEX)
Spare2_T_UINT8_2375aa06_09c2_4d70_b8e8_98452c5b67ea = ProtoField.uint8(
    "cop.Spare2_T_UINT8_2375aa06_09c2_4d70_b8e8_98452c5b67ea",
    "Spare2", base.HEX)
Sequence_num_T_UINT32_ea2981e9_13e4_49a0_991f_c32e2949b949 = ProtoField.uint32(
    "cop.Sequence_num_T_UINT32_ea2981e9_13e4_49a0_991f_c32e2949b949",
    "Sequence_num",
    base.HEX)
Text_Length_T_UINT8_0e6a3891_91cb_4063_82c1_36518c5f33a2 = ProtoField.uint8(
    "cop.Text_Length_T_UINT8_0e6a3891_91cb_4063_82c1_36518c5f33a2",
    "Text_Length",
    base.HEX)
Text_T_CHAR_c3386610_a74f_4209_aecd_b6233480b08b = ProtoField.string(
    "cop.Text_T_CHAR_c3386610_a74f_4209_aecd_b6233480b08b",
    "Text", base.ASCII,
    base.HEX)
Latitude_T_FLOAT32_ba3a03d1_69b4_467c_8fb0_4209562c34ab = ProtoField.float(
    "cop.Latitude_T_FLOAT32_ba3a03d1_69b4_467c_8fb0_4209562c34ab",
    "Latitude",
    base.DEC)
Longitude_T_FLOAT32_7a23e00b_e3d7_440b_8ad8_41447742edfe = ProtoField.float(
    "cop.Longitude_T_FLOAT32_7a23e00b_e3d7_440b_8ad8_41447742edfe",
    "Longitude",
    base.DEC)
q_MslID_TU8_af7d7c03_e9cb_4565_a933_f63e05203bdc = ProtoField.uint8(
    "cop.q_MslID_TU8_af7d7c03_e9cb_4565_a933_f63e05203bdc",
    "q_MslID", base.HEX)
q_MissleDataBuffer_TU8_fcde7e09_8e65_4a68_896a_75619553c864 = ProtoField.uint8(
    "cop.q_MissleDataBuffer_TU8_fcde7e09_8e65_4a68_896a_75619553c864",
    "q_MissleDataBuffer",
    base.HEX)

proto_cop.fields = {
    msg_type_header_msg_type_db78c392_4550_47fe_995b_e096d8a2fc5d,
    msg_length_header_msg_length_43e9cc4e_0f1a_4138_abbb_ae72f0a9969f,
    msg_number_header_msg_number_b6b9ad9c_89d1_41e8_92d5_a75f38e0e0b6,
    sender_header_sender_cd49b1ae_ce10_4ebd_b1ce_72c6ace26866,
    sim_flag_T_UINT8_454eae62_77cc_4bef_952a_dd6d6ed2b7ad,
    msg_sent_time_header_msg_sent_time_9efeb58c_fe7c_4227_a9aa_c9d421938332,
    msg_checksum_header_msg_checksum_58775c64_6950_477f_b268_45045a657997,
    Threat_Level_T_4_BIT_UINT8_449e2289_00b5_4064_a6d0_60d93d8da3d6,
    Tracking_Status_T_2_BIT_UINT8_81b184a3_34e4_45cc_b121_c0fd5e8fc2dd,
    Spare_T_2_BIT_UINT8_a516bf33_8ee9_4867_adff_11ce6ecf1032,
    Militry_emergency_T_1_BIT_UINT8_3028f864_f2b2_4657_bc21_993295971959,
    Special_Identification_T_1_BIT_UINT8_183483ea_8fce_4104_be8b_bdb410aa6561,
    Foe_Friend_T_2_BIT_UINT8_8bf6a277_862e_4cdd_814f_062c5dcdf206,
    Jammer_T_1_BIT_UINT8_19403d56_a38e_480f_9153_27dbc12ce9b1,
    Spare1_T_1_BIT_UINT8_2a82d0a2_68ca_4b34_838e_d5e989504819,
    Spare2_T_1_BIT_UINT8_907afce3_868f_4c78_91c4_0ed37e98a67f,
    Spare3_T_1_BIT_UINT8_fcaf84f5_b608_44a0_8eda_49855a2f5827,
    X_Pos_T_SINT32_eef02520_bf77_4082_bdf9_974253d764d3,
    Y_Pos_T_SINT32_558dfe57_e891_41fe_9a42_d951f67c52f9,
    Z_Pos_T_SINT32_e1ce30ec_b69d_4c64_b2be_e3ca40e88168,
    Vx_T_SINT32_c7df902a_e0fb_4565_9143_9e3c0ad37e4a,
    Vy_T_SINT32_aeddb5f1_0a83_43cc_953e_4fb647aa00ec,
    Vz_T_SINT32_2184a7aa_d116_4042_bd09_65fa7a91c269,
    Track_ID_T_UINT16_7f0843c7_3985_499f_a795_4ef280d45404,
    FCR_track_ID_T_UINT16_dd6b7e2d_1e86_4344_8467_2f8bf3626731,
    TN_str_T_CHAR_4f880c7c_61eb_4570_baea_db185a07e591,
    track_update_time_T_FLOAT32_29af4c5a_e7da_464a_8aba_d57930f5dac9,
    target_type_T_UINT8_6d22b7ba_2db4_4bb3_831b_dfbc65f59122,
    target_identity_T_UINT8_a2646523_2517_4aa8_9444_324227d85fa4,
    SIF_1_Code_T_UINT8_efb0892b_3106_42ab_9c58_05ccb5bc94e1,
    SIF_2_Code_T_UINT16_56dbad66_dfce_45f8_8400_7085d17e45de,
    SIF_3_Code_T_UINT16_3e6d5d21_2cba_46c1_bc01_0aaec0aca12b,
    Spare_T_UINT8_23007b50_dd1b_4f7e_ab8f_cff2483d17cc,
    X_T_FLOAT32_5761c97d_827e_41be_b4e7_d9de8e712905,
    Y_T_FLOAT32_44dcc04b_cd80_4168_bb14_f96f8c050761,
    Z_T_FLOAT32_e37a1b2b_ed68_4f6d_86b2_39e02ac27d92,
    Var_XX_T_FLOAT32_4fa38e8b_3788_493a_b315_067953c4227f,
    Cov_XY_T_FLOAT32_2c2cf207_126b_49a0_8de2_12ea209bbfea,
    Cov_XZ_T_FLOAT32_d5fffafa_8648_4b7d_8059_8a512ac4fc7a,
    Var_YY_T_FLOAT32_cada1991_7f52_43ea_a207_55ed98cc47a0,
    Cov_YZ_T_FLOAT32_32513bc2_4903_4f3a_8556_72cf2a7e8bc4,
    Var_ZZ_T_FLOAT32_a3d2fc24_cf35_4818_b74a_d6484b41c089,
    Longitude_T_FLOAT32_0ab63f31_0c48_44c4_a5be_e1befbf0158a,
    Latitude_T_FLOAT32_97d88032_2388_41cb_9fc4_e4e28f850f56,
    Altitude_T_FLOAT32_eedeb579_e98f_4a29_b6e5_9f1379cf7333,
    Track_ID_T_UINT16_3c7114b2_8fa6_444e_94de_072fb7f8d86e,
    target_type_T_UINT8_9e212e03_2885_4322_a6e0_3892f7b69890,
    track_update_time_T_FLOAT32_4074622e_8b0d_421e_aa3d_9eaa50306ecf,
    Sensor_To_Target_Azimuth_T_FLOAT32_8e633cc5_05d9_4182_ae67_d6bc7a6e45f1,
    Sensor_To_Target_Elevation_T_FLOAT32_1804bee6_be13_4448_82ae_499fe26e8b5a,
    Target_Source_Radar_type_T_UINT8_b12bbfc0_ce15_4576_ad6c_8d6a607761a5,
    Spare_T_UINT16_3bdb05fd_6301_4be4_9df8_3509faf4dc2a,
    Radar_Type_T_UINT8_6133cd1d_789d_4e0c_b480_c940446e9c3a,
    COP_Logical_ID1_T_1_BIT_UINT8_a9860ee3_64cc_485a_904e_1f93b530d807,
    COP_Logical_ID2_T_1_BIT_UINT8_efef2ea0_7b7b_4732_8db4_86126e879a0b,
    COP_Logical_ID3_T_1_BIT_UINT8_cd2d0d32_39e2_481e_b939_85939f0c86fa,
    COP_Logical_ID4_T_1_BIT_UINT8_1c79cbd4_94a8_4892_8d66_cfa77149ec8e,
    COP_Logical_ID5_T_1_BIT_UINT8_2aa61d2a_5283_4c34_890b_87274e172466,
    COP_Logical_ID6_T_1_BIT_UINT8_8b35b6f8_19fe_4970_825a_0fece632987b,
    bSpare1_T_1_BIT_UINT8_5c33d296_9343_4f4d_9635_3137a204e306,
    bSpare2_T_1_BIT_UINT8_eb9186c3_7512_4cdc_8a41_3a8c0cce43f8,
    IBIT_Action_T_UINT8_f92c415d_0e2c_4dbf_a93e_61baa472e253,
    Link_Type_T_1_BIT_UINT8_c82e636a_dc1a_47c7_94bb_5d8f4f0eb041,
    Locking_Policy_T_1_BIT_UINT8_7ac23c2a_cd0c_480f_9a4e_9f2050f424f3,
    Fire_Source_T_1_BIT_UINT8_e3463b4b_32f2_4a0e_ab0f_e0766ec49b3a,
    Spare1_T_1_BIT_UINT8_92e1d649_7988_4ecd_ae9f_76437edc0934,
    Spare2_T_1_BIT_UINT8_610b035b_d712_4253_a3f2_011cd04be7b2,
    Spare3_T_1_BIT_UINT8_e2e21eaa_db8c_469c_a903_606418b0e983,
    Spare4_T_1_BIT_UINT8_3900936c_1297_4188_b6ea_c850b60ffb13,
    Spare5_T_1_BIT_UINT8_31c18657_ee50_4816_9585_04bf3c316624,
    COP_Logical_ID_T_UINT8_1af78059_9f4b_458e_867d_c63d0802db12,
    MCU_Serial_ID_T_UINT8_b49d07a1_dcc4_4f58_b5d0_89eb599cb446,
    Site_Text_Length_T_UINT8_2867ba33_c1e0_4842_af8f_45dfa18d8099,
    Site_Name_T_CHAR_6380a20b_f93e_4734_9fbe_97a5ae5e4338,
    Area_Status_T_1_BIT_UINT8_3aa3b417_8c67_4beb_8b6f_f24d533d3002,
    Area_Hostility_T_2_BIT_UINT8_7d393218_71ab_4061_9a22_00a75e818d66,
    SeverityOrWCOType_T_2_BIT_UINT8_3e74f626_80ae_4cfe_a6a9_9d29f7c585e0,
    Spare_T_3_BIT_UINT8_420452b1_0a20_435a_b9dd_b6184fe47c04,
    Area_ID_T_UINT16_d17c9257_3eb3_42d9_81bf_3c857fda9882,
    Name_Length_T_UINT8_fed2df58_bb27_4d6f_94cd_3926646d177d,
    Name_T_CHAR_7b925d3a_40d2_4be9_8c85_4cbe7a0b9d43,
    Object_Type_T_UINT8_d3cd4d2f_f675_4df6_b02a_b17c67b2f691,
    Vertices_Num_T_UINT8_ebf124d6_24d8_467c_9519_88dd38617473,
    SKOZ_Min_Height_T_UINT16_8036bfe1_2496_42ef_bcc9_94024d95915b,
    SKOZ_Max_Height_T_UINT16_b51d428c_b6ed_4d95_92cd_c62d9ed81d96,
    Start_Azimuth_T_UINT16_6421117f_3ef8_4b63_8f89_10a82ea1988d,
    End_Azimuth_T_UINT16_b9b2ff4b_d30f_422d_910a_f957ece0d615,
    Radius_T_UINT16_7b78eff4_0cf2_4641_b6a9_262a34473cd1,
    Azimuth_T_UINT16_54c73401_1aca_4488_9c53_fc6e2eaee92f,
    Major_Radius_T_UINT16_b1d804ad_ffaa_48b5_a956_0b4374444c7f,
    Minor_Radius_T_UINT16_6b70bb9b_54db_46e5_ae4f_9214ab06f624,
    Area_Type_T_UINT8_4a6d5f69_aea9_461d_b3b6_f98685adf04c,
    Engagement_Priority_T_1_BIT_UINT8_70cdc9b2_33b5_437b_aa85_73d47946730a,
    Type_Of_ICP_T_2_BIT_UINT8_db39df11_e3e5_41ac_8058_32da1e2fa49e,
    Locking_Policy_T_1_BIT_UINT8_e9d65c05_7608_43fb_a582_3a4127e4fdb4,
    Engagement_Request_T_2_BIT_UINT8_0354bfb4_34b3_4dcc_9591_f6ad0a7d4f09,
    Spare1_T_1_BIT_UINT8_9af521db_87f6_45b9_84a3_87587428561b,
    Spare2_T_1_BIT_UINT8_9be6326a_ba97_46a5_8295_c7c733f142df,
    Plan_Status_T_2_BIT_UINT8_118c9b08_60ef_4fc1_9b5f_10cdedfdb8e8,
    Engagement_Status_T_3_BIT_UINT8_8385ada5_2dcb_4858_8564_4836980eff19,
    Spare1_T_1_BIT_UINT8_c73e311a_14e3_40a4_b363_5517a8e267f4,
    Spare2_T_1_BIT_UINT8_bc2407e3_8f39_4842_a11c_c45b0fd8c3ab,
    Spare3_T_1_BIT_UINT8_64d91827_f7c1_4504_a88b_61475d2c071c,
    Fire_Mode_T_1_BIT_UINT8_8bdf62d1_45f7_41ab_b805_5a0e1594df88,
    wSpare_T_7_BIT_UINT8_d7562fed_bf78_46ff_a119_af7d01feb6d1,
    X_Pos_T_SINT32_eeaf4845_fbc3_4b07_91ed_93d08d5b917a,
    Y_Pos_T_SINT32_a1a02a5b_9346_4f1b_92ff_f715cd74378d,
    Z_Pos_T_SINT32_83ee88ca_2e06_4927_ba12_f36a9fae9be7,
    Rel_Time_T_UINT32_92922097_86c3_45d3_a383_8c2e83dc1fdc,
    PIP_Pos_X_T_SINT32_8b8c452c_504f_4c8c_8fa1_53411700911c,
    PIP_Pos_Y_T_SINT32_5474ff6e_bab6_4b82_9744_22e253344294,
    PIP_Pos_Z_T_SINT32_b2cbb2e6_7193_4d90_8e2d_71bf5d3c4bc3,
    MFU1_T_1_BIT_UINT8_a3c7c1a7_73ef_4f31_8bad_7490f0a4af04,
    MFU2_T_1_BIT_UINT8_eea01408_1f74_4e97_bf64_c13ca64cd3ea,
    MFU3_T_1_BIT_UINT8_2e9836b1_bb25_4dc5_a9e9_b53ed0f3f73c,
    MFU4_T_1_BIT_UINT8_62e44ae0_5e1a_4cfb_8152_7ce38218760b,
    MFU5_T_1_BIT_UINT8_e01f00df_6c37_466d_9f9f_cfe46579416e,
    MFU6_T_1_BIT_UINT8_5e5fd650_70e7_4adf_98aa_6ef06f7c3464,
    Spare_T_2_BIT_UINT8_2d83fa0f_2ddb_42e5_ae4b_43695fb8a54f,
    q_MslID_TU8_0705e044_c6fe_49f7_ac3b_369ac51b4409,
    q_Engagement1ID_TU16_002_22b00d7c_536f_425a_ba40_c10efbc68f91,
    q_RdrTime_TReal64_001_72428c46_1c27_4d1e_9e05_9ff0371b5e08,
    q_RdrHorizontalFom_TU16_bcf0ff7c_02db_4955_818e_709444ce9dc1,
    q_RdrVerticalFom_TU16_fd2499a5_1685_49fe_8732_5d1d5c118f60,
    q_RdrAzimuthToMsl1_TU16_85cc4d11_72be_48f1_81f9_373c4a282960,
    q_RdrElevationToMsl1_TU16_a8a03b58_160a_4d87_810d_2b29bd7e6788,
    q_RdrRangeToMsl1_TU16_d3c335ea_1137_4b1d_8879_b0354d41e974,
    q_RdrMsl1Status_TU16_993d022b_54ef_4a82_8d01_34c512f0671d,
    q_NumOfMslInAir_TU8_fd1f1675_6f51_467a_abc3_6e6aac7125e7,
    Operability_T_2_BIT_UINT8_8f0ec644_2e0d_45e4_a569_96163fbf2440,
    Mode_T_4_BIT_UINT8_f1a331a5_45e6_4d4c_bf85_5d5b3836533b,
    Type_T_1_BIT_UINT8_3ef3b326_8c14_4044_9a27_a6b7cc6ef570,
    Spare_T_1_BIT_UINT8_04831496_c06d_4983_8db4_015ee2e3f73f,
    MCU_State_T_3_BIT_UINT8_5ca2eb9a_fcac_4499_af16_b1f2bcf68300,
    LM_Readiness_To_Engage_T_2_BIT_UINT8_9f19e764_62b7_411d_979c_d44c842d4779,
    LM_Armed_T_1_BIT_UINT8_f58853bb_6101_4277_9a58_9cdb9a1e8750,
    MCU_Abort_Button_Pressed_T_1_BIT_UINT8_a59d2671_ad0e_4ace_b730_7e5f82739191,
    Spare2_T_1_BIT_UINT8_809f729f_5262_4eb9_a4c0_29b626b0e8e3,
    Not_In_Immediate_Status_T_1_BIT_UINT16_4a355c80_6097_4d53_863c_ae90f4607b1b,
    Conflict_In_The_Fire_Source_T_1_BIT_UINT16_79ce371b_a418_48a6_9fcc_a9a5fac9c770,
    No_Missile_Available_T_1_BIT_UINT16_7f0dc95e_7526_4592_8104_9dc2480fcea6,
    Uplink_Activated_T_1_BIT_UINT16_8df89a14_b8dc_4ba3_a122_aad21091404f,
    Launch_Fail_T_1_BIT_UINT16_032f85a2_f2d3_4fb6_9388_f53eb00758c7,
    MCU_Failure_T_1_BIT_UINT16_25c4d462_f109_4023_8d65_4d64922cd1a5,
    LCU_Failure_T_1_BIT_UINT16_0a422ea4_6da8_466d_be10_5c904de3158a,
    MDCU_Failure_T_1_BIT_UINT16_a37b2c3f_a5d2_4087_af33_fadb4b703fa5,
    BNET_Failure_And_LOAL_T_1_BIT_UINT16_b3ae9f56_ae99_426a_9228_374528b12b71,
    INS_Failure_T_1_BIT_UINT16_da0e381a_f810_4ce5_a2e8_a586de26503c,
    GPS_Failure_T_1_BIT_UINT16_c2d5e81a_c750_467d_ac6a_6d5eedde2e85,
    Time_Validity_Failure_T_1_BIT_UINT16_d071aee3_acae_42c3_990e_647eccfd4d9d,
    Pwr_Supply_Failure_T_1_BIT_UINT16_7730ca4b_ef02_456b_84bd_677ff32644a6,
    MCU_disconnected_T_1_BIT_UINT16_453f4827_4166_442e_a2bd_9db2cdd043df,
    Turret_In_No_Launch_Sector_T_1_BIT_UINT16_b0af6ead_e494_40c8_aa6d_07fd32abbfe8,
    Spare3_T_1_BIT_UINT16_4675fbd1_d679_44c6_aed5_fe7a4d40644d,
    COP_Client_Disconnected_T_1_BIT_UINT16_536efe2a_3b0a_448e_b1be_31f57d9d932a,
    COP_Server_Disconnected_T_1_BIT_UINT16_b744bef3_164d_40f7_8531_34695e678349,
    CCU_Server_Disconnected_T_1_BIT_UINT16_17ed85a9_6107_4850_a64a_93e10761964a,
    COP_CCU_Desync_T_1_BIT_UINT16_2cccd484_b5dd_4aad_9942_332fab68f51d,
    COP_MCU_Desync_T_1_BIT_UINT16_07f19728_8baa_495a_8449_b9f255e6e89e,
    DLQ_Smaller_Than_Threashold_T_1_BIT_UINT16_9b8a9c2b_87c7_49ef_9ce4_6b7952e7bf82,
    COP_Client_Faulty_T_1_BIT_UINT16_6aea280b_43a8_43a5_98e8_555b95f232b3,
    COP_Server_Faulty_T_1_BIT_UINT16_6704a723_0520_44f3_92f1_8da9b7dd47e6,
    CCU_Server_Faulty_T_1_BIT_UINT16_7a3138ac_58e1_4430_8488_6aaaca0e7eef,
    DTM_Comp_T_1_BIT_UINT16_0315f382_1898_4396_99bd_6bcefe426234,
    SICS_Faulty_T_1_BIT_UINT16_a93b4214_4d00_461b_b9a5_872ad4c264b5,
    Toplite_Faulty_T_1_BIT_UINT16_41badab3_c512_451a_904e_7934c443dded,
    Spare4_T_1_BIT_UINT16_648b2ef4_7aa8_4615_91bd_0ff05c3ee5f8,
    Spare5_T_1_BIT_UINT16_38ce9280_ba32_4ad8_8e3a_db38872ac141,
    Spare6_T_1_BIT_UINT16_9bfb9f5b_c2de_4da3_8b52_2d5f330bf17f,
    Spare7_T_1_BIT_UINT16_ce611c65_5098_4e9e_9099_7e46c2249770,
    objP_T_4_BIT_UINT8_76a5044d_1da7_480b_9764_4ab082483c8f,
    Derby_T_4_BIT_UINT8_e59be195_4a49_44db_8123_9c9498f14a72,
    ER_Derby_T_4_BIT_UINT8_d1e609d8_23d4_4287_a50c_109336f72a25,
    LR_Derby_T_4_BIT_UINT8_2a43448e_a83a_48c7_ba96_5e7215e34183,
    objP_T_4_BIT_UINT8_1cac2eb5_774f_4b20_b932_3d9732f4bfc1,
    Derby_T_4_BIT_UINT8_38971f62_9323_43aa_92c1_42c5c3146324,
    ER_Derby_T_4_BIT_UINT8_0f7d8506_324e_498e_8ac9_cc184209c021,
    LR_Derby_T_4_BIT_UINT8_d14653ee_6a51_46c3_96bd_d818f66f1b68,
    Latitude_T_FLOAT32_3e4bba20_c47e_4474_8dfc_50cd33f31c8c,
    Longitude_T_FLOAT32_80b0933d_82df_45f8_8d36_6e7a3a252da4,
    Altitude_T_FLOAT32_1c4df4f1_6d94_4863_88a0_31e8b8f68e07,
    Start_Azimuth_T_UINT16_12d0a5dd_a23d_451e_ac42_da8321265c43,
    Sector_Size_T_UINT16_eff07ec7_621d_4c42_94b1_d51905c80094,
    MCU_T_2_BIT_UINT32_caf15730_e070_4029_bb8d_57c29393eb31,
    INS_T_2_BIT_UINT32_6f079d30_a488_43da_9d5c_a7c87be7305c,
    GPS_T_2_BIT_UINT32_f56bcf78_a874_4d44_9fb3_e2741c20201a,
    MDCU_T_2_BIT_UINT32_56063872_6515_401a_87da_58d32fdebce7,
    LCU1_T_2_BIT_UINT32_a96fe027_0216_440f_a2f6_22a6460b6c5d,
    LCU2_T_2_BIT_UINT32_04b8dc04_b14d_4b4f_8df7_c1caeef39317,
    BNET_T_2_BIT_UINT32_7c9a2ef9_52ee_4a07_b073_7e948a2f5653,
    Time_Validity_T_2_BIT_UINT32_d70e3537_70d0_49a2_be66_8785e7405a37,
    mDRS_T_2_BIT_UINT32_ce745225_84f9_4644_bfde_1ac6b1ddff3f,
    TopLite_T_2_BIT_UINT32_01b86a96_9ce6_4958_8837_83413a5b533a,
    Rubidium_T_2_BIT_UINT32_54951abc_fcb6_4f3c_875e_e958fe285cfa,
    Spare3_T_2_BIT_UINT32_62c66e30_bce3_4562_8cb6_8d51da9cefcd,
    Spare4_T_2_BIT_UINT32_d0c0793b_ebcb_4ffc_a67b_9437c97609fc,
    Spare5_T_2_BIT_UINT32_ecd4f668_b0de_4567_b829_b78d598d2f69,
    Spare6_T_2_BIT_UINT32_697f69d4_09f5_4a79_9153_195d92fd1098,
    Spare7_T_2_BIT_UINT32_884245f3_6c4a_4c2a_9ee9_d2f7c840edc8,
    Data_Source_T_1_BIT_UINT8_a2c4ee7a_6c45_4235_be85_ac88d09255dd,
    DLQ_Value_T_7_BIT_UINT8_c93a8896_b2c5_4c7a_b3d7_5851824ef00f,
    MCU_T_UINT16_0b0a8911_07cd_4666_b28f_b45fed74f1b1,
    BNET_T_UINT16_0f95abc8_7178_4f3f_ac54_c77cd1d218ab,
    INS_T_UINT16_22508071_942d_4627_b2f6_a193367373b1,
    MDCU_T_UINT16_12dbb7dc_1da9_4141_a401_a7f59875ffd2,
    LCU_T_UINT16_61dcdb3c_3fe2_4f0a_b99a_2e55eb01ce72,
    COP_Server_Version_T_UINT16_6d8f95d9_8274_41be_aac6_8b0119c42f53,
    COP_Client_Version_T_UINT16_7ca443a5_91a7_4843_804a_eb5a41612978,
    ICS_DTM_Version_T_UINT16_d2f4751f_a4a9_4e5a_9ba8_cdbabe86705d,
    MCU_Engagement_Handling_T_3_BIT_UINT8_cbfc1b00_acd3_4d65_9dab_6b9d5af6fc45,
    MCU_Engagement_Status_T_2_BIT_UINT8_b8036856_9bcb_480a_8e24_67c5f91e3fc9,
    Spare_T_1_BIT_UINT8_af648298_7501_467f_8ffa_efcac15fb17f,
    Engagement_Abort_Source_T_2_BIT_UINT8_73bf31a8_8bbe_456d_888e_1281176f8dcb,
    ICP_Allocated_T_2_BIT_UINT8_6dde0994_689b_43fb_8371_3982be1fd4c3,
    objP_ICP_Lock_T_1_BIT_UINT8_8585583a_1a14_4bfb_ab97_6991ab763abd,
    Derby_ICP_Lock_T_1_BIT_UINT8_8617f769_e03b_4dee_ae83_f4dcc83a9a28,
    ER_Derby_ICP_Lock_T_1_BIT_UINT8_f6c09481_eaba_44f5_8161_b53dd93d9e3f,
    LR_Derby_ICP_Lock_T_1_BIT_UINT8_fa7d9d08_1449_494c_95ba_29fc6c229f66,
    ICP_Stage_T_1_BIT_UINT8_f5fc65a6_b3df_4f8d_b991_b7628f8ada58,
    Lock_Type_T_1_BIT_UINT8_bbf19f87_340b_45af_845e_953a8528d084,
    Misfire_Indication_T_1_BIT_UINT8_3c6348db_5929_4c23_953d_982b441258c9,
    Misfire_Reason_T_2_BIT_UINT8_fecf3e6b_a781_45a5_a121_a26893a6f5ad,
    spare_T_5_BIT_UINT8_81879ce0_69f8_4726_9683_cc364c591aee,
    Seeker_Mode_T_1_BIT_UINT8_58d5e937_3acf_48ae_924a_f7835b9d481a,
    SPare1_T_1_BIT_UINT8_ea828b75_d6c2_442c_9d35_c9ba15bd0a9c,
    Spare2_T_1_BIT_UINT8_7698d824_3711_455d_9aab_4a02dad53d2d,
    Spare3_T_1_BIT_UINT8_7ee8cecf_e007_4860_ad08_c4a8d216b23a,
    Spare4_T_2_BIT_UINT8_05454b7a_52da_4704_83c1_93338d49629c,
    Spare5_T_2_BIT_UINT8_a43ee928_d428_4381_a6c1_7d1ec39e598f,
    Track_ID_T_UINT16_fcd5d2c6_cdaa_49c1_ada9_6e0f48ea1c9b,
    CCU_Engagement_ID_T_UINT16_08829c96_1c3a_4f5f_b1e6_8273eeb2090a,
    COP_Engagement_ID_T_UINT16_508c88cf_4f0c_489b_86dd_465bd4f232eb,
    Engagement_Launch_Time_T_UINT32_fd534a87_ae89_49e3_b23f_6680c6acb774,
    Turret_Launching_Azimuth_T_UINT16_ac186391_2e73_4de7_83e8_985cc84c6251,
    q_Spare2Bits_TU16_2_001_da2afb7c_83af_4205_a505_ab5f9bc3c3e8,
    q_Abort_TU16_2_001_ff638045_4260_4e1a_97f3_b01bfa34eeea,
    q_PfStatus_TU16_4_001_2de6e39b_c651_4a7b_bc33_4c1f71cf2296,
    q_Pointingstatus_TU16_4_001_edfd44dc_7c13_4e2d_a2be_38ab77231d58,
    q_PropulsionStage_TU16_4_001_5c16d5d7_c7fc_484d_af75_9455530fd1b1,
    q_UpLinkStatus_TU8_4_001_7f6df6bc_73ce_41d6_8e15_0e6e360e105f,
    q_SeekerState_TU8_4_001_ef6da7bc_0f67_43ff_927e_0ba66a00c86c,
    q_EcmStatus_TU8_4_001_517dd728_5a87_4146_914a_1ee45917cda2,
    q_Spare_TU8_4_001_b817ca5f_e633_43b3_9b8b_806a4b610ac3,
    q_EsadStatus_TU8_4_001_35c474a3_b55e_4870_8cb4_0e1d487435a8,
    q_EIsadStatus_TU8_4_001_47e06d9e_9b4b_4fbf_84a6_7113bba1c63c,
    q_MslID_T_UINT8_292715fb_4b48_4e51_9d40_ed026488aa91,
    q_NavTimeTag_TU16_002_c0097ec7_7600_4a85_a878_87fa333d8af0,
    q_MslXL0_TS16_001_44f181dc_7682_4eb1_87ad_c0000b3d3ba1,
    q_MslYL0_TS16_001_19262d37_4a60_430f_ad6b_dcdcbeccc6fa,
    q_MslZL0_TS16_001_97f7d429_ea86_4d81_ad14_5775df755beb,
    q_MslVxL0_TS16_001_b3213180_81f3_4095_96d3_cc96b7af9cf8,
    q_MslVyL0_TS16_001_0836e383_381d_44d4_8499_1c19fd0664d9,
    q_MslVzL0_TS16_001_a3ffcc3a_999d_4f13_b4f1_2ff3861704a5,
    q_EulerAlpha_TS16_001_60d39747_bea2_4054_9b7c_d08a16b5bace,
    q_EulerBeta_TS16_001_d1a94e4c_e0ff_4e9c_b18d_e3cd56dc1bf8,
    q_EulerGama_TS16_001_768e354c_b9c0_4e71_8c3e_95d27f6a0203,
    MslNavPosUncertainty_TU8_cb49c6a7_afef_499d_91c9_2ae9a08ff0a5,
    MslNavVelUncertainty_TU8_b1e54f26_1053_4aa7_bf23_97a41e657a4a,
    q_RangeMslTarget_TS16_a32fa25a_baa6_4910_8950_64a155cf8104,
    q_Tgo_TU8_5ef3d2cb_48d8_4f05_b33a_8a8262464b05,
    q_TtoActivateSeeker_TU8_1c8d6b99_17ac_4096_97f4_5082f44ae623,
    q_GuidanceMode_TU8_b4178bc2_23aa_406b_acd8_4605d2066163,
    q_GimbalTheta_TU8_2d8ce243_aeb4_420f_a203_8b3739fb9005,
    q_GimbalPsi_TU8_5cf3fd22_2264_4ff8_a911_9f97b81ce4d8,
    q_Range_TU8_0b84bbf2_4703_462e_9e18_543f1b74644f,
    q_RDOT_TU8_d6097dd2_9f94_4efe_88da_041b9cd6053e,
    q_ImuStatus_TU8_6700d067_ce31_45bc_b4af_7c4c866a0c6f,
    q_SeekerStatus_TU8_6fa54310_e49c_4365_956e_c182a6f76472,
    q_VesselPres_TU8_41828f4f_c33a_454a_861a_27b68a56047a,
    q_MslData1_TU32_184f33df_fc78_472b_bf6f_27145bae347c,
    q_MslData2_TU32_8ad05827_c87f_4df2_8f3a_1947255cd2d9,
    q_MslData3_TU32_3dc239bb_ebbf_4faa_bcc0_1489ae9887db,
    q_MslData4_TU32_67a32579_a6cd_4271_ae43_db6bbacaf44b,
    q_MslData5_TU32_b1a40e00_61e1_490b_8d08_a7662c5f6c39,
    q_NumOfMslInAir_TU8_adfe5860_f7f4_4798_b916_8dd2d853d30a,
    TargetUpdateTime_TU32_6b1820c3_626a_4184_a20e_623e6851610c,
    TargetPositionNorth_T_SINT32_670a12e9_99f4_4d8c_9505_52b752e6f231,
    TargetPositionEast_T_SINT32_201eda6c_dadf_4511_a154_7953127a1c8a,
    TargetPositionDown_T_SINT32_1ad667e5_2036_4602_9be0_cdb33b577630,
    TargetVelocityNorth_T_SINT32_81fc3cba_4ea9_4278_82c0_e66f669c386c,
    TargetVelocityEast_T_SINT32_244abd48_fb4f_438f_bfc3_f9cad677cff5,
    TargetVelocityDown_T_SINT32_0cbec0b7_45d6_460f_b844_456379d6a46b,
    AutonomousTargetID_T_UINT16_283c3389_5761_4e3a_a3d5_d8adf731dc8d,
    AutonomousEngagementID_T_UINT16_3e1f17fa_4e08_488e_b678_328c5f2e1144,
    TopliteTargetType_TU8_35f3e5ce_8052_4e06_872a_0e1533d20d06,
    Validity_T_1_BIT_UINT8_a73aa0b0_8e80_4725_a0f2_f9d7ed5ba7f6,
    spare_T_7_BIT_UINT8_acdd4034_48b5_4ce6_b505_d2f8c9a3c181,
    live_tracks_T_UINT8_64847538_5bf5_472c_a938_58717e94957f,
    number_of_tracks_T_UINT8_48201a7b_7261_45d0_89be_28ba2ed080e2,
    Spare_T_UINT16_ee8724aa_8840_4d5f_8bac_ed413ca99577,
    Live_Tracks_T_UINT8_490932e5_c337_4781_b38e_24d82ba222a9,
    C2_Mode_T_UINT8_f52f8bad_1cfa_48aa_bc19_ba49e708462f,
    C2_Substate_T_UINT8_bdebfd51_853b_4460_b7f8_05c477188755,
    C2_Safety_State_T_UINT8_6e7e5779_4e59_4f89_b4b2_12f1e4c46c40,
    C2_Fire_Readiness_Status_T_UINT8_d2c24998_784e_4f0a_83af_88416eefa57c,
    C2_Status_T_UINT8_44e4c68f_3f7a_441b_a982_626bdc2e1198,
    Num_Of_Conneted_Sensors_T_UINT8_adfe01e9_51f5_4873_84c8_c0e103011244,
    Spare_T_UINT16_cdfbff38_0dde_4848_a81c_f975bd7860c2,
    Num_Of_Connected_MFUs_T_UINT8_f645320b_f749_4e9b_afb6_62d481b86f36,
    Sequence_num_T_UINT32_313a04c1_e7ab_486a_a0ba_6ee276ad08f2,
    Num_Of_GSPs_T_UINT8_c2db8f8a_ae73_4127_8f40_a8a30801c057,
    Last_In_Cycle_T_UINT8_97c5e6fd_2ed3_4dc1_a294_f6ff5ea19993,
    Spare_T_UINT16_a96ccb41_39be_412e_801f_5d3df80e700f,
    Track_ID_T_UINT16_11fe0ff6_32da_474e_8c24_1edd8184f028,
    CCU_Engagement_ID_T_UINT16_6caa7cf7_6ede_4349_a3fb_87d34db5d494,
    Freq_Allocation_T_UINT8_f04bbe34_39fa_431e_9b21_17f28fa66e9f,
    Channel_Allocation_Index_T_UINT8_a9b7e2b6_69e6_41bd_9fea_8a56a2f9dcd0,
    Fire_Control2_T_UINT8_5633dd03_258c_4f9b_b7ca_9e5f33d1cde6,
    Abort_Engagement_T_UINT8_84d75e19_ba48_475e_96d0_b4abbc38b854,
    CCU_Time_T_UINT32_dec03fdc_c194_41fe_9db6_90bfff3d2909,
    Magic_Word_T_UINT32_231f0105_50f9_41d5_90d9_bbdaa4d70368,
    Spare_T_UINT16_82e6aa77_9e6c_4698_8f75_a9e4873c9c7b,
    ICP_Plan_Exists_T_UINT8_f2f01884_1420_4c74_9f06_9aad8c72b10c,
    Number_Of_Points_In_Traj_T_UINT8_e9e9754f_9268_4137_9ee8_e3a7aa061295,
    Text_Length_T_UINT8_37113846_cb79_4a6b_966d_5a61be5ab191,
    Chat_Text_T_CHAR_e2f96c40_0fc6_495b_b53b_ee71057010e3,
    Text_Length_T_UINT8_62609524_b521_4e80_8f65_a080d4a9adc3,
    Text_T_CHAR_fe4888b2_ba47_4345_a916_757fa6e4c6d6,
    Latitude_T_FLOAT32_5c9d5f03_dfc2_41a8_9107_a84149332eab,
    Longitude_T_FLOAT32_280114d1_733c_42fe_ad3e_decf3e4ad1bd,
    MCU_Serial_ID_T_UINT8_7a3dee28_cc01_4963_b72a_7637cf690cb2,
    Network_Size_T_UINT8_175879c1_f8f3_41a0_9a9c_403bad1070f5,
    Turret_Azimuth_T_UINT16_dbdcaed5_c24a_4e7d_a4f1_8493835fc680,
    Cabin_Azimuth_T_UINT32_7419cc7b_46dd_408b_a11c_355f388e28df,
    Number_Of_Sectors_T_UINT8_0b00ea4b_76bb_4c69_8296_21054c081d6b,
    Chat_Text_Length_T_UINT8_75275dbe_6d13_4310_bec4_1b9d920c61f0,
    Chat_Text_T_CHAR_55b71ec7_3c64_46eb_806a_ca122f537aa6,
    Toplite_Azimuth_T_UINT16_3b8b8563_e5f2_42f7_85a0_819b6543dad5,
    Toplite_Elevation_T_UINT16_44083aad_4cd5_4b4c_8644_50cd1edec391,
    Toplite_Target_Range_T_UINT32_be358cf7_ba6e_4d7f_bf59_fa79ec904abe,
    COPTopliteEngagementIndication_T_2_BIT_UINT8_5f91e315_02d4_47f9_91cf_60df46fdff7d,
    Spare_T_6_BIT_UINT8_b962dcd7_18d9_45f9_8caa_7a3f89ae7b1c,
    Number_Of_Engagements_T_UINT8_4f8de101_74bd_48c6_a33f_c34afa14bf83,
    Spare2_T_UINT8_2375aa06_09c2_4d70_b8e8_98452c5b67ea,
    Sequence_num_T_UINT32_ea2981e9_13e4_49a0_991f_c32e2949b949,
    Text_Length_T_UINT8_0e6a3891_91cb_4063_82c1_36518c5f33a2,
    Text_T_CHAR_c3386610_a74f_4209_aecd_b6233480b08b,
    Latitude_T_FLOAT32_ba3a03d1_69b4_467c_8fb0_4209562c34ab,
    Longitude_T_FLOAT32_7a23e00b_e3d7_440b_8ad8_41447742edfe,
    q_MslID_TU8_af7d7c03_e9cb_4565_a933_f63e05203bdc,
    q_MissleDataBuffer_TU8_fcde7e09_8e65_4a68_896a_75619553c864
}

function proto_cop.dissector(buffer, pinfo, tree)
    if buffer:len() < 18 then return end

    local subtree = tree:add(proto_cop, buffer(), "COP v3_3")
    local offset = 0
    local subtree_header = subtree:add(proto_cop, buffer(offset, 18), "Header")
    local fieldValue = 0
    local msg_type_val = buffer(offset, 1):le_uint()
    fieldValue = buffer(offset, 1):le_uint()
    subtree_header:add(
        msg_type_header_msg_type_db78c392_4550_47fe_995b_e096d8a2fc5d,
        buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
    offset = offset + 1
    fieldValue = buffer(offset, 2):le_uint()
    subtree_header:add(
        msg_length_header_msg_length_43e9cc4e_0f1a_4138_abbb_ae72f0a9969f,
        buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
    offset = offset + 2
    fieldValue = buffer(offset, 2):le_uint()
    subtree_header:add(
        msg_number_header_msg_number_b6b9ad9c_89d1_41e8_92d5_a75f38e0e0b6,
        buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
    offset = offset + 2
    fieldValue = buffer(offset, 2):le_uint()
    subtree_header:add(
        sender_header_sender_cd49b1ae_ce10_4ebd_b1ce_72c6ace26866,
        buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
    offset = offset + 2
    fieldValue = buffer(offset, 1):le_uint()
    subtree_header:add(sim_flag_T_UINT8_454eae62_77cc_4bef_952a_dd6d6ed2b7ad,
        buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
    offset = offset + 1
    fieldValue = buffer(offset, 8):le_float()
    subtree_header:add(
        msg_sent_time_header_msg_sent_time_9efeb58c_fe7c_4227_a9aa_c9d421938332,
        buffer(offset, 8)):append_text(" (" .. fieldValue .. ")")
    offset = offset + 8
    fieldValue = buffer(offset, 2):le_uint()
    subtree_header:add(
        msg_checksum_header_msg_checksum_58775c64_6950_477f_b268_45045a657997,
        buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
    offset = offset + 2

    pinfo.cols.protocol = "COP v3_3 (" .. msg_type_val .. ")"

    if msg_type_val == 1 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset),
            "C2_COP_ASP_Track")
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            live_tracks_T_UINT8_64847538_5bf5_472c_a938_58717e94957f,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        local number_of_tracks = fieldValue
        msg_subtree:add(
            number_of_tracks_T_UINT8_48201a7b_7261_45d0_89be_28ba2ed080e2,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(Spare_T_UINT16_ee8724aa_8840_4d5f_8bac_ed413ca99577,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        for i = 0, number_of_tracks - 1 do
            local msg_subtree_mode = msg_subtree:add(proto_cop,
                buffer(offset, 42),
                "ASP_Track[" .. (i + 1) ..
                "]")
            local newOffset = offset
            local fieldValue_mode = 0
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                Track_ID_T_UINT16_7f0843c7_3985_499f_a795_4ef280d45404,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                FCR_track_ID_T_UINT16_dd6b7e2d_1e86_4344_8467_2f8bf3626731,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 5):string()
            msg_subtree_mode:add(
                TN_str_T_CHAR_4f880c7c_61eb_4570_baea_db185a07e591,
                buffer(newOffset, 5))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 5
            fieldValue_mode = buffer(newOffset, 4):le_float()
            msg_subtree_mode:add(
                track_update_time_T_FLOAT32_29af4c5a_e7da_464a_8aba_d57930f5dac9,
                buffer(newOffset, 4))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 4
            fieldValue_mode = buffer(newOffset, 1):le_uint()
            msg_subtree_mode:add(
                target_type_T_UINT8_6d22b7ba_2db4_4bb3_831b_dfbc65f59122,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 1):le_uint()
            msg_subtree_mode:add(
                target_identity_T_UINT8_a2646523_2517_4aa8_9444_324227d85fa4,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 1):bitfield(4, 4)
            msg_subtree_mode:add(
                Threat_Level_T_4_BIT_UINT8_449e2289_00b5_4064_a6d0_60d93d8da3d6,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(2, 2)
            msg_subtree_mode:add(
                Tracking_Status_T_2_BIT_UINT8_81b184a3_34e4_45cc_b121_c0fd5e8fc2dd,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(0, 2)
            msg_subtree_mode:add(
                Spare_T_2_BIT_UINT8_a516bf33_8ee9_4867_adff_11ce6ecf1032,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 1):bitfield(7, 1)
            msg_subtree_mode:add(
                Militry_emergency_T_1_BIT_UINT8_3028f864_f2b2_4657_bc21_993295971959,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(6, 1)
            msg_subtree_mode:add(
                Special_Identification_T_1_BIT_UINT8_183483ea_8fce_4104_be8b_bdb410aa6561,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(4, 2)
            msg_subtree_mode:add(
                Foe_Friend_T_2_BIT_UINT8_8bf6a277_862e_4cdd_814f_062c5dcdf206,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(3, 1)
            msg_subtree_mode:add(
                Jammer_T_1_BIT_UINT8_19403d56_a38e_480f_9153_27dbc12ce9b1,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(2, 1)
            msg_subtree_mode:add(
                Spare1_T_1_BIT_UINT8_2a82d0a2_68ca_4b34_838e_d5e989504819,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(1, 1)
            msg_subtree_mode:add(
                Spare2_T_1_BIT_UINT8_907afce3_868f_4c78_91c4_0ed37e98a67f,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(0, 1)
            msg_subtree_mode:add(
                Spare3_T_1_BIT_UINT8_fcaf84f5_b608_44a0_8eda_49855a2f5827,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 1):le_uint()
            msg_subtree_mode:add(
                SIF_1_Code_T_UINT8_efb0892b_3106_42ab_9c58_05ccb5bc94e1,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                SIF_2_Code_T_UINT16_56dbad66_dfce_45f8_8400_7085d17e45de,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                SIF_3_Code_T_UINT16_3e6d5d21_2cba_46c1_bc01_0aaec0aca12b,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 4):le_int()
            msg_subtree_mode:add(
                X_Pos_T_SINT32_eef02520_bf77_4082_bdf9_974253d764d3,
                buffer(newOffset, 4))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 4
            fieldValue_mode = buffer(newOffset, 4):le_int()
            msg_subtree_mode:add(
                Y_Pos_T_SINT32_558dfe57_e891_41fe_9a42_d951f67c52f9,
                buffer(newOffset, 4))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 4
            fieldValue_mode = buffer(newOffset, 4):le_int()
            msg_subtree_mode:add(
                Z_Pos_T_SINT32_e1ce30ec_b69d_4c64_b2be_e3ca40e88168,
                buffer(newOffset, 4))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 4
            fieldValue_mode = buffer(newOffset, 4):le_int()
            msg_subtree_mode:add(
                Vx_T_SINT32_c7df902a_e0fb_4565_9143_9e3c0ad37e4a,
                buffer(newOffset, 4))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 4
            fieldValue_mode = buffer(newOffset, 4):le_int()
            msg_subtree_mode:add(
                Vy_T_SINT32_aeddb5f1_0a83_43cc_953e_4fb647aa00ec,
                buffer(newOffset, 4))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 4
            fieldValue_mode = buffer(newOffset, 4):le_int()
            msg_subtree_mode:add(
                Vz_T_SINT32_2184a7aa_d116_4042_bd09_65fa7a91c269,
                buffer(newOffset, 4))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 4
            fieldValue_mode = buffer(newOffset, 1):le_uint()
            msg_subtree_mode:add(
                Spare_T_UINT8_23007b50_dd1b_4f7e_ab8f_cff2483d17cc,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            offset = newOffset
        end
        return
    end
    if msg_type_val == 2 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset),
            "C2_COP_Engaged_Track")
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            Live_Tracks_T_UINT8_490932e5_c337_4781_b38e_24d82ba222a9,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(Track_ID_T_UINT16_3c7114b2_8fa6_444e_94de_072fb7f8d86e,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            target_type_T_UINT8_9e212e03_2885_4322_a6e0_3892f7b69890,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(
            track_update_time_T_FLOAT32_4074622e_8b0d_421e_aa3d_9eaa50306ecf,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(X_T_FLOAT32_5761c97d_827e_41be_b4e7_d9de8e712905,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Y_T_FLOAT32_44dcc04b_cd80_4168_bb14_f96f8c050761,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Z_T_FLOAT32_e37a1b2b_ed68_4f6d_86b2_39e02ac27d92,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(Vx_T_SINT32_c7df902a_e0fb_4565_9143_9e3c0ad37e4a,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(Vy_T_SINT32_aeddb5f1_0a83_43cc_953e_4fb647aa00ec,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(Vz_T_SINT32_2184a7aa_d116_4042_bd09_65fa7a91c269,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Var_XX_T_FLOAT32_4fa38e8b_3788_493a_b315_067953c4227f,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Cov_XY_T_FLOAT32_2c2cf207_126b_49a0_8de2_12ea209bbfea,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Cov_XZ_T_FLOAT32_d5fffafa_8648_4b7d_8059_8a512ac4fc7a,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Var_YY_T_FLOAT32_cada1991_7f52_43ea_a207_55ed98cc47a0,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Cov_YZ_T_FLOAT32_32513bc2_4903_4f3a_8556_72cf2a7e8bc4,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Var_ZZ_T_FLOAT32_a3d2fc24_cf35_4818_b74a_d6484b41c089,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Var_XX_T_FLOAT32_4fa38e8b_3788_493a_b315_067953c4227f,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Cov_XY_T_FLOAT32_2c2cf207_126b_49a0_8de2_12ea209bbfea,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Cov_XZ_T_FLOAT32_d5fffafa_8648_4b7d_8059_8a512ac4fc7a,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Var_YY_T_FLOAT32_cada1991_7f52_43ea_a207_55ed98cc47a0,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Cov_YZ_T_FLOAT32_32513bc2_4903_4f3a_8556_72cf2a7e8bc4,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Var_ZZ_T_FLOAT32_a3d2fc24_cf35_4818_b74a_d6484b41c089,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(
            Longitude_T_FLOAT32_0ab63f31_0c48_44c4_a5be_e1befbf0158a,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Latitude_T_FLOAT32_97d88032_2388_41cb_9fc4_e4e28f850f56,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Altitude_T_FLOAT32_eedeb579_e98f_4a29_b6e5_9f1379cf7333,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(
            Sensor_To_Target_Azimuth_T_FLOAT32_8e633cc5_05d9_4182_ae67_d6bc7a6e45f1,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(
            Sensor_To_Target_Elevation_T_FLOAT32_1804bee6_be13_4448_82ae_499fe26e8b5a,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            Target_Source_Radar_type_T_UINT8_b12bbfc0_ce15_4576_ad6c_8d6a607761a5,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(Spare_T_UINT16_3bdb05fd_6301_4be4_9df8_3509faf4dc2a,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        return
    end
    if msg_type_val == 4 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset),
            "C2_COP_Status")
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(C2_Mode_T_UINT8_f52f8bad_1cfa_48aa_bc19_ba49e708462f,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            C2_Substate_T_UINT8_bdebfd51_853b_4460_b7f8_05c477188755,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            C2_Safety_State_T_UINT8_6e7e5779_4e59_4f89_b4b2_12f1e4c46c40,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            C2_Fire_Readiness_Status_T_UINT8_d2c24998_784e_4f0a_83af_88416eefa57c,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(C2_Status_T_UINT8_44e4c68f_3f7a_441b_a982_626bdc2e1198,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(X_Pos_T_SINT32_eef02520_bf77_4082_bdf9_974253d764d3,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(Y_Pos_T_SINT32_558dfe57_e891_41fe_9a42_d951f67c52f9,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(Z_Pos_T_SINT32_e1ce30ec_b69d_4c64_b2be_e3ca40e88168,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            Num_Of_Conneted_Sensors_T_UINT8_adfe01e9_51f5_4873_84c8_c0e103011244,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(X_Pos_T_SINT32_eef02520_bf77_4082_bdf9_974253d764d3,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(Y_Pos_T_SINT32_558dfe57_e891_41fe_9a42_d951f67c52f9,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(Z_Pos_T_SINT32_e1ce30ec_b69d_4c64_b2be_e3ca40e88168,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(Radar_Type_T_UINT8_6133cd1d_789d_4e0c_b480_c940446e9c3a,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(Spare_T_UINT16_cdfbff38_0dde_4848_a81c_f975bd7860c2,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 1):bitfield(7, 1)
        msg_subtree:add(
            COP_Logical_ID1_T_1_BIT_UINT8_a9860ee3_64cc_485a_904e_1f93b530d807,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(6, 1)
        msg_subtree:add(
            COP_Logical_ID2_T_1_BIT_UINT8_efef2ea0_7b7b_4732_8db4_86126e879a0b,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(5, 1)
        msg_subtree:add(
            COP_Logical_ID3_T_1_BIT_UINT8_cd2d0d32_39e2_481e_b939_85939f0c86fa,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(4, 1)
        msg_subtree:add(
            COP_Logical_ID4_T_1_BIT_UINT8_1c79cbd4_94a8_4892_8d66_cfa77149ec8e,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(3, 1)
        msg_subtree:add(
            COP_Logical_ID5_T_1_BIT_UINT8_2aa61d2a_5283_4c34_890b_87274e172466,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 1)
        msg_subtree:add(
            COP_Logical_ID6_T_1_BIT_UINT8_8b35b6f8_19fe_4970_825a_0fece632987b,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(1, 1)
        msg_subtree:add(
            bSpare1_T_1_BIT_UINT8_5c33d296_9343_4f4d_9635_3137a204e306,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 1)
        msg_subtree:add(
            bSpare2_T_1_BIT_UINT8_eb9186c3_7512_4cdc_8a41_3a8c0cce43f8,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            IBIT_Action_T_UINT8_f92c415d_0e2c_4dbf_a93e_61baa472e253,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        local Num_Of_Connected_MFUs = fieldValue
        msg_subtree:add(
            Num_Of_Connected_MFUs_T_UINT8_f645320b_f749_4e9b_afb6_62d481b86f36,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        for i = 0, Num_Of_Connected_MFUs - 1 do
            local msg_subtree_mode = msg_subtree:add(proto_cop,
                buffer(offset, 4),
                "COP_Initialization_Data_Array[" ..
                (i + 1) .. "]")
            local newOffset = offset
            local fieldValue_mode = 0
            fieldValue_mode = buffer(newOffset, 1):le_uint()
            msg_subtree_mode:add(
                COP_Logical_ID_T_UINT8_1af78059_9f4b_458e_867d_c63d0802db12,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 1):le_uint()
            msg_subtree_mode:add(
                MCU_Serial_ID_T_UINT8_b49d07a1_dcc4_4f58_b5d0_89eb599cb446,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 1):bitfield(7, 1)
            msg_subtree_mode:add(
                Link_Type_T_1_BIT_UINT8_c82e636a_dc1a_47c7_94bb_5d8f4f0eb041,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(6, 1)
            msg_subtree_mode:add(
                Locking_Policy_T_1_BIT_UINT8_7ac23c2a_cd0c_480f_9a4e_9f2050f424f3,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(5, 1)
            msg_subtree_mode:add(
                Fire_Source_T_1_BIT_UINT8_e3463b4b_32f2_4a0e_ab0f_e0766ec49b3a,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(4, 1)
            msg_subtree_mode:add(
                Spare1_T_1_BIT_UINT8_92e1d649_7988_4ecd_ae9f_76437edc0934,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(3, 1)
            msg_subtree_mode:add(
                Spare2_T_1_BIT_UINT8_610b035b_d712_4253_a3f2_011cd04be7b2,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(2, 1)
            msg_subtree_mode:add(
                Spare3_T_1_BIT_UINT8_e2e21eaa_db8c_469c_a903_606418b0e983,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(1, 1)
            msg_subtree_mode:add(
                Spare4_T_1_BIT_UINT8_3900936c_1297_4188_b6ea_c850b60ffb13,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(0, 1)
            msg_subtree_mode:add(
                Spare5_T_1_BIT_UINT8_31c18657_ee50_4816_9585_04bf3c316624,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 1):le_uint()
            local Site_Text_Length = fieldValue_mode
            msg_subtree_mode:add(
                Site_Text_Length_T_UINT8_2867ba33_c1e0_4842_af8f_45dfa18d8099,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, Site_Text_Length):string()
            msg_subtree_mode:add(
                Site_Name_T_CHAR_6380a20b_f93e_4734_9fbe_97a5ae5e4338,
                buffer(newOffset, Site_Text_Length)):append_text(" (" ..
                fieldValue_mode ..
                ")")
            newOffset = newOffset + Site_Text_Length
            offset = newOffset
        end
        return
    end
    if msg_type_val == 5 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset), "C2_COP_ACK")
        fieldValue = buffer(offset, 4):le_uint()
        msg_subtree:add(
            Sequence_num_T_UINT32_313a04c1_e7ab_486a_a0ba_6ee276ad08f2,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        return
    end
    if msg_type_val == 6 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset), "C2_COP_GSP")
        fieldValue = buffer(offset, 1):le_uint()
        local Num_Of_GSPs = fieldValue
        msg_subtree:add(
            Num_Of_GSPs_T_UINT8_c2db8f8a_ae73_4127_8f40_a8a30801c057,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            Last_In_Cycle_T_UINT8_97c5e6fd_2ed3_4dc1_a294_f6ff5ea19993,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        for i = 0, Num_Of_GSPs - 1 do
            ---- 163/35
            local msg_subtree_mode = msg_subtree:add(proto_cop,
                buffer(offset, 35),
                "GSP_Element[" .. (i + 1) ..
                "]")
            local newOffset = offset
            local fieldValue_mode = 0
            fieldValue_mode = buffer(newOffset, 1):le_uint()
            msg_subtree_mode:add(
                Area_Type_T_UINT8_4a6d5f69_aea9_461d_b3b6_f98685adf04c,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                Area_ID_T_UINT16_d17c9257_3eb3_42d9_81bf_3c857fda9882,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 1):bitfield(7, 1)
            msg_subtree_mode:add(
                Area_Status_T_1_BIT_UINT8_3aa3b417_8c67_4beb_8b6f_f24d533d3002,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(5, 2)
            msg_subtree_mode:add(
                Area_Hostility_T_2_BIT_UINT8_7d393218_71ab_4061_9a22_00a75e818d66,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(3, 2)
            msg_subtree_mode:add(
                SeverityOrWCOType_T_2_BIT_UINT8_3e74f626_80ae_4cfe_a6a9_9d29f7c585e0,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(0, 3)
            msg_subtree_mode:add(
                Spare_T_3_BIT_UINT8_420452b1_0a20_435a_b9dd_b6184fe47c04,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 1):le_uint()

            local Name_Length = fieldValue_mode

            msg_subtree_mode:add(
                Name_Length_T_UINT8_fed2df58_bb27_4d6f_94cd_3926646d177d,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, Name_Length):string()
            msg_subtree_mode:add(
                Name_T_CHAR_7b925d3a_40d2_4be9_8c85_4cbe7a0b9d43,
                buffer(newOffset, Name_Length)):append_text(" (" ..
                fieldValue_mode ..
                ")")
            newOffset = newOffset + Name_Length
            fieldValue_mode = buffer(newOffset, 1):le_uint()
            msg_subtree_mode:add(
                Object_Type_T_UINT8_d3cd4d2f_f675_4df6_b02a_b17c67b2f691,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 1):le_uint()
            msg_subtree_mode:add(
                Vertices_Num_T_UINT8_ebf124d6_24d8_467c_9519_88dd38617473,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            local cnt_points = fieldValue_mode
            for i = 0, cnt_points - 1 do
                ---cnt_points x
                point_subtree = msg_subtree_mode:add("ECEF Point " .. (i + 1))
                fieldValue_mode = buffer(newOffset, 4):le_int()
                point_subtree:add(
                    X_Pos_T_SINT32_eef02520_bf77_4082_bdf9_974253d764d3,
                    buffer(newOffset, 4)):append_text(
                    " (" .. fieldValue_mode .. ")")
                newOffset = newOffset + 4
                fieldValue_mode = buffer(newOffset, 4):le_int()
                point_subtree:add(
                    Y_Pos_T_SINT32_558dfe57_e891_41fe_9a42_d951f67c52f9,
                    buffer(newOffset, 4)):append_text(
                    " (" .. fieldValue_mode .. ")")
                newOffset = newOffset + 4
                fieldValue_mode = buffer(newOffset, 4):le_int()
                point_subtree:add(
                    Z_Pos_T_SINT32_e1ce30ec_b69d_4c64_b2be_e3ca40e88168,
                    buffer(newOffset, 4)):append_text(
                    " (" .. fieldValue_mode .. ")")
                newOffset = newOffset + 4
                ---10x
            end

            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                SKOZ_Min_Height_T_UINT16_8036bfe1_2496_42ef_bcc9_94024d95915b,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                SKOZ_Max_Height_T_UINT16_b51d428c_b6ed_4d95_92cd_c62d9ed81d96,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                Start_Azimuth_T_UINT16_6421117f_3ef8_4b63_8f89_10a82ea1988d,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                End_Azimuth_T_UINT16_b9b2ff4b_d30f_422d_910a_f957ece0d615,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                Radius_T_UINT16_7b78eff4_0cf2_4641_b6a9_262a34473cd1,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                Azimuth_T_UINT16_54c73401_1aca_4488_9c53_fc6e2eaee92f,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                Major_Radius_T_UINT16_b1d804ad_ffaa_48b5_a956_0b4374444c7f,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                Minor_Radius_T_UINT16_6b70bb9b_54db_46e5_ae4f_9214ab06f624,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            offset = newOffset
        end
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(Spare_T_UINT16_a96ccb41_39be_412e_801f_5d3df80e700f,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        return
    end
    if msg_type_val == 11 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset),
            "C2_COP_Engagement")
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(Track_ID_T_UINT16_11fe0ff6_32da_474e_8c24_1edd8184f028,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            CCU_Engagement_ID_T_UINT16_6caa7cf7_6ede_4349_a3fb_87d34db5d494,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 1):bitfield(7, 1)
        msg_subtree:add(
            Engagement_Priority_T_1_BIT_UINT8_70cdc9b2_33b5_437b_aa85_73d47946730a,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(5, 2)
        msg_subtree:add(
            Type_Of_ICP_T_2_BIT_UINT8_db39df11_e3e5_41ac_8058_32da1e2fa49e,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(4, 1)
        msg_subtree:add(
            Locking_Policy_T_1_BIT_UINT8_e9d65c05_7608_43fb_a582_3a4127e4fdb4,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 2)
        msg_subtree:add(
            Engagement_Request_T_2_BIT_UINT8_0354bfb4_34b3_4dcc_9591_f6ad0a7d4f09,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(1, 1)
        msg_subtree:add(
            Spare1_T_1_BIT_UINT8_9af521db_87f6_45b9_84a3_87587428561b,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 1)
        msg_subtree:add(
            Spare2_T_1_BIT_UINT8_9be6326a_ba97_46a5_8295_c7c733f142df,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(6, 2)
        msg_subtree:add(
            Plan_Status_T_2_BIT_UINT8_118c9b08_60ef_4fc1_9b5f_10cdedfdb8e8,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(3, 3)
        msg_subtree:add(
            Engagement_Status_T_3_BIT_UINT8_8385ada5_2dcb_4858_8564_4836980eff19,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 1)
        msg_subtree:add(
            Spare1_T_1_BIT_UINT8_c73e311a_14e3_40a4_b363_5517a8e267f4,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(1, 1)
        msg_subtree:add(
            Spare2_T_1_BIT_UINT8_bc2407e3_8f39_4842_a11c_c45b0fd8c3ab,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 1)
        msg_subtree:add(
            Spare3_T_1_BIT_UINT8_64d91827_f7c1_4504_a88b_61475d2c071c,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            Freq_Allocation_T_UINT8_f04bbe34_39fa_431e_9b21_17f28fa66e9f,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            Channel_Allocation_Index_T_UINT8_a9b7e2b6_69e6_41bd_9fea_8a56a2f9dcd0,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(7, 1)
        msg_subtree:add(
            Fire_Mode_T_1_BIT_UINT8_8bdf62d1_45f7_41ab_b805_5a0e1594df88,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 7)
        msg_subtree:add(
            wSpare_T_7_BIT_UINT8_d7562fed_bf78_46ff_a119_af7d01feb6d1,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            Fire_Control2_T_UINT8_5633dd03_258c_4f9b_b7ca_9e5f33d1cde6,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            Abort_Engagement_T_UINT8_84d75e19_ba48_475e_96d0_b4abbc38b854,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 4):le_uint()
        msg_subtree:add(CCU_Time_T_UINT32_dec03fdc_c194_41fe_9db6_90bfff3d2909,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_uint()
        msg_subtree:add(
            Magic_Word_T_UINT32_231f0105_50f9_41d5_90d9_bbdaa4d70368,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(Spare_T_UINT16_82e6aa77_9e6c_4698_8f75_a9e4873c9c7b,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            ICP_Plan_Exists_T_UINT8_f2f01884_1420_4c74_9f06_9aad8c72b10c,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        local Number_Of_Points_In_Traj = fieldValue
        msg_subtree:add(
            Number_Of_Points_In_Traj_T_UINT8_e9e9754f_9268_4137_9ee8_e3a7aa061295,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        for i = 0, Number_Of_Points_In_Traj - 1 do
            local msg_subtree_mode = msg_subtree:add(proto_cop,
                buffer(offset, 16),
                "ECEF_Position_And_Time[" ..
                (i + 1) .. "]")
            local newOffset = offset
            local fieldValue_mode = 0
            fieldValue_mode = buffer(newOffset, 4):le_int()
            msg_subtree_mode:add(
                X_Pos_T_SINT32_eeaf4845_fbc3_4b07_91ed_93d08d5b917a,
                buffer(newOffset, 4))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 4
            fieldValue_mode = buffer(newOffset, 4):le_int()
            msg_subtree_mode:add(
                Y_Pos_T_SINT32_a1a02a5b_9346_4f1b_92ff_f715cd74378d,
                buffer(newOffset, 4))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 4
            fieldValue_mode = buffer(newOffset, 4):le_int()
            msg_subtree_mode:add(
                Z_Pos_T_SINT32_83ee88ca_2e06_4927_ba12_f36a9fae9be7,
                buffer(newOffset, 4))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 4
            fieldValue_mode = buffer(newOffset, 4):le_uint()
            msg_subtree_mode:add(
                Rel_Time_T_UINT32_92922097_86c3_45d3_a383_8c2e83dc1fdc,
                buffer(newOffset, 4))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 4
            offset = newOffset
        end
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(PIP_Pos_X_T_SINT32_8b8c452c_504f_4c8c_8fa1_53411700911c,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(PIP_Pos_Y_T_SINT32_5474ff6e_bab6_4b82_9744_22e253344294,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(PIP_Pos_Z_T_SINT32_b2cbb2e6_7193_4d90_8e2d_71bf5d3c4bc3,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        return
    end
    if msg_type_val == 10 then
        local msg_subtree =
            subtree:add(proto_cop, buffer(offset), "C2_COP_Chat")
        fieldValue = buffer(offset, 1):bitfield(7, 1)
        msg_subtree:add(MFU1_T_1_BIT_UINT8_a3c7c1a7_73ef_4f31_8bad_7490f0a4af04,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(6, 1)
        msg_subtree:add(MFU2_T_1_BIT_UINT8_eea01408_1f74_4e97_bf64_c13ca64cd3ea,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(5, 1)
        msg_subtree:add(MFU3_T_1_BIT_UINT8_2e9836b1_bb25_4dc5_a9e9_b53ed0f3f73c,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(4, 1)
        msg_subtree:add(MFU4_T_1_BIT_UINT8_62e44ae0_5e1a_4cfb_8152_7ce38218760b,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(3, 1)
        msg_subtree:add(MFU5_T_1_BIT_UINT8_e01f00df_6c37_466d_9f9f_cfe46579416e,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 1)
        msg_subtree:add(MFU6_T_1_BIT_UINT8_5e5fd650_70e7_4adf_98aa_6ef06f7c3464,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 2)
        msg_subtree:add(
            Spare_T_2_BIT_UINT8_2d83fa0f_2ddb_42e5_ae4b_43695fb8a54f,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        local Text_Length = fieldValue
        msg_subtree:add(
            Text_Length_T_UINT8_37113846_cb79_4a6b_966d_5a61be5ab191,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, Text_Length):string()
        msg_subtree:add(Chat_Text_T_CHAR_e2f96c40_0fc6_495b_b53b_ee71057010e3,
            buffer(offset, Text_Length)):append_text(" (" ..
            fieldValue ..
            ")")
        offset = offset + Text_Length
        return
    end
    if msg_type_val == 12 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset),
            "C2_COP_Pointer")
        fieldValue = buffer(offset, 1):le_uint()
        local Text_Length = fieldValue
        msg_subtree:add(
            Text_Length_T_UINT8_62609524_b521_4e80_8f65_a080d4a9adc3,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, Text_Length):string()
        msg_subtree:add(Text_T_CHAR_fe4888b2_ba47_4345_a916_757fa6e4c6d6,
            buffer(offset, Text_Length)):append_text(" (" ..
            fieldValue ..
            ")")
        offset = offset + Text_Length
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Latitude_T_FLOAT32_5c9d5f03_dfc2_41a8_9107_a84149332eab,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(
            Longitude_T_FLOAT32_280114d1_733c_42fe_ad3e_decf3e4ad1bd,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        return
    end
    if msg_type_val == 7 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset),
            "C2_COP_Msls_Location_From_Rdr")
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            q_NumOfMslInAir_TU8_fd1f1675_6f51_467a_abc3_6e6aac7125e7,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(q_MslID_TU8_0705e044_c6fe_49f7_ac3b_369ac51b4409,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            q_Engagement1ID_TU16_002_22b00d7c_536f_425a_ba40_c10efbc68f91,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 8):le_float()
        msg_subtree:add(
            q_RdrTime_TReal64_001_72428c46_1c27_4d1e_9e05_9ff0371b5e08,
            buffer(offset, 8)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 8
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            q_RdrHorizontalFom_TU16_bcf0ff7c_02db_4955_818e_709444ce9dc1,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            q_RdrVerticalFom_TU16_fd2499a5_1685_49fe_8732_5d1d5c118f60,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            q_RdrAzimuthToMsl1_TU16_85cc4d11_72be_48f1_81f9_373c4a282960,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            q_RdrElevationToMsl1_TU16_a8a03b58_160a_4d87_810d_2b29bd7e6788,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            q_RdrRangeToMsl1_TU16_d3c335ea_1137_4b1d_8879_b0354d41e974,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            q_RdrMsl1Status_TU16_993d022b_54ef_4a82_8d01_34c512f0671d,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        return
    end
    if msg_type_val == 8 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset),
            "COP_C2_Status")
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            MCU_Serial_ID_T_UINT8_7a3dee28_cc01_4963_b72a_7637cf690cb2,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(6, 2)
        msg_subtree:add(
            Operability_T_2_BIT_UINT8_8f0ec644_2e0d_45e4_a569_96163fbf2440,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 4)
        msg_subtree:add(Mode_T_4_BIT_UINT8_f1a331a5_45e6_4d4c_bf85_5d5b3836533b,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(1, 1)
        msg_subtree:add(Type_T_1_BIT_UINT8_3ef3b326_8c14_4044_9a27_a6b7cc6ef570,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 1)
        msg_subtree:add(
            Spare_T_1_BIT_UINT8_04831496_c06d_4983_8db4_015ee2e3f73f,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(5, 3)
        msg_subtree:add(
            MCU_State_T_3_BIT_UINT8_5ca2eb9a_fcac_4499_af16_b1f2bcf68300,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(3, 2)
        msg_subtree:add(
            LM_Readiness_To_Engage_T_2_BIT_UINT8_9f19e764_62b7_411d_979c_d44c842d4779,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 1)
        msg_subtree:add(
            LM_Armed_T_1_BIT_UINT8_f58853bb_6101_4277_9a58_9cdb9a1e8750,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(1, 1)
        msg_subtree:add(
            MCU_Abort_Button_Pressed_T_1_BIT_UINT8_a59d2671_ad0e_4ace_b730_7e5f82739191,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 1)
        msg_subtree:add(
            Spare2_T_1_BIT_UINT8_809f729f_5262_4eb9_a4c0_29b626b0e8e3,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(7, 1)
        msg_subtree:add(
            Not_In_Immediate_Status_T_1_BIT_UINT16_4a355c80_6097_4d53_863c_ae90f4607b1b,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(6, 1)
        msg_subtree:add(
            Conflict_In_The_Fire_Source_T_1_BIT_UINT16_79ce371b_a418_48a6_9fcc_a9a5fac9c770,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(5, 1)
        msg_subtree:add(
            No_Missile_Available_T_1_BIT_UINT16_7f0dc95e_7526_4592_8104_9dc2480fcea6,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(4, 1)
        msg_subtree:add(
            Uplink_Activated_T_1_BIT_UINT16_8df89a14_b8dc_4ba3_a122_aad21091404f,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(3, 1)
        msg_subtree:add(
            Launch_Fail_T_1_BIT_UINT16_032f85a2_f2d3_4fb6_9388_f53eb00758c7,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 1)
        msg_subtree:add(
            MCU_Failure_T_1_BIT_UINT16_25c4d462_f109_4023_8d65_4d64922cd1a5,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(1, 1)
        msg_subtree:add(
            LCU_Failure_T_1_BIT_UINT16_0a422ea4_6da8_466d_be10_5c904de3158a,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 1)
        msg_subtree:add(
            MDCU_Failure_T_1_BIT_UINT16_a37b2c3f_a5d2_4087_af33_fadb4b703fa5,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(7, 1)
        msg_subtree:add(
            BNET_Failure_And_LOAL_T_1_BIT_UINT16_b3ae9f56_ae99_426a_9228_374528b12b71,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(6, 1)
        msg_subtree:add(
            INS_Failure_T_1_BIT_UINT16_da0e381a_f810_4ce5_a2e8_a586de26503c,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(5, 1)
        msg_subtree:add(
            GPS_Failure_T_1_BIT_UINT16_c2d5e81a_c750_467d_ac6a_6d5eedde2e85,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(4, 1)
        msg_subtree:add(
            Time_Validity_Failure_T_1_BIT_UINT16_d071aee3_acae_42c3_990e_647eccfd4d9d,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(3, 1)
        msg_subtree:add(
            Pwr_Supply_Failure_T_1_BIT_UINT16_7730ca4b_ef02_456b_84bd_677ff32644a6,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 1)
        msg_subtree:add(
            MCU_disconnected_T_1_BIT_UINT16_453f4827_4166_442e_a2bd_9db2cdd043df,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(1, 1)
        msg_subtree:add(
            Turret_In_No_Launch_Sector_T_1_BIT_UINT16_b0af6ead_e494_40c8_aa6d_07fd32abbfe8,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 1)
        msg_subtree:add(
            Spare3_T_1_BIT_UINT16_4675fbd1_d679_44c6_aed5_fe7a4d40644d,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(7, 1)
        msg_subtree:add(
            COP_Client_Disconnected_T_1_BIT_UINT16_536efe2a_3b0a_448e_b1be_31f57d9d932a,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(6, 1)
        msg_subtree:add(
            COP_Server_Disconnected_T_1_BIT_UINT16_b744bef3_164d_40f7_8531_34695e678349,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(5, 1)
        msg_subtree:add(
            CCU_Server_Disconnected_T_1_BIT_UINT16_17ed85a9_6107_4850_a64a_93e10761964a,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(4, 1)
        msg_subtree:add(
            COP_CCU_Desync_T_1_BIT_UINT16_2cccd484_b5dd_4aad_9942_332fab68f51d,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(3, 1)
        msg_subtree:add(
            COP_MCU_Desync_T_1_BIT_UINT16_07f19728_8baa_495a_8449_b9f255e6e89e,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 1)
        msg_subtree:add(
            DLQ_Smaller_Than_Threashold_T_1_BIT_UINT16_9b8a9c2b_87c7_49ef_9ce4_6b7952e7bf82,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(1, 1)
        msg_subtree:add(
            COP_Client_Faulty_T_1_BIT_UINT16_6aea280b_43a8_43a5_98e8_555b95f232b3,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 1)
        msg_subtree:add(
            COP_Server_Faulty_T_1_BIT_UINT16_6704a723_0520_44f3_92f1_8da9b7dd47e6,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(7, 1)
        msg_subtree:add(
            CCU_Server_Faulty_T_1_BIT_UINT16_7a3138ac_58e1_4430_8488_6aaaca0e7eef,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(6, 1)
        msg_subtree:add(
            DTM_Comp_T_1_BIT_UINT16_0315f382_1898_4396_99bd_6bcefe426234,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(5, 1)
        msg_subtree:add(
            SICS_Faulty_T_1_BIT_UINT16_a93b4214_4d00_461b_b9a5_872ad4c264b5,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(4, 1)
        msg_subtree:add(
            Toplite_Faulty_T_1_BIT_UINT16_41badab3_c512_451a_904e_7934c443dded,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(3, 1)
        msg_subtree:add(
            Spare4_T_1_BIT_UINT16_648b2ef4_7aa8_4615_91bd_0ff05c3ee5f8,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 1)
        msg_subtree:add(
            Spare5_T_1_BIT_UINT16_38ce9280_ba32_4ad8_8e3a_db38872ac141,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(1, 1)
        msg_subtree:add(
            Spare6_T_1_BIT_UINT16_9bfb9f5b_c2de_4da3_8b52_2d5f330bf17f,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 1)
        msg_subtree:add(
            Spare7_T_1_BIT_UINT16_ce611c65_5098_4e9e_9099_7e46c2249770,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            Network_Size_T_UINT8_175879c1_f8f3_41a0_9a9c_403bad1070f5,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(4, 4)
        msg_subtree:add(objP_T_4_BIT_UINT8_76a5044d_1da7_480b_9764_4ab082483c8f,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 4)
        msg_subtree:add(
            Derby_T_4_BIT_UINT8_e59be195_4a49_44db_8123_9c9498f14a72,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(4, 4)
        msg_subtree:add(
            ER_Derby_T_4_BIT_UINT8_d1e609d8_23d4_4287_a50c_109336f72a25,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 4)
        msg_subtree:add(
            LR_Derby_T_4_BIT_UINT8_2a43448e_a83a_48c7_ba96_5e7215e34183,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(4, 4)
        msg_subtree:add(objP_T_4_BIT_UINT8_1cac2eb5_774f_4b20_b932_3d9732f4bfc1,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 4)
        msg_subtree:add(
            Derby_T_4_BIT_UINT8_38971f62_9323_43aa_92c1_42c5c3146324,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(4, 4)
        msg_subtree:add(
            ER_Derby_T_4_BIT_UINT8_0f7d8506_324e_498e_8ac9_cc184209c021,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 4)
        msg_subtree:add(
            LR_Derby_T_4_BIT_UINT8_d14653ee_6a51_46c3_96bd_d818f66f1b68,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            Turret_Azimuth_T_UINT16_dbdcaed5_c24a_4e7d_a4f1_8493835fc680,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 4):le_uint()
        msg_subtree:add(
            Cabin_Azimuth_T_UINT32_7419cc7b_46dd_408b_a11c_355f388e28df,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Latitude_T_FLOAT32_3e4bba20_c47e_4474_8dfc_50cd33f31c8c,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(
            Longitude_T_FLOAT32_80b0933d_82df_45f8_8d36_6e7a3a252da4,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Altitude_T_FLOAT32_1c4df4f1_6d94_4863_88a0_31e8b8f68e07,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 1):le_uint()
        local Number_Of_Sectors = fieldValue
        msg_subtree:add(
            Number_Of_Sectors_T_UINT8_0b00ea4b_76bb_4c69_8296_21054c081d6b,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        for i = 0, Number_Of_Sectors - 1 do
            local msg_subtree_mode = msg_subtree:add(proto_cop,
                buffer(offset, 4),
                "No launch sector[" ..
                (i + 1) .. "]")
            local newOffset = offset
            local fieldValue_mode = 0
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                Start_Azimuth_T_UINT16_12d0a5dd_a23d_451e_ac42_da8321265c43,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                Sector_Size_T_UINT16_eff07ec7_621d_4c42_94b1_d51905c80094,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            offset = newOffset
        end
        local mechanical_limit_node = msg_subtree:add(proto_cop,
            buffer(offset, 4),
            "Mechanical limit")
        fieldValue = buffer(offset, 2):le_uint()
        mechanical_limit_node:add(
            Start_Azimuth_T_UINT16_12d0a5dd_a23d_451e_ac42_da8321265c43,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        mechanical_limit_node:add(
            Sector_Size_T_UINT16_eff07ec7_621d_4c42_94b1_d51905c80094,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 1):bitfield(6, 2)
        msg_subtree:add(MCU_T_2_BIT_UINT32_caf15730_e070_4029_bb8d_57c29393eb31,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(4, 2)
        msg_subtree:add(INS_T_2_BIT_UINT32_6f079d30_a488_43da_9d5c_a7c87be7305c,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 2)
        msg_subtree:add(GPS_T_2_BIT_UINT32_f56bcf78_a874_4d44_9fb3_e2741c20201a,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 2)
        msg_subtree:add(
            MDCU_T_2_BIT_UINT32_56063872_6515_401a_87da_58d32fdebce7,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(6, 2)
        msg_subtree:add(
            LCU1_T_2_BIT_UINT32_a96fe027_0216_440f_a2f6_22a6460b6c5d,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(4, 2)
        msg_subtree:add(
            LCU2_T_2_BIT_UINT32_04b8dc04_b14d_4b4f_8df7_c1caeef39317,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 2)
        msg_subtree:add(
            BNET_T_2_BIT_UINT32_7c9a2ef9_52ee_4a07_b073_7e948a2f5653,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 2)
        msg_subtree:add(
            Time_Validity_T_2_BIT_UINT32_d70e3537_70d0_49a2_be66_8785e7405a37,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(6, 2)
        msg_subtree:add(
            mDRS_T_2_BIT_UINT32_ce745225_84f9_4644_bfde_1ac6b1ddff3f,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(4, 2)
        msg_subtree:add(
            TopLite_T_2_BIT_UINT32_01b86a96_9ce6_4958_8837_83413a5b533a,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 2)
        msg_subtree:add(
            Rubidium_T_2_BIT_UINT32_54951abc_fcb6_4f3c_875e_e958fe285cfa,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 2)
        msg_subtree:add(
            Spare3_T_2_BIT_UINT32_62c66e30_bce3_4562_8cb6_8d51da9cefcd,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(6, 2)
        msg_subtree:add(
            Spare4_T_2_BIT_UINT32_d0c0793b_ebcb_4ffc_a67b_9437c97609fc,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(4, 2)
        msg_subtree:add(
            Spare5_T_2_BIT_UINT32_ecd4f668_b0de_4567_b829_b78d598d2f69,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(2, 2)
        msg_subtree:add(
            Spare6_T_2_BIT_UINT32_697f69d4_09f5_4a79_9153_195d92fd1098,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 2)
        msg_subtree:add(
            Spare7_T_2_BIT_UINT32_884245f3_6c4a_4c2a_9ee9_d2f7c840edc8,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(7, 1)
        msg_subtree:add(
            Data_Source_T_1_BIT_UINT8_a2c4ee7a_6c45_4235_be85_ac88d09255dd,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 7)
        msg_subtree:add(
            DLQ_Value_T_7_BIT_UINT8_c93a8896_b2c5_4c7a_b3d7_5851824ef00f,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(MCU_T_UINT16_0b0a8911_07cd_4666_b28f_b45fed74f1b1,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(BNET_T_UINT16_0f95abc8_7178_4f3f_ac54_c77cd1d218ab,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(INS_T_UINT16_22508071_942d_4627_b2f6_a193367373b1,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(MDCU_T_UINT16_12dbb7dc_1da9_4141_a401_a7f59875ffd2,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(LCU_T_UINT16_61dcdb3c_3fe2_4f0a_b99a_2e55eb01ce72,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            COP_Server_Version_T_UINT16_6d8f95d9_8274_41be_aac6_8b0119c42f53,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            COP_Client_Version_T_UINT16_7ca443a5_91a7_4843_804a_eb5a41612978,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            ICS_DTM_Version_T_UINT16_d2f4751f_a4a9_4e5a_9ba8_cdbabe86705d,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 1):le_uint()
        local Chat_Text_Length = fieldValue
        msg_subtree:add(
            Chat_Text_Length_T_UINT8_75275dbe_6d13_4310_bec4_1b9d920c61f0,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, Chat_Text_Length):string()
        msg_subtree:add(Chat_Text_T_CHAR_55b71ec7_3c64_46eb_806a_ca122f537aa6,
            buffer(offset, Chat_Text_Length)):append_text(" (" ..
            fieldValue ..
            ")")
        offset = offset + Chat_Text_Length
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            Toplite_Azimuth_T_UINT16_3b8b8563_e5f2_42f7_85a0_819b6543dad5,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            Toplite_Elevation_T_UINT16_44083aad_4cd5_4b4c_8644_50cd1edec391,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 4):le_uint()
        msg_subtree:add(
            Toplite_Target_Range_T_UINT32_be358cf7_ba6e_4d7f_bf59_fa79ec904abe,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 1):bitfield(6, 2)
        msg_subtree:add(
            COPTopliteEngagementIndication_T_2_BIT_UINT8_5f91e315_02d4_47f9_91cf_60df46fdff7d,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 6)
        msg_subtree:add(
            Spare_T_6_BIT_UINT8_b962dcd7_18d9_45f9_8caa_7a3f89ae7b1c,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        return
    end
    if msg_type_val == 129 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset),
            "COP_C2_Engagement_Status")
        fieldValue = buffer(offset, 1):le_uint()
        local Number_Of_Engagements = fieldValue
        msg_subtree:add(
            Number_Of_Engagements_T_UINT8_4f8de101_74bd_48c6_a33f_c34afa14bf83,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        for i = 0, Number_Of_Engagements - 1 do
            local msg_subtree_mode = msg_subtree:add(proto_cop,
                buffer(offset, 16),
                "COP_Engagement_Status[" ..
                (i + 1) .. "]")
            local newOffset = offset
            local fieldValue_mode = 0
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                Track_ID_T_UINT16_fcd5d2c6_cdaa_49c1_ada9_6e0f48ea1c9b,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                CCU_Engagement_ID_T_UINT16_08829c96_1c3a_4f5f_b1e6_8273eeb2090a,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                COP_Engagement_ID_T_UINT16_508c88cf_4f0c_489b_86dd_465bd4f232eb,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 1):bitfield(5, 3)
            msg_subtree_mode:add(
                MCU_Engagement_Handling_T_3_BIT_UINT8_cbfc1b00_acd3_4d65_9dab_6b9d5af6fc45,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(3, 2)
            msg_subtree_mode:add(
                MCU_Engagement_Status_T_2_BIT_UINT8_b8036856_9bcb_480a_8e24_67c5f91e3fc9,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(2, 1)
            msg_subtree_mode:add(
                Spare_T_1_BIT_UINT8_af648298_7501_467f_8ffa_efcac15fb17f,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(0, 2)
            msg_subtree_mode:add(
                Engagement_Abort_Source_T_2_BIT_UINT8_73bf31a8_8bbe_456d_888e_1281176f8dcb,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 1):bitfield(6, 2)
            msg_subtree_mode:add(
                ICP_Allocated_T_2_BIT_UINT8_6dde0994_689b_43fb_8371_3982be1fd4c3,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(5, 1)
            msg_subtree_mode:add(
                objP_ICP_Lock_T_1_BIT_UINT8_8585583a_1a14_4bfb_ab97_6991ab763abd,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(4, 1)
            msg_subtree_mode:add(
                Derby_ICP_Lock_T_1_BIT_UINT8_8617f769_e03b_4dee_ae83_f4dcc83a9a28,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(3, 1)
            msg_subtree_mode:add(
                ER_Derby_ICP_Lock_T_1_BIT_UINT8_f6c09481_eaba_44f5_8161_b53dd93d9e3f,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(2, 1)
            msg_subtree_mode:add(
                LR_Derby_ICP_Lock_T_1_BIT_UINT8_fa7d9d08_1449_494c_95ba_29fc6c229f66,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(1, 1)
            msg_subtree_mode:add(
                ICP_Stage_T_1_BIT_UINT8_f5fc65a6_b3df_4f8d_b991_b7628f8ada58,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(0, 1)
            msg_subtree_mode:add(
                Lock_Type_T_1_BIT_UINT8_bbf19f87_340b_45af_845e_953a8528d084,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 1):bitfield(7, 1)
            msg_subtree_mode:add(
                Misfire_Indication_T_1_BIT_UINT8_3c6348db_5929_4c23_953d_982b441258c9,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(5, 2)
            msg_subtree_mode:add(
                Misfire_Reason_T_2_BIT_UINT8_fecf3e6b_a781_45a5_a121_a26893a6f5ad,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(0, 5)
            msg_subtree_mode:add(
                spare_T_5_BIT_UINT8_81879ce0_69f8_4726_9683_cc364c591aee,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            fieldValue_mode = buffer(newOffset, 4):le_uint()
            msg_subtree_mode:add(
                Engagement_Launch_Time_T_UINT32_fd534a87_ae89_49e3_b23f_6680c6acb774,
                buffer(newOffset, 4))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 4
            fieldValue_mode = buffer(newOffset, 2):le_uint()
            msg_subtree_mode:add(
                Turret_Launching_Azimuth_T_UINT16_ac186391_2e73_4de7_83e8_985cc84c6251,
                buffer(newOffset, 2))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 2
            fieldValue_mode = buffer(newOffset, 1):bitfield(7, 1)
            msg_subtree_mode:add(
                Seeker_Mode_T_1_BIT_UINT8_58d5e937_3acf_48ae_924a_f7835b9d481a,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(6, 1)
            msg_subtree_mode:add(
                SPare1_T_1_BIT_UINT8_ea828b75_d6c2_442c_9d35_c9ba15bd0a9c,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(5, 1)
            msg_subtree_mode:add(
                Spare2_T_1_BIT_UINT8_7698d824_3711_455d_9aab_4a02dad53d2d,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(4, 1)
            msg_subtree_mode:add(
                Spare3_T_1_BIT_UINT8_7ee8cecf_e007_4860_ad08_c4a8d216b23a,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(2, 2)
            msg_subtree_mode:add(
                Spare4_T_2_BIT_UINT8_05454b7a_52da_4704_83c1_93338d49629c,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            fieldValue_mode = buffer(newOffset, 1):bitfield(0, 2)
            msg_subtree_mode:add(
                Spare5_T_2_BIT_UINT8_a43ee928_d428_4381_a6c1_7d1ec39e598f,
                buffer(newOffset, 1))
                :append_text(" (" .. fieldValue_mode .. ")")
            newOffset = newOffset + 1
            offset = newOffset
        end
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(Spare2_T_UINT8_2375aa06_09c2_4d70_b8e8_98452c5b67ea,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        return
    end
    if msg_type_val == 133 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset), "COP_C2_ACK")
        fieldValue = buffer(offset, 4):le_uint()
        msg_subtree:add(
            Sequence_num_T_UINT32_ea2981e9_13e4_49a0_991f_c32e2949b949,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        return
    end
    if msg_type_val == 140 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset),
            "COP_C2_Pointer")
        fieldValue = buffer(offset, 1):le_uint()
        local Text_Length = fieldValue
        msg_subtree:add(
            Text_Length_T_UINT8_0e6a3891_91cb_4063_82c1_36518c5f33a2,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, Text_Length):string()
        msg_subtree:add(Text_T_CHAR_c3386610_a74f_4209_aecd_b6233480b08b,
            buffer(offset, Text_Length)):append_text(" (" ..
            fieldValue ..
            ")")
        offset = offset + Text_Length
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(Latitude_T_FLOAT32_ba3a03d1_69b4_467c_8fb0_4209562c34ab,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_float()
        msg_subtree:add(
            Longitude_T_FLOAT32_7a23e00b_e3d7_440b_8ad8_41447742edfe,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        return
    end
    if msg_type_val == 134 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset),
            "COP_C2_Msl_DL")
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            q_NumOfMslInAir_TU8_adfe5860_f7f4_4798_b916_8dd2d853d30a,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(q_MslID_T_UINT8_292715fb_4b48_4e51_9d40_ed026488aa91,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            q_NavTimeTag_TU16_002_c0097ec7_7600_4a85_a878_87fa333d8af0,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_int()
        msg_subtree:add(q_MslXL0_TS16_001_44f181dc_7682_4eb1_87ad_c0000b3d3ba1,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_int()
        msg_subtree:add(q_MslYL0_TS16_001_19262d37_4a60_430f_ad6b_dcdcbeccc6fa,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_int()
        msg_subtree:add(q_MslZL0_TS16_001_97f7d429_ea86_4d81_ad14_5775df755beb,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_int()
        msg_subtree:add(q_MslVxL0_TS16_001_b3213180_81f3_4095_96d3_cc96b7af9cf8,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_int()
        msg_subtree:add(q_MslVyL0_TS16_001_0836e383_381d_44d4_8499_1c19fd0664d9,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_int()
        msg_subtree:add(q_MslVzL0_TS16_001_a3ffcc3a_999d_4f13_b4f1_2ff3861704a5,
            buffer(offset, 2))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_int()
        msg_subtree:add(
            q_EulerAlpha_TS16_001_60d39747_bea2_4054_9b7c_d08a16b5bace,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_int()
        msg_subtree:add(
            q_EulerBeta_TS16_001_d1a94e4c_e0ff_4e9c_b18d_e3cd56dc1bf8,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_int()
        msg_subtree:add(
            q_EulerGama_TS16_001_768e354c_b9c0_4e71_8c3e_95d27f6a0203,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 3):le_uint()
        msg_subtree:add(
            MslNavPosUncertainty_TU8_cb49c6a7_afef_499d_91c9_2ae9a08ff0a5,
            buffer(offset, 3)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 3
        fieldValue = buffer(offset, 3):le_uint()
        msg_subtree:add(
            MslNavVelUncertainty_TU8_b1e54f26_1053_4aa7_bf23_97a41e657a4a,
            buffer(offset, 3)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 3
        fieldValue = buffer(offset, 2):le_int()
        msg_subtree:add(
            q_RangeMslTarget_TS16_a32fa25a_baa6_4910_8950_64a155cf8104,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(q_Tgo_TU8_5ef3d2cb_48d8_4f05_b33a_8a8262464b05,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            q_TtoActivateSeeker_TU8_1c8d6b99_17ac_4096_97f4_5082f44ae623,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(q_GuidanceMode_TU8_b4178bc2_23aa_406b_acd8_4605d2066163,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(6, 2)
        msg_subtree:add(
            q_Spare2Bits_TU16_2_001_da2afb7c_83af_4205_a505_ab5f9bc3c3e8,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(4, 2)
        msg_subtree:add(q_Abort_TU16_2_001_ff638045_4260_4e1a_97f3_b01bfa34eeea,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 4)
        msg_subtree:add(
            q_PfStatus_TU16_4_001_2de6e39b_c651_4a7b_bc33_4c1f71cf2296,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(4, 4)
        msg_subtree:add(
            q_Pointingstatus_TU16_4_001_edfd44dc_7c13_4e2d_a2be_38ab77231d58,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 4)
        msg_subtree:add(
            q_PropulsionStage_TU16_4_001_5c16d5d7_c7fc_484d_af75_9455530fd1b1,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(4, 4)
        msg_subtree:add(
            q_UpLinkStatus_TU8_4_001_7f6df6bc_73ce_41d6_8e15_0e6e360e105f,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 4)
        msg_subtree:add(
            q_SeekerState_TU8_4_001_ef6da7bc_0f67_43ff_927e_0ba66a00c86c,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(q_GimbalTheta_TU8_2d8ce243_aeb4_420f_a203_8b3739fb9005,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(q_GimbalPsi_TU8_5cf3fd22_2264_4ff8_a911_9f97b81ce4d8,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(q_Range_TU8_0b84bbf2_4703_462e_9e18_543f1b74644f,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(q_RDOT_TU8_d6097dd2_9f94_4efe_88da_041b9cd6053e,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(4, 4)
        msg_subtree:add(
            q_EcmStatus_TU8_4_001_517dd728_5a87_4146_914a_1ee45917cda2,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 4)
        msg_subtree:add(q_Spare_TU8_4_001_b817ca5f_e633_43b3_9b8b_806a4b610ac3,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(q_ImuStatus_TU8_6700d067_ce31_45bc_b4af_7c4c866a0c6f,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(q_SeekerStatus_TU8_6fa54310_e49c_4365_956e_c182a6f76472,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(4, 4)
        msg_subtree:add(
            q_EsadStatus_TU8_4_001_35c474a3_b55e_4870_8cb4_0e1d487435a8,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 4)
        msg_subtree:add(
            q_EIsadStatus_TU8_4_001_47e06d9e_9b4b_4fbf_84a6_7113bba1c63c,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(q_VesselPres_TU8_41828f4f_c33a_454a_861a_27b68a56047a,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 4):le_uint()
        msg_subtree:add(q_MslData1_TU32_184f33df_fc78_472b_bf6f_27145bae347c,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_uint()
        msg_subtree:add(q_MslData2_TU32_8ad05827_c87f_4df2_8f3a_1947255cd2d9,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_uint()
        msg_subtree:add(q_MslData3_TU32_3dc239bb_ebbf_4faa_bcc0_1489ae9887db,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_uint()
        msg_subtree:add(q_MslData4_TU32_67a32579_a6cd_4271_ae43_db6bbacaf44b,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_uint()
        msg_subtree:add(q_MslData5_TU32_b1a40e00_61e1_490b_8d08_a7662c5f6c39,
            buffer(offset, 4))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        return
    end
    if msg_type_val == 135 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset),
            "COP_C2_Msl_Metry")
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(q_MslID_TU8_af7d7c03_e9cb_4565_a933_f63e05203bdc,
            buffer(offset, 1))
            :append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1000):le_uint()
        msg_subtree:add(
            q_MissleDataBuffer_TU8_fcde7e09_8e65_4a68_896a_75619553c864,
            buffer(offset, 1000)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1000
        return
    end
    if msg_type_val == 136 then
        local msg_subtree = subtree:add(proto_cop, buffer(offset),
            "COP_C2_Toplite_Track")
        fieldValue = buffer(offset, 4):le_uint()
        msg_subtree:add(
            TargetUpdateTime_TU32_6b1820c3_626a_4184_a20e_623e6851610c,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(
            TargetPositionNorth_T_SINT32_670a12e9_99f4_4d8c_9505_52b752e6f231,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(
            TargetPositionEast_T_SINT32_201eda6c_dadf_4511_a154_7953127a1c8a,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(
            TargetPositionDown_T_SINT32_1ad667e5_2036_4602_9be0_cdb33b577630,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(
            TargetVelocityNorth_T_SINT32_81fc3cba_4ea9_4278_82c0_e66f669c386c,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(
            TargetVelocityEast_T_SINT32_244abd48_fb4f_438f_bfc3_f9cad677cff5,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 4):le_int()
        msg_subtree:add(
            TargetVelocityDown_T_SINT32_0cbec0b7_45d6_460f_b844_456379d6a46b,
            buffer(offset, 4)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 4
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            AutonomousTargetID_T_UINT16_283c3389_5761_4e3a_a3d5_d8adf731dc8d,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 2):le_uint()
        msg_subtree:add(
            AutonomousEngagementID_T_UINT16_3e1f17fa_4e08_488e_b678_328c5f2e1144,
            buffer(offset, 2)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 2
        fieldValue = buffer(offset, 1):le_uint()
        msg_subtree:add(
            TopliteTargetType_TU8_35f3e5ce_8052_4e06_872a_0e1533d20d06,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        fieldValue = buffer(offset, 1):bitfield(7, 1)
        msg_subtree:add(
            Validity_T_1_BIT_UINT8_a73aa0b0_8e80_4725_a0f2_f9d7ed5ba7f6,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        fieldValue = buffer(offset, 1):bitfield(0, 7)
        msg_subtree:add(
            spare_T_7_BIT_UINT8_acdd4034_48b5_4ce6_b505_d2f8c9a3c181,
            buffer(offset, 1)):append_text(" (" .. fieldValue .. ")")
        offset = offset + 1
        return
    end
end

local udp_port = DissectorTable.get("udp.port")
udp_port:add(14000, proto_cop)
