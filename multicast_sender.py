import socket
import struct
import time
import sys

# Use the same multicast group and port as in your receiver
MULTICAST_GROUP = "*********"
MULTICAST_PORT = 11111
TTL = 2  # Time-to-live (number of network hops)

# For same PC communication
interface_ip = '127.0.0.1'

# Create the socket
sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM, socket.IPPROTO_UDP)

# Set the time-to-live for messages
sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_TTL, TTL)

# Enable multicast loopback so packets sent from this machine can be received on this machine
sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_LOOP, 1)

# Bind the socket to a specific interface
sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_IF, socket.inet_aton(interface_ip))

print(f"Multicast sender started. Sending to {MULTICAST_GROUP}:{MULTICAST_PORT}")

try:
    message_count = 0
    while True:
        message = f"Multicast message #{message_count} from sender"
        sock.sendto(message.encode('utf-8'), (MULTICAST_GROUP, MULTICAST_PORT))
        print(f"Sent: {message}")
        message_count += 1
        time.sleep(1)  # Send a message every second
except KeyboardInterrupt:
    print("\nExiting sender.")
finally:
    sock.close()