import socket
import struct
import time

# Use the same multicast group and port as in your sender
MULTICAST_GROUP = "*********"
MULTICAST_PORT = 10000
BUFFER_SIZE = 200

# For same PC communication
interface_ip = '**************'

# Create the socket
sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM, socket.IPPROTO_UDP)

# Allow multiple sockets to bind to the same address and port
sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEPORT, 1)

# Enable multicast loopback so packets sent from this machine can be received on this machine
sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_LOOP, 1)

# Bind to the multicast port on all interfaces
sock.bind(('', MULTICAST_PORT))

# Join the multicast group on the specified interface
mreq = struct.pack('=4s4s', socket.inet_aton(MULTICAST_GROUP), socket.inet_aton(interface_ip))
# mreq = struct.pack('=4sl', socket.inet_aton(MULTICAST_GROUP), socket.INADDR_ANY)
sock.setsockopt(socket.IPPROTO_IP, socket.IP_ADD_MEMBERSHIP, mreq)

print(f"Joined multicast group {MULTICAST_GROUP} on port {MULTICAST_PORT}")
sock.settimeout(2)  # Shorter timeout for more responsive feedback

try:
    # Receive data
    while True:
        try:
            data, addr = sock.recvfrom(BUFFER_SIZE)
            print(f"Received from {addr}: {data.decode('utf-8')}")
        except socket.timeout:
            print("Waiting for data...")
            continue
except KeyboardInterrupt:
    print("\nExiting receiver.")
finally:
    # Leave the multicast group and close the socket
    mreq = struct.pack('=4s4s', socket.inet_aton(MULTICAST_GROUP), socket.inet_aton(interface_ip))
    # mreq = struct.pack('=4sl', socket.inet_aton(MULTICAST_GROUP), socket.INADDR_ANY)
    sock.setsockopt(socket.IPPROTO_IP, socket.IP_DROP_MEMBERSHIP, mreq)
    sock.close()
