from spyder_cop.cop_data.headergen import HeaderGen
from spyder_cop.messages.class_C2_COP_ASP_Track import ASPTrack, C2COPASPTrack
from spyder_cop.messages.class_header import Header
from spyder_cop.messages.util import (
    EASPTargetIdentity,
    EASPTargetType,
    EASPThreatLevel,
    EASPTrackingStatus,
)


class TestC2COPASPTrack:
    def test_pack_unpack_empty_tracks(self):
        # Arrange
        message = C2COPASPTrack(live_track=True)

        # Act
        packed = message.pack()
        unpacked = C2COPASPTrack.unpack(packed)

        # Assert
        assert unpacked.live_track is True
        assert unpacked.number_of_tracks == 0
        assert len(unpacked.tracks) == 0

    def test_pack_unpack_single_track(self):
        # Arrange
        message = C2COPASPTrack(live_track=False)
        track = ASPTrack(
            track_id=1,
            fcr_track_id=2,
            tra_upd_time=123.456,
            target_type=EASPTargetType.FIGHTER,
            target_identity=EASPTargetIdentity.FRIEND,
            thread_level=EASPThreatLevel.NO_THREAT,
            track_status=EASPTrackingStatus.TRACKED,
            tn_str="12345",
        )
        message.add_track(track)

        # Act
        packed = message.pack()
        unpacked = C2COPASPTrack.unpack(packed)

        # Assert
        assert unpacked.live_track is False
        assert unpacked.number_of_tracks == 1
        assert len(unpacked.tracks) == 1

        unpacked_track = unpacked.tracks[0]
        assert unpacked_track.track_id == 1
        assert unpacked_track.fcr_track_id == 2
        assert round(unpacked_track.tra_upd_time, 3) == round(123.456, 3)
        assert unpacked_track.target_type == EASPTargetType.FIGHTER
        assert unpacked_track.target_identity == EASPTargetIdentity.FRIEND
        assert unpacked_track.thread_level == EASPThreatLevel.NO_THREAT
        assert unpacked_track.track_status == EASPTrackingStatus.TRACKED
        assert unpacked_track.tn_str == "12345"

    def test_pack_unpack_multiple_tracks(self):
        # Arrange
        message = C2COPASPTrack(live_track=True)

        # Add three tracks with different values
        tracks = [
            ASPTrack(
                1,
                10,
                100.0,
                EASPTargetType.FIGHTER,
                EASPTargetIdentity.FRIEND,
                EASPThreatLevel.NO_THREAT,
                EASPTrackingStatus.TRACKED,
                "11111",
            ),
            ASPTrack(
                2,
                20,
                200.0,
                EASPTargetType.COMMERCIAL_JET,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.NO_THREAT,
                EASPTrackingStatus.TRACKED,
                "22222",
            ),
            ASPTrack(
                3,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                4,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                5,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                6,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                7,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                8,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                9,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                10,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                11,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                12,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                13,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                14,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                15,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                16,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                17,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                18,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                19,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                20,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                21,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                22,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                23,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                24,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                25,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                26,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                27,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                28,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                29,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                30,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                31,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
            ASPTrack(
                32,
                30,
                300.0,
                EASPTargetType.UNKNOWN,
                EASPTargetIdentity.PENDING,
                EASPThreatLevel.SIX,
                EASPTrackingStatus.TRACKED,
                "33333",
            ),
        ]

        for track in tracks:
            message.add_track(track)

        # Act
        packed = message.pack()
        packet_with_header = HeaderGen().get_header_msg(
            C2COPASPTrack.get_msg_type(), packed, 1
        )
        _header = Header.unpack(packet_with_header[: Header.SIZE])
        next_data = packet_with_header[Header.SIZE :]
        assert next_data == packed
        unpacked = C2COPASPTrack.unpack(next_data)

        # Assert
        assert unpacked.live_track is True
        assert unpacked.number_of_tracks == len(tracks)

        for original, unpacked_track in zip(tracks, unpacked.tracks):
            assert unpacked_track.track_id == original.track_id
            assert unpacked_track.fcr_track_id == original.fcr_track_id
            assert round(unpacked_track.tra_upd_time, 3) == round(
                original.tra_upd_time, 3
            )
            assert unpacked_track.target_type == original.target_type
            assert unpacked_track.target_identity == original.target_identity
            assert unpacked_track.thread_level == original.thread_level
            assert unpacked_track.track_status == original.track_status
            assert unpacked_track.tn_str == original.tn_str

    def test_duplicate_track_not_added(self):
        # Arrange
        message = C2COPASPTrack(live_track=True)
        track = ASPTrack(
            1,
            10,
            100.0,
            EASPTargetType.FIGHTER,
            EASPTargetIdentity.FRIEND,
            EASPThreatLevel.NO_THREAT,
            EASPTrackingStatus.TRACKED,
            "11111",
        )

        # Act
        message.add_track(track)
        message.add_track(track)  # Try to add the same track again

        # Assert
        assert message.number_of_tracks == 1
        assert len(message.tracks) == 1
