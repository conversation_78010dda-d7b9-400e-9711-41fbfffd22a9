from unittest.mock import MagicMock

import pytest

from spyder_cop.cop_data.ASPTrack import CO<PERSON>lane
from spyder_cop.cop_data.inventory import Inventory
from spyder_cop.cop_data.missile import Missile
from spyder_cop.messages.util import ECOPType, ETypeOfICP


class TestMissileUpdateTypeOfICP:
    @pytest.fixture
    def mock_target(self):
        return MagicMock(spec=COPPlane, track_id=123)

    @pytest.fixture
    def mock_inventory(self):
        inventory = MagicMock(spec=Inventory)
        inventory.get_missile_id.side_effect = lambda icp_type: (
            5 if icp_type in [ETypeOfICP.ER_DERBY, ETypeOfICP.LR_DERBY] else 0
        )
        return inventory

    def test_update_to_er_derby_from_zero_station(self, mock_target, mock_inventory):
        # Arrange
        missile = Missile(
            1, 100.0, mock_target, ECOPType.MR, ETypeOfICP.DERBY, mock_inventory
        )
        missile.station_id = 0  # Ensure station_id is 0

        # Act
        missile.update_type_of_icp(ETypeOfICP.ER_DERBY)

        # Assert
        assert missile.type_of_icp == ETypeOfICP.ER_DERBY
        assert missile.station_id == 5

    def test_update_to_lr_derby_from_zero_station(self, mock_target, mock_inventory):
        # Arrange
        missile = Missile(
            1, 100.0, mock_target, ECOPType.MR, ETypeOfICP.DERBY, mock_inventory
        )
        missile.station_id = 0

        # Act
        missile.update_type_of_icp(ETypeOfICP.LR_DERBY)

        # Assert
        assert missile.type_of_icp == ETypeOfICP.LR_DERBY
        assert missile.station_id == 5

    def test_update_to_non_derby_er_lr_resets_station_id(
        self, mock_target, mock_inventory
    ):
        # Arrange
        missile = Missile(
            1, 100.0, mock_target, ECOPType.MR, ETypeOfICP.ER_DERBY, mock_inventory
        )
        missile.station_id = 5  # Set non-zero station_id

        # Act
        missile.update_type_of_icp(ETypeOfICP.DERBY)

        # Assert
        assert missile.type_of_icp == ETypeOfICP.DERBY
        assert missile.station_id == 0

    def test_update_to_er_derby_with_nonzero_station_id(
        self, mock_target, mock_inventory
    ):
        # Arrange
        missile = Missile(
            1, 100.0, mock_target, ECOPType.MR, ETypeOfICP.DERBY, mock_inventory
        )
        missile.station_id = 3  # Set non-zero station_id

        # Act
        missile.update_type_of_icp(ETypeOfICP.ER_DERBY)

        # Assert
        assert missile.type_of_icp == ETypeOfICP.ER_DERBY
        assert missile.station_id == 3
