#!/usr/bin/env python3
"""
Module for creating a multicast communication system using asyncio in Python.

This module provides the `MulticastCommunicator` class, which allows for
sending and receiving multicast messages in a multicast communication system.
It uses asyncio to handle the asynchronous communication.

Example usage:
    multicast_communicator =
    MulticastCommunicator(multicast_group="224.1.1.1", multicast_port=5008)
    asyncio.run(multicast_communicator.run())
"""
import asyncio

import sentry_sdk

from spyder_cop.config import Config, COPConfig
from spyder_cop.cop_sil import COPServer
from spyder_cop.logger import COPLogger


class COPCommunicator:
    """
    Class responsible for creating a multicast communication a system.
    """

    def __init__(self, config_data: Config) -> None:
        """
        Initializes a new instance of the MulticastCommunicator class.

        Args:
            config_data: Config.ini class
        """
        self.config = config_data
        self.logger = COPLogger("COPLogger")

        self.cops: list[COPServer] = self.init_cops(
            self.config.cop_configs, self.config
        )

    @staticmethod
    def init_cops(
        cop_config_list: list[COPConfig], general_config: Config
    ) -> list[COPServer]:
        """Init all cops radar"""
        return [
            COPServer(
                general_conf=general_config,
                cop_config=cop_config,
                log_fac=COPLogger.get_logger(cop_config.index),
            )
            for cop_config in cop_config_list
            if cop_config.activate
        ]

    async def run(self) -> None:
        """
        Runs the receiver and sender tasks concurrently using asyncio.
        """
        try:
            async with asyncio.TaskGroup() as group:
                for cop in self.cops:
                    group.create_task(cop.start(asyncio.get_running_loop()))
        except asyncio.exceptions.CancelledError as e:
            self.logger.error(f"Error occurred while starting COP servers: {e}")


def init_sentry(dsn: str | None):
    if not dsn or dsn == "":
        return
    sentry_sdk.init(
        dsn=dsn,
        # Add data like request headers and IP for users,
        # see https://docs.sentry.io/platforms/python/data-management/data-collected/ for more info
        send_default_pii=True,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for tracing.
        traces_sample_rate=1.0,
    )


if __name__ == "__main__":
    config = Config("config.ini")
    if config.is_sentry_enabled:
        init_sentry(config.sentry_dsn)
    multicast_communicator = COPCommunicator(
        config_data=config,
    )
    asyncio.run(multicast_communicator.run())
