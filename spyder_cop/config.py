#!/usr/bin/env python3
# coding:utf-8
"""
Author:   --<>
Purpose:
Created: 03/10/2023
"""
import configparser
import os
import socket

from spyder_bmc.enums import EMFUTypeRadius

from spyder_cop.messages.util import ECOPType


def is_empty(addr: str | None) -> bool:
    """Check if string is empty or None"""
    if addr is None:
        return True
    if addr == "":
        return True
    return False


def get_host_by_name(ip_name: str | None) -> str:
    """Try to resolve host name to IP"""
    if is_empty(ip_name):
        return ""
    else:
        return socket.gethostbyname(ip_name)


def _convert_mfu_type(typ: str) -> ECOPType:
    """try to convert MFU type"""
    if typ == "SR":
        return ECOPType.SR
    return ECOPType.MR


class COPConfig:
    """MFU configuration class"""

    def __init__(
        self, section: configparser.SectionProxy, cop_ip: str, bmc_ip: str, index: int
    ) -> None:
        """
        Initializes the MFUConfig class with the configuration section.

        Args:
            section (configparser.SectionProxy): The configuration section.
        """
        if not (0 <= index <= 6):
            raise ValueError("Index must be between 0 and 6")
        self.index = index
        self.activate = section.getboolean("Activated", fallback=False)
        self.is_training = section.getboolean("Training", fallback=False)
        self.mcu_serial_id = section.getint("MCU_SERIAL_ID", fallback=1)
        self.sender_id = section.getint("SENDER_ID", fallback=4)
        self.mfu_lat = section.getfloat("LAT", fallback=1.0)
        self.mfu_lon = section.getfloat("LON", fallback=1.0)
        self.mfu_alt = section.getfloat("ALT", fallback=1.0)

        self.bmc_ip = get_host_by_name(section.get("BMC_IP", fallback=bmc_ip))
        if is_empty(self.bmc_ip):
            self.bmc_ip = bmc_ip
        self.bmc_port = section.getint("BMC_PORT", fallback=0)

        self.cop_ip = get_host_by_name(section.get("COP_IP", fallback=cop_ip))
        if is_empty(self.cop_ip):
            self.cop_ip = cop_ip
        self.cop_port = section.getint("COP_PORT", fallback=0)

        self.cop_web_ip = get_host_by_name(section.get("COP_WEB_IP", fallback=cop_ip))
        if is_empty(self.cop_web_ip):
            self.cop_web_ip = cop_ip
        self.cop_web_port = section.getint("COP_WEB_PORT", fallback=0)

        self.gui_ip = get_host_by_name(section.get("GUI_IP", fallback=cop_ip))
        if is_empty(self.gui_ip):
            self.gui_ip = cop_ip
        self.gui_port = section.getint("GUI_PORT", fallback=0)

        self.remote_control_ip = get_host_by_name(
            section.get("REMOTE_CONTROL_IP", fallback=cop_ip)
        )
        if is_empty(self.remote_control_ip):
            self.remote_control_ip = cop_ip
        self.remote_control_port = section.getint("REMOTE_CONTROL_PORT", fallback=0)
        self.remote_control_strict_json = section.getboolean(
            "REMOTE_CONTROL_STRICT_JSON", fallback=False
        )

        self.missile_speed: float = section.getfloat("MISSILE_SPEED", fallback=879.0)

        self.cabin_azimuth: float = section.getfloat("CABIN_AZIMUTH", fallback=0.0)
        self.turret_azimuth: float = section.getfloat("TURRET_AZIMUTH", fallback=0.0)
        self._mfu_type: str = section.get("MFU_TYPE", fallback="MR")
        self.mfu_type: ECOPType = _convert_mfu_type(self._mfu_type)
        match self.mfu_type:
            case ECOPType.MR:
                self.mfu_radius = EMFUTypeRadius.RADIUS_MR.value * 1000
            case ECOPType.SR:
                self.mfu_radius = EMFUTypeRadius.RADIUS_SR.value * 1000

        self.python = section.getint("PYTHON", fallback=0)
        self.derby = section.getint("DERBY", fallback=0)
        self.derby_er = section.getint("DERBY_ER", fallback=0)
        self.derby_lr = section.getint("DERBY_LR", fallback=0)


class Config:
    """Config"""

    def __init__(self, config_name: str) -> None:
        """Constructor - read config file"""
        if not os.path.exists(config_name):
            raise FileNotFoundError(
                f"Configuration file '{config_name}' does not exist."
            )
        self.cop_configs: list[COPConfig] = []
        parser = configparser.ConfigParser()
        try:
            parser.read(config_name)
        except configparser.Error as e:
            raise configparser.Error(f"Error reading configuration file: {e}") from e

        # Use the 'General' section by default
        self.section = parser["General"]

        self.is_sentry_enabled = self.section.getboolean(
            "SENTRY_ENABLED", fallback=False
        )
        self.sentry_dsn = self.section.get("SENTRY_DSN", fallback="")

        # parser.optionxform = str
        self.bmc_server_ip = get_host_by_name(
            self.section.get("BMC_IP", fallback="127.0.0.1")
        )
        self.cop_ip = get_host_by_name(self.section.get("COP_IP", fallback="127.0.0.1"))

        self.multicast_group = self.section.get("BMC_MULTICAST")
        self.multicast_if = get_host_by_name(
            self.section.get("BMC_MULTICAST_IF", fallback="")
        )
        self.multicast_port = self.section.getint("BMC_MULTICAST_PORT")

        self.flightradar_ip = get_host_by_name(
            self.section.get("FLIGHTRADAR_IP", fallback="0.0.0.0")
        )
        self.flightradar_port = self.section.getint("FLIGHTRADAR_PORT", fallback=0)

        self._enable_flightradar = self.section.getboolean(
            "ENABLE_FLIGHTRADAR", fallback=True
        )
        self.enable_flightradar: bool = (
            self._enable_flightradar and self.flightradar_port > 0
        )

        self.flightradar_check_url = (
            f"http://{self.flightradar_ip}:{self.flightradar_port}/is_mfu"
        )
        self.flightradar_register_url = (
            f"http://{self.flightradar_ip}:{self.flightradar_port}/register_mfu"
        )

        self.timeout_downlink = self.section.getfloat("TIMEOUT_DOWNLINK", fallback=1.0)
        self.timeout_flightradar_registration = self.section.getfloat(
            "TIMEOUT_FLIGHTRADAR_REGISTRATION", fallback=10.0
        )
        self.timeout_status = self.section.getfloat("TIMEOUT_STATUS", fallback=1.0)
        self.timeout_status_timeout = self.section.getfloat(
            "TIMEOUT_STATUS_TIMEOUT", fallback=2.0
        )
        self.timeout_status_valid = self.section.getfloat(
            "TIMEOUT_STATUS_VALID", fallback=10.0
        )

        cop_configs: list[configparser.SectionProxy] = [
            parser[section]
            for section in parser.sections()
            if section.startswith("MFU")
        ]
        for index, cop_section in enumerate(cop_configs, start=1):
            self.cop_configs.append(
                COPConfig(cop_section, self.cop_ip, self.bmc_server_ip, index)
            )
