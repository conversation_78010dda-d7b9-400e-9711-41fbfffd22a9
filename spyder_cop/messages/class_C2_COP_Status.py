#!/usr/bin/env python3
# coding:utf-8
"""
Author:   --<>
Purpose:
Created: 03/04/2023
"""

import struct

from .util import (
    COPMessage,
    CopMessageType,
    EC2FireReadinessStatus,
    EC2Mode,
    EC2SafetyState,
    EC2Substate,
    ECEFPosition,
    EComponentStatus,
    EIBITAction,
    EInitFireSource,
    EInitLinkType,
    ELockingPolicy,
    RadarInfo,
    SupportPack,
)


class RelevantCOP(SupportPack):
    """RelevantCOP"""

    Relevant_COP_FORMAT = "<B B"
    Relevant_COP_SIZE = struct.calcsize(Relevant_COP_FORMAT)

    def __init__(
        self,
        cop_logical_id1: int,
        cop_logical_id2: int,
        cop_logical_id3: int,
        cop_logical_id4: int,
        cop_logical_id5: int,
        cop_logical_id6: int,
        ibit_action: EIBITAction | int = EIBITAction.STOP,
    ) -> None:
        """Constructor"""
        self.cop_logical_id1 = cop_logical_id1
        self.cop_logical_id2 = cop_logical_id2
        self.cop_logical_id3 = cop_logical_id3
        self.cop_logical_id4 = cop_logical_id4
        self.cop_logical_id5 = cop_logical_id5
        self.cop_logical_id6 = cop_logical_id6
        self.spare1 = 0
        self.spare2 = 0

        if isinstance(ibit_action, int):
            self._ibit_action = EIBITAction(ibit_action)
        else:
            self._ibit_action = ibit_action

    @property
    def ibit_action(self) -> EIBITAction:
        """getter"""
        return self._ibit_action

    @ibit_action.setter
    def ibit_action(self, value: EIBITAction | int) -> None:
        """setter"""
        if isinstance(value, EIBITAction):
            self._ibit_action = value
        else:
            self._ibit_action = EIBITAction(value)

    def pack(self) -> bytes:
        """Pack"""
        packbyte = (
            self.cop_logical_id1
            | (self.cop_logical_id2 << 1)
            | (self.cop_logical_id3 << 2)
            | (self.cop_logical_id4 << 3)
            | (self.cop_logical_id5 << 4)
            | (self.cop_logical_id6 << 5)
        )
        data = struct.pack(
            RelevantCOP.Relevant_COP_FORMAT, packbyte, self._ibit_action.value
        )
        return data

    @classmethod
    def unpack(cls, data: bytes) -> "RelevantCOP":
        """unpack"""
        (unpacked_byte, ibit_action) = struct.unpack(
            RelevantCOP.Relevant_COP_FORMAT, data[: RelevantCOP.Relevant_COP_SIZE]
        )
        cop_logical_id1 = unpacked_byte & 0x01
        cop_logical_id2 = (unpacked_byte >> 1) & 0x01
        cop_logical_id3 = (unpacked_byte >> 2) & 0x01
        cop_logical_id4 = (unpacked_byte >> 3) & 0x01
        cop_logical_id5 = (unpacked_byte >> 4) & 0x01
        cop_logical_id6 = (unpacked_byte >> 5) & 0x01
        return cls(
            cop_logical_id1,
            cop_logical_id2,
            cop_logical_id3,
            cop_logical_id4,
            cop_logical_id5,
            cop_logical_id6,
            ibit_action,
        )

    def to_dict(self) -> dict:
        """to JSON"""
        return {
            "cop_logical_id1": self.cop_logical_id1,
            "cop_logical_id2": self.cop_logical_id2,
            "cop_logical_id3": self.cop_logical_id3,
            "cop_logical_id4": self.cop_logical_id4,
            "cop_logical_id5": self.cop_logical_id5,
            "cop_logical_id6": self.cop_logical_id6,
            "ibit_action": self.ibit_action.value,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "RelevantCOP":
        """From JSON"""
        return cls(
            data["cop_logical_id1"],
            data["cop_logical_id2"],
            data["cop_logical_id3"],
            data["cop_logical_id4"],
            data["cop_logical_id5"],
            data["cop_logical_id6"],
            data["ibit_action"],
        )

    @classmethod
    def get_size(cls) -> int:
        """get_size"""
        return cls.Relevant_COP_SIZE

    def __eq__(self, other) -> bool:
        """Compare instances"""
        if not isinstance(other, RelevantCOP):
            return False

        return (
            self.cop_logical_id1 == other.cop_logical_id1
            and self.cop_logical_id2 == other.cop_logical_id2
            and self.cop_logical_id3 == other.cop_logical_id3
            and self.cop_logical_id4 == other.cop_logical_id4
            and self.cop_logical_id5 == other.cop_logical_id5
            and self.cop_logical_id6 == other.cop_logical_id6
            and self.ibit_action == other.ibit_action
        )

    def __str__(self) -> str:
        """Description"""
        return (
            "Relevant_COP: "
            f"1: {self.cop_logical_id1}, 2: {self.cop_logical_id2},"
            f"3: {self.cop_logical_id3}, 4: {self.cop_logical_id4},"
            f"5: {self.cop_logical_id5},"
            f"6: {self.cop_logical_id6},\n"
            f"IBIT_Action: {self.ibit_action}\n"
        )


class COPInitializationRequest(SupportPack):
    """COPInitializationRequest"""

    COP_Initialization_Request_FORMAT = "<B"
    COP_Initialization_Request_SIZE = struct.calcsize(COP_Initialization_Request_FORMAT)

    def __init__(
        self,
        link_type: EInitLinkType | int = EInitLinkType.WIRED,
        locking_policy: ELockingPolicy | int = ELockingPolicy.LOAL,
        fire_source: EInitFireSource | int = EInitFireSource.LOCAL,
    ):
        """Constructor"""
        if isinstance(link_type, int):
            self._link_type = EInitLinkType(link_type)
        else:
            self._link_type = link_type

        if isinstance(locking_policy, int):
            self._locking_policy = ELockingPolicy(locking_policy)
        else:
            self._locking_policy = locking_policy

        if isinstance(fire_source, int):
            self._fire_source = EInitFireSource(fire_source)
        else:
            self._fire_source = fire_source

        self._spare1 = 0
        self._spare2 = 0
        self._spare3 = 0
        self._spare4 = 0
        self._spare5 = 0

    @property
    def link_type(self) -> EInitLinkType:
        """getter"""
        return self._link_type

    @link_type.setter
    def link_type(self, value: EInitLinkType | int) -> None:
        """setter"""
        if isinstance(value, EInitLinkType):
            self._link_type = value
        else:
            self._link_type = EInitLinkType(value)

    @property
    def locking_policy(self) -> ELockingPolicy:
        """getter"""
        return self._locking_policy

    @locking_policy.setter
    def locking_policy(self, value: ELockingPolicy | int) -> None:
        """
        Set the locking policy.

        Args:
            value (ELockingPolicy | int): The locking policy to set.
        """
        self._locking_policy = (
            value if isinstance(value, ELockingPolicy) else ELockingPolicy(value)
        )

    @property
    def fire_source(self) -> EInitFireSource:
        """getter"""
        return self._fire_source

    @fire_source.setter
    def fire_source(self, value: EInitFireSource | int) -> None:
        """
        Set the fire source.

        Args:
            value (EInitFireSource | int): The fire source to set.
        """
        self._fire_source = (
            value if isinstance(value, EInitFireSource) else EInitFireSource(value)
        )

    def pack(self) -> bytes:
        """pack"""
        packbyte = (
            self._link_type.value
            | (self._locking_policy.value << 1)
            | (self._fire_source.value << 2)
        )
        data = struct.pack(
            COPInitializationRequest.COP_Initialization_Request_FORMAT, packbyte
        )
        return data

    @classmethod
    def unpack(cls, data: bytes) -> "COPInitializationRequest":
        """unpack"""
        (unpacked_byte,) = struct.unpack(
            COPInitializationRequest.COP_Initialization_Request_FORMAT,
            data[: COPInitializationRequest.COP_Initialization_Request_SIZE],
        )
        link_type = unpacked_byte & 0x01
        locking_policy = (unpacked_byte >> 1) & 0x01
        fire_source = (unpacked_byte >> 2) & 0x01
        return cls(link_type, locking_policy, fire_source)

    def to_dict(self):
        return {
            "link_type": self.link_type.value,
            "locking_policy": self.locking_policy.value,
            "fire_source": self.fire_source.value,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "COPInitializationRequest":
        return cls(
            link_type=data.get("link_type", EInitLinkType.WIRED),
            locking_policy=data.get("locking_policy", ELockingPolicy.LOAL),
            fire_source=data.get("fire_source", EInitFireSource.LOCAL),
        )

    @classmethod
    def get_size(cls) -> int:
        """get_size"""
        return cls.COP_Initialization_Request_SIZE

    def __eq__(self, other):
        if not isinstance(other, COPInitializationRequest):
            return False
        return (
            self.link_type == other.link_type
            and self.locking_policy == other.locking_policy
            and self.fire_source == other.fire_source
        )

    def __str__(self) -> str:
        """Description"""
        return (
            f"COP_Initialization_Request: Link_Type: {self.link_type}, "
            f"Locking_Policy: {self.locking_policy}, "
            f"Fire_Source: {self.fire_source}\n"
        )


class COPInitData:
    """COPInitData"""

    def __init__(self, cop_logical_id: int, mcu_serial_id: int) -> None:
        """Constructor"""
        self.cop_logical_id = cop_logical_id
        self.mcu_serial_id = mcu_serial_id
        self.required_state = COPInitializationRequest()
        self._site_text_length = 0
        self._site_name = ""
        self._datasize = 0

    def set_required_state_lock_policy(self, policy: ELockingPolicy | int) -> None:
        """Set locking policy
        0 - LOBL, 1- LOAL
        """
        self.required_state.locking_policy = ELockingPolicy(policy)

    def set_required_state_fire_source(self, source: EInitFireSource | int) -> None:
        """Set fire source
        0 - Local (MFU), 1 - Remote (BMC)
        """
        self.required_state.fire_source = EInitFireSource(source)

    @property
    def site_name(self) -> str:
        """getter"""
        return self._site_name

    @site_name.setter
    def site_name(self, value: str) -> None:
        """setter"""
        self._site_name = value.encode("ascii").decode("ascii")
        self._site_text_length = len(self._site_name)

    def pack(self) -> bytes:
        """pack"""
        data = struct.pack("<BB", self.cop_logical_id, self.mcu_serial_id)

        data += self.required_state.pack()

        name_bytes = self._site_name.encode("ascii")
        self._site_text_length = len(name_bytes)
        data += struct.pack(
            "<B%ds" % self._site_text_length, self._site_text_length, name_bytes
        )
        self._datasize = len(data)
        return data

    @classmethod
    def unpack(cls, data: bytes) -> "COPInitData":
        """unzpack"""
        (cop_logical_id, mcu_serial_id) = struct.unpack(
            "<BB", data[: struct.calcsize("<BB")]
        )
        cop_init = cls(cop_logical_id, mcu_serial_id)

        data = data[struct.calcsize("<BB") :]
        req_state = COPInitializationRequest.unpack(data)
        cop_init.required_state = req_state
        data = data[COPInitializationRequest.get_size() :]

        name_len = struct.unpack("<B", data[: struct.calcsize("<B")])[0]
        name = struct.unpack("<%ds" % name_len, data[1 : 1 + name_len])[0].decode(
            "ascii"
        )

        cop_init.site_name = name
        cop_init._site_text_length = name_len
        cop_init._datasize = (
            struct.calcsize("<BB")
            + COPInitializationRequest.get_size()
            + struct.calcsize("<B")
            + name_len
        )
        return cop_init

    def to_dict(self) -> dict:
        """Converts COPInitData object to a dictionary"""
        return {
            "cop_logical_id": self.cop_logical_id,
            "mcu_serial_id": self.mcu_serial_id,
            "required_state": self.required_state.to_dict(),
            "site_name": self.site_name,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "COPInitData":
        """Creates a COPInitData object from a dictionary"""
        cop_init = cls(
            cop_logical_id=data.get("cop_logical_id", 0),
            mcu_serial_id=data.get("mcu_serial_id", 0),
        )
        cop_init.site_name = data["site_name"]
        cop_init.required_state = COPInitializationRequest.from_dict(
            data["required_state"]
        )
        return cop_init

    def get_size(self) -> int:
        """get_size"""
        return self._datasize

    def __eq__(self, other) -> bool:
        """Compares two COPInitData objects for equality"""
        if not isinstance(other, COPInitData):
            return False
        return (
            self.cop_logical_id == other.cop_logical_id
            and self.mcu_serial_id == other.mcu_serial_id
            and self.required_state == other.required_state
            and self.site_name == other.site_name
        )

    def __str__(self) -> str:
        """Description"""
        return (
            f"COP_Init_Data: COP_Logical_ID: {self.cop_logical_id}, "
            f"MCU_Serial_ID: {self.mcu_serial_id},\n "
            f"Required_State: {self.required_state}, site name: {self.site_name}\n"
        )


class C2COPStatus(COPMessage):
    """C2_COP_Status"""

    FORMAT = "<BBBBB"
    SIZE = struct.calcsize(FORMAT)

    MSG_TYPE = CopMessageType.C2_COP_Status

    def __init__(
        self,
        c2_mode: EC2Mode | int,
        c2_substate: EC2Substate | int,
        c2_safety_state: EC2SafetyState | int,
        c2_fire_readiness_status: EC2FireReadinessStatus | int,
        c2_status: EComponentStatus | int,
    ) -> None:
        """Constructor"""
        super().__init__()
        if isinstance(c2_mode, int):
            self._cc_mode = EC2Mode(c2_mode)
        else:
            self._cc_mode = c2_mode

        if isinstance(c2_substate, int):
            self._cc_substate = EC2Substate(c2_substate)
        else:
            self._cc_substate = c2_substate

        if isinstance(c2_safety_state, int):
            self._cc_safety_state = EC2SafetyState(c2_safety_state)
        else:
            self._cc_safety_state = c2_safety_state

        if isinstance(c2_fire_readiness_status, int):
            self._cc_fire_readiness_status = EC2FireReadinessStatus(
                c2_fire_readiness_status
            )
        else:
            self._cc_fire_readiness_status = c2_fire_readiness_status

        if isinstance(c2_status, int):
            self._cc_status = EComponentStatus(c2_status)
        else:
            self._cc_status = c2_status

        self._cc_position = ECEFPosition(0, 0, 0)
        self._num_of_connected_sensors = 1
        self.radar_info = RadarInfo(0, 0, 0, 0)
        self.spare = 0
        self.dlq_ibit_request = RelevantCOP(0, 0, 0, 0, 0, 0, 0)
        self.num_of_connected_mfus = 0

        self.initialization_data_array: list[COPInitData] = []

    @property
    def cc_mode(self) -> EC2Mode:
        """getter"""
        return self._cc_mode

    @cc_mode.setter
    def cc_mode(self, value: EC2Mode | int) -> None:
        """setter"""
        if isinstance(value, EC2Mode):
            self._cc_mode = value
        else:
            self._cc_mode = EC2Mode(value)

    @property
    def cc_substate(self) -> EC2Substate:
        """getter"""
        return self._cc_substate

    @cc_substate.setter
    def cc_substate(self, value: EC2Substate | int) -> None:
        """setter"""
        if isinstance(value, EC2Substate):
            self._cc_substate = value
        else:
            self._cc_substate = EC2Substate(value)

    @property
    def cc_safety_state(self) -> EC2SafetyState:
        """getter"""
        return self._cc_safety_state

    @cc_safety_state.setter
    def cc_safety_state(self, value: EC2SafetyState | int) -> None:
        """setter"""
        if isinstance(value, EC2SafetyState):
            self._cc_safety_state = value
        else:
            self._cc_safety_state = EC2SafetyState(value)

    @property
    def cc_fire_readiness_status(self) -> EC2FireReadinessStatus:
        """getter"""
        return self._cc_fire_readiness_status

    @cc_fire_readiness_status.setter
    def cc_fire_readiness_status(self, value: EC2FireReadinessStatus | int) -> None:
        """setter"""
        if isinstance(value, EC2FireReadinessStatus):
            self._cc_fire_readiness_status = value
        else:
            self._cc_fire_readiness_status = EC2FireReadinessStatus(value)

    @property
    def cc_status(self) -> EComponentStatus:
        """getter"""
        return self._cc_status

    @cc_status.setter
    def cc_status(self, value: EComponentStatus | int) -> None:
        """setter"""
        if isinstance(value, EComponentStatus):
            self._cc_status = value
        else:
            self._cc_status = EComponentStatus(value)

    @property
    def cc_position(self) -> ECEFPosition:
        """getter"""
        return self._cc_position

    @cc_position.setter
    def cc_position(self, value: ECEFPosition) -> None:
        """setter"""
        self._cc_position = value

    @property
    def num_of_connected_sensors(self) -> int:
        """getter"""
        return self._num_of_connected_sensors

    @num_of_connected_sensors.setter
    def num_of_connected_sensors(self, value: int) -> None:
        """setter"""
        self._num_of_connected_sensors = value

    def add_init_data(self, init_data: COPInitData) -> None:
        """add_init_data"""
        self.initialization_data_array.append(init_data)
        self.num_of_connected_mfus += 1

    def pack(self) -> bytes:
        """pack"""
        self.num_of_connected_mfus = len(self.initialization_data_array)

        data = struct.pack(
            C2COPStatus.FORMAT,
            self._cc_mode.value,
            self._cc_substate.value,
            self._cc_safety_state.value,
            self._cc_fire_readiness_status.value,
            self._cc_status.value,
        )
        data += self.cc_position.pack()
        data += struct.pack("<B", self.num_of_connected_sensors)
        data += self.radar_info.pack()
        data += struct.pack("<H", self.spare)
        data += self.dlq_ibit_request.pack()
        data += struct.pack("<B", self.num_of_connected_mfus)
        for mfu in self.initialization_data_array:
            data += mfu.pack()
        return data

    @classmethod
    def unpack(cls, data: bytes) -> "C2COPStatus":
        """unpack"""
        (
            c2_mode,
            c2_substate,
            c2_safety_state,
            c2_fire_readiness_status,
            c2_status,
        ) = struct.unpack(cls.FORMAT, data[: cls.SIZE])

        c2status = C2COPStatus(
            c2_mode, c2_substate, c2_safety_state, c2_fire_readiness_status, c2_status
        )
        data = data[C2COPStatus.SIZE :]
        c2status.cc_position = ECEFPosition.unpack(data[: ECEFPosition.get_size()])
        data = data[ECEFPosition.get_size() :]
        (num_of_connected_sensors,) = struct.unpack("<B", data[: struct.calcsize("<B")])
        c2status.num_of_connected_sensors = num_of_connected_sensors
        data = data[struct.calcsize("<B") :]
        c2status.radar_info = RadarInfo.unpack(data[: RadarInfo.get_size()])
        data = data[RadarInfo.get_size() :]
        (spare,) = struct.unpack("<H", data[: struct.calcsize("<H")])
        c2status.spare = spare
        data = data[struct.calcsize("<H") :]
        c2status.dlq_ibit_request = RelevantCOP.unpack(data[: RelevantCOP.get_size()])
        data = data[RelevantCOP.get_size() :]
        (num_of_connected_mfus,) = struct.unpack("<B", data[: struct.calcsize("<B")])
        c2status.num_of_connected_mfus = 0
        data = data[struct.calcsize("<B") :]
        for _i in range(num_of_connected_mfus):
            mfudata = COPInitData.unpack(data)
            c2status.add_init_data(mfudata)
            data = data[mfudata.get_size() :]
        return c2status

    def to_dict(self) -> dict:
        """Converts C2COPStatus object to a dictionary"""
        initialization_data_array = [
            data.to_dict() for data in self.initialization_data_array
        ]

        return {
            "c2_mode": self.cc_mode.value,
            "c2_substate": self.cc_substate.value,
            "c2_safety_state": self.cc_safety_state.value,
            "c2_fire_readiness_status": self.cc_fire_readiness_status.value,
            "c2_status": self.cc_status.value,
            "cc_position": self.cc_position.to_dict(),
            "num_of_connected_sensors": self.num_of_connected_sensors,
            "radar_info": self.radar_info.to_dict(),
            "dlq_ibit_request": self.dlq_ibit_request.to_dict(),
            "initialization_data_array": initialization_data_array,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "C2COPStatus":
        """Creates a C2COPStatus object from a dictionary"""
        c2_mode = data.get("c2_mode", 0)
        c2_substate = data.get("c2_substate", 0)
        c2_safety_state = data.get("c2_safety_state", 0)
        c2_fire_readiness_status = data.get("c2_fire_readiness_status", 0)
        c2_status = data.get("c2_status", 0)

        c2cop_status = cls(
            c2_mode, c2_substate, c2_safety_state, c2_fire_readiness_status, c2_status
        )
        c2cop_status.cc_position = ECEFPosition.from_dict(data.get("cc_position", {}))

        c2cop_status.radar_info = RadarInfo.from_dict(data.get("radar_info", {}))
        c2cop_status.dlq_ibit_request = RelevantCOP.from_dict(
            data.get("dlq_ibit_request", {})
        )
        c2cop_status.num_of_connected_sensors = data.get("num_of_connected_sensors", 0)

        initialization_data_array = data.get("initialization_data_array", [])
        for init_data in initialization_data_array:
            c2cop_status.add_init_data(COPInitData.from_dict(init_data))

        return c2cop_status

    def __eq__(self, other) -> bool:
        """Compares two C2COPStatus objects for equality"""
        if not isinstance(other, C2COPStatus):
            return False

        return (
            self._cc_mode == other._cc_mode
            and self.cc_substate == other.cc_substate
            and self.cc_safety_state == other.cc_safety_state
            and self.cc_fire_readiness_status == other.cc_fire_readiness_status
            and self.cc_status == other.cc_status
            and self.cc_position == other.cc_position
            and self.num_of_connected_sensors == other.num_of_connected_sensors
            and self.radar_info == other.radar_info
            and self.dlq_ibit_request == other.dlq_ibit_request
            and self.num_of_connected_mfus == other.num_of_connected_mfus
            and self.initialization_data_array == other.initialization_data_array
        )

    def __str__(self) -> str:
        """Description"""
        ret = (
            f"C2_COP_Status: \nC2_Mode: {self.cc_mode}, "
            f"C2_Substate: {self.cc_substate},\n"
            f"C2_Safety_State: {self.cc_safety_state}, "
            f"C2_Fire_Readiness_Status: {self.cc_fire_readiness_status},\n "
            f"C2_Status: {self.cc_status}, "
            f"C2_Position: {self.cc_position}\n"
        )
        for i in self.initialization_data_array:
            ret += f"{i}\n"
        return ret
