#!/usr/bin/env python3
# coding:utf-8
"""
Author:   --<>
Purpose:
Created: 03/04/2023
"""

import struct

from spyder_cop.messages.util import SupportPack

from .util import COPMessage, CopMessageType


class SMslInAirType(SupportPack):
    FORMAT = "<BH B L HH 4h 4B"
    SIZE = struct.calcsize(FORMAT)

    def __init__(
        self,
        msl_id: int,
        eng_id: int,
        rdr_index: int,  # always 1
        rdr_time: int,  # scale 25us
        rdr_time_year: int,  # 4digits format UTC
        rdr_time_date_year: int,  # 0 - 365 0 -1.day
        azimuth: int,
        elevation: int,
        rdr_range: int,
        status: int,
        rdr_cov_x: int,
        rdr_cov_y: int,
        rdr_cov_z: int,
    ) -> None:
        self.msl_id = msl_id
        self.eng_id = eng_id
        self.rdr_index = rdr_index
        self.rdr_time = rdr_time
        self.rdr_time_year = rdr_time_year
        self.rdr_time_date_year = rdr_time_date_year

        self.azimuth = azimuth
        self.elevation = elevation
        self.rdr_range = rdr_range
        self.status = status

        self.rdr_cov_x = rdr_cov_x
        self.rdr_cov_y = rdr_cov_y
        self.rdr_cov_z = rdr_cov_z
        self._spare: int = 0

    def pack(self) -> bytes:
        """pack"""
        return struct.pack(
            SMslInAirType.FORMAT,
            self.msl_id,
            self.eng_id,
            self.rdr_index,
            self.rdr_time,
            self.rdr_time_year,
            self.rdr_time_date_year,
            self.azimuth,
            self.elevation,
            self.rdr_range,
            self.status,
            self.rdr_cov_x,
            self.rdr_cov_y,
            self.rdr_cov_z,
            self._spare,
        )

    @classmethod
    def unpack(cls, data: bytes) -> "SMslInAirType":
        """Unpack"""
        (
            msl_id,
            eng_id,
            rdr_index,
            rdr_time,
            rdr_time_year,
            rdr_time_date_year,
            azimuth,
            elevation,
            rdr_range,
            status,
            rdr_cov_x,
            rdr_cov_y,
            rdr_cov_z,
            spare,
        ) = struct.unpack(SMslInAirType.FORMAT, data[: SMslInAirType.SIZE])
        return cls(
            msl_id,
            eng_id,
            rdr_index,
            rdr_time,
            rdr_time_year,
            rdr_time_date_year,
            azimuth,
            elevation,
            rdr_range,
            status,
            rdr_cov_x,
            rdr_cov_y,
            rdr_cov_z,
        )

    @classmethod
    def from_dict(cls, data: dict) -> "SMslInAirType":
        """Create from dict"""
        return cls(
            data.get("msl_id", 0),
            data.get("eng_id", 0),
            data.get("rdr_index", 1),
            data.get("rdr_time", 0),
            data.get("rdr_time_year", 0),
            data.get("rdr_time_date_year", 0),
            data.get("azimuth", 0),
            data.get("elevation", 0),
            data.get("rdr_range", 0),
            data.get("status", 0),
            data.get("rdr_cov_x", 0),
            data.get("rdr_cov_y", 0),
            data.get("rdr_cov_z", 0),
        )

    def to_dict(self) -> dict:
        """Convert to dict"""
        return {
            "msl_id": self.msl_id,
            "eng_id": self.eng_id,
            "rdr_index": self.rdr_index,
            "rdr_time": self.rdr_time,
            "rdr_time_year": self.rdr_time_year,
            "rdr_time_date_year": self.rdr_time_date_year,
            "azimuth": self.azimuth,
            "elevation": self.elevation,
            "rdr_range": self.rdr_range,
            "status": self.status,
            "rdr_cov_x": self.rdr_cov_x,
            "rdr_cov_y": self.rdr_cov_y,
            "rdr_cov_z": self.rdr_cov_z,
        }

    def __eq__(self, other) -> bool:
        """Compare two structures"""
        if not isinstance(other, SMslInAirType):
            return False
        for attr in [
            "msl_id",
            "eng_id",
        ]:
            if getattr(self, attr) != getattr(other, attr):
                return False
        return True

    def __hash__(self) -> int:
        """Generate hash"""
        return hash(
            (
                self.msl_id,
                self.eng_id,
                self.rdr_index,
                self.rdr_time,
                self.rdr_time_year,
                self.rdr_time_date_year,
                self.azimuth,
                self.elevation,
                self.rdr_range,
                self.status,
                self.rdr_cov_x,
                self.rdr_cov_y,
                self.rdr_cov_z,
            )
        )

    def __str__(self) -> str:
        """Description"""
        return (
            f"SMslInAirType(msl_id={self.msl_id}, eng_id={self.eng_id}, "
            f"rdr_index={self.rdr_index}, rdr_time={self.rdr_time}, "
            f"rdr_time_year={self.rdr_time_year}, "
            f"rdr_time_date_year={self.rdr_time_date_year}, "
            f"azimuth={self.azimuth}, elevation={self.elevation}, "
            f"rdr_range={self.rdr_range}, status={self.status}, "
            f"rdr_cov_x={self.rdr_cov_x}, rdr_cov_y={self.rdr_cov_y}, "
            f"rdr_cov_z={self.rdr_cov_z})"
        )


class SmslsLocationFromRdrData(SupportPack):
    FORMAT = "<B"
    SIZE = struct.calcsize(FORMAT)

    MAX_ITEMS = 8

    def __init__(self) -> None:
        self.msl_in_air: int = 0
        self.msl_array: set[SMslInAirType] = set()

    def add_msl(self, msl_data: SMslInAirType) -> bool:
        for data in self.msl_array:
            if data == msl_data:
                return False
        else:
            if self.msl_in_air < SmslsLocationFromRdrData.MAX_ITEMS:
                self.msl_array.add(msl_data)
                self.msl_in_air = len(self.msl_array)
                return True
        return False

    def pack(self) -> bytes:
        """pack"""
        packed_data = struct.pack(SmslsLocationFromRdrData.FORMAT, self.msl_in_air)
        for msl in self.msl_array:
            packed_data += msl.pack()
        return packed_data

    @classmethod
    def unpack(cls, data: bytes) -> "SmslsLocationFromRdrData":
        """unpack"""
        unpacked_data = cls()
        (unpacked_data.msl_in_air,) = struct.unpack_from(cls.FORMAT, data, 0)

        offset = cls.SIZE
        for _ in range(unpacked_data.msl_in_air):
            msl_data = SMslInAirType.unpack(data[offset:])
            offset += SMslInAirType.SIZE
            unpacked_data.msl_array.add(msl_data)
        return unpacked_data

    @classmethod
    def from_dict(cls, data: dict) -> "SmslsLocationFromRdrData":
        """create from dict"""
        obj = cls()
        obj.msl_in_air = data.get("msl_in_air", 0)
        obj.msl_array = {
            SMslInAirType.from_dict(item) for item in data.get("msl_array", [])
        }
        return obj

    def to_dict(self) -> dict:
        """convert to dict"""
        return {
            "msl_in_air": self.msl_in_air,
            "msl_array": [msl.to_dict() for msl in self.msl_array],
        }

    def __str__(self) -> str:
        """Description"""
        return (
            f"SmslsLocationFromRdrData(msl_in_air={self.msl_in_air}, "
            f"msl_array={[str(msl) for msl in self.msl_array]})"
        )


class C2COPMslsLocationFromRdr(COPMessage):
    """C2COPMslsLocationFromRdr"""

    MSG_TYPE = CopMessageType.C2_COP_MslsLocationFromRdr

    def __init__(self) -> None:
        """Constructor"""
        super().__init__()
        self.data: SmslsLocationFromRdrData = SmslsLocationFromRdrData()

    def add_msl_data(self, msl_data: SMslInAirType) -> bool:
        return self.data.add_msl(msl_data)

    def pack(self) -> bytes:
        """pack"""
        return self.data.pack()

    @classmethod
    def unpack(cls, data: bytes) -> "C2COPMslsLocationFromRdr":
        """unpack"""
        radar_data = SmslsLocationFromRdrData.unpack(data)
        obj = cls()
        obj.data = radar_data
        return obj

    @classmethod
    def from_dict(cls, data: dict) -> "C2COPMslsLocationFromRdr":
        """Create from dict"""
        obj = cls()
        obj.data = SmslsLocationFromRdrData.from_dict(data["data"])
        return obj

    def to_dict(self) -> dict:
        """Convert to dict"""
        return {"data": self.data.to_dict()}

    def __str__(self) -> str:
        """Description"""
        return f"C2COPMslsLocationFromRdr: data = {self.data}\n"
