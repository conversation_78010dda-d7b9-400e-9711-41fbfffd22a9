#!/usr/bin/env python3
# coding:utf-8
"""
Author:   --<>
Purpose:
Created: 03/04/2023
"""

import struct

from .util import (
    COPMessage,
    CopMessageType,
    ECOPEngagementAbortSource,
    ECOPEngagementHandling,
    ECOPMCUEngagementStatus,
    ECOPMisfireReason,
    ECOPSeekerMode,
    EICPLockStatus,
    EICPStage,
    ELockingPolicy,
    ENoYesBoolean,
    ETypeOfICP,
    SupportPack,
)


class COPMCUEngagement(SupportPack):
    """COP_MCU_Engagement"""

    FORMAT = "<B"
    SIZE = struct.calcsize(FORMAT)

    def __init__(
        self,
        mcu_engagement_handling: (
            ECOPEngagementHandling | int
        ) = ECOPEngagementHandling.WILL_PRO,
        mcu_engagement_status: (
            ECOPMCUEngagementStatus | int
        ) = ECOPMCUEngagementStatus.OPENED,
        engagement_abort_source: (
            ECOPEngagementAbortSource | int
        ) = ECOPEngagementAbortSource.NO_ABORT,
    ):
        """Constructor"""
        if isinstance(mcu_engagement_handling, int):
            self._mcu_engagement_handling = ECOPEngagementHandling(
                mcu_engagement_handling
            )
        else:
            self._mcu_engagement_handling = mcu_engagement_handling

        if isinstance(mcu_engagement_status, int):
            self._mcu_engagement_status = ECOPMCUEngagementStatus(mcu_engagement_status)
        else:
            self._mcu_engagement_status = mcu_engagement_status
        self.spare = 0

        if isinstance(engagement_abort_source, int):
            self._engagement_abort_source = ECOPEngagementAbortSource(
                engagement_abort_source
            )
        else:
            self._engagement_abort_source = engagement_abort_source

    @property
    def mcu_engagement_handling(self) -> ECOPEngagementHandling:
        """getter"""
        return self._mcu_engagement_handling

    @mcu_engagement_handling.setter
    def mcu_engagement_handling(self, value: ECOPEngagementHandling | int) -> None:
        """setter"""
        if isinstance(value, ECOPEngagementHandling):
            self._mcu_engagement_handling = value
        else:
            self._mcu_engagement_handling = ECOPEngagementHandling(value)

    @property
    def mcu_engagement_status(self) -> ECOPMCUEngagementStatus:
        """getter"""
        return self._mcu_engagement_status

    @mcu_engagement_status.setter
    def mcu_engagement_status(self, value: ECOPMCUEngagementStatus | int) -> None:
        """setter"""
        if isinstance(value, ECOPMCUEngagementStatus):
            self._mcu_engagement_status = value
        else:
            self._mcu_engagement_status = ECOPMCUEngagementStatus(value)

    @property
    def engagement_abort_source(self) -> ECOPEngagementAbortSource:
        """getter"""
        return self._engagement_abort_source

    @engagement_abort_source.setter
    def engagement_abort_source(self, value: ECOPEngagementAbortSource | int) -> None:
        """setter"""
        if isinstance(value, ECOPEngagementAbortSource):
            self._engagement_abort_source = value
        else:
            self._engagement_abort_source = ECOPEngagementAbortSource(value)

    def pack(self) -> bytes:
        """pack"""
        packbyte = (
            self._mcu_engagement_handling.value
            | (self._mcu_engagement_status.value << 3)
            | (self.spare << 5)
            | (self._engagement_abort_source.value << 6)
        )
        data = struct.pack(COPMCUEngagement.FORMAT, packbyte)
        return data

    @classmethod
    def unpack(cls, data: bytes) -> "COPMCUEngagement":
        """unpack"""
        (unpacked_byte,) = struct.unpack(cls.FORMAT, data[: cls.SIZE])
        mcu_engagement_handling = unpacked_byte & 0x07
        mcu_engagement_status = (unpacked_byte >> 3) & 0x03
        engagement_abort_source = (unpacked_byte >> 6) & 0x03

        return cls(
            mcu_engagement_handling, mcu_engagement_status, engagement_abort_source
        )

    def to_dict(self) -> dict:
        return {
            "mcu_engagement_handling": self.mcu_engagement_handling.value,
            "mcu_engagement_status": self.mcu_engagement_status.value,
            "engagement_abort_source": self.engagement_abort_source.value,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "COPMCUEngagement":
        return cls(
            mcu_engagement_handling=data["mcu_engagement_handling"],
            mcu_engagement_status=data["mcu_engagement_status"],
            engagement_abort_source=data["engagement_abort_source"],
        )

    @classmethod
    def get_size(cls) -> int:
        """get_size"""
        return cls.SIZE

    def __eq__(self, other) -> bool:
        if not isinstance(other, COPMCUEngagement):
            return False
        return (
            self.mcu_engagement_handling == other.mcu_engagement_handling
            and self.mcu_engagement_status == other.mcu_engagement_status
            and self.engagement_abort_source == other.engagement_abort_source
        )

    def __str__(self) -> str:
        """Description"""
        return (
            "COP_MCU_Engagement: MCU_Engagement_Handling: "
            f"{self.mcu_engagement_handling}, "
            f"MCU_Engagement_Status: {self.mcu_engagement_status}, "
            f"Engagement_Abort_Source: {self.engagement_abort_source}\n"
        )


class COPMCUEngagementSpare(SupportPack):
    """COP_MCU_Engagement_Spare"""

    FORMAT = "<B"
    SIZE = struct.calcsize(FORMAT)

    def __init__(self, seeker_mode: ECOPSeekerMode | int = ECOPSeekerMode.FREE) -> None:
        """Constructor"""
        if isinstance(seeker_mode, int):
            self._seeker_mode = ECOPSeekerMode(seeker_mode)
        else:
            self._seeker_mode = seeker_mode
        self.spare = 0

    @property
    def seeker_mode(self) -> ECOPSeekerMode:
        """getter"""
        return self._seeker_mode

    @seeker_mode.setter
    def seeker_mode(self, value: ECOPSeekerMode | int) -> None:
        """setter"""
        if isinstance(value, ECOPSeekerMode):
            self._seeker_mode = value
        else:
            self._seeker_mode = ECOPSeekerMode(value)

    def pack(self) -> bytes:
        """pack"""
        packbyte = self._seeker_mode.value
        data = struct.pack(COPMCUEngagementSpare.FORMAT, packbyte)
        return data

    @classmethod
    def unpack(cls, data: bytes) -> "COPMCUEngagementSpare":
        """unpack"""
        (unpacked_byte,) = struct.unpack(cls.FORMAT, data[: cls.SIZE])
        seeker_mode = unpacked_byte & 0x01

        return cls(seeker_mode)

    def to_dict(self) -> dict:
        """Converts object attributes to a dictionary"""
        return {"seeker_mode": self._seeker_mode.value}

    @classmethod
    def from_dict(cls, data: dict) -> "COPMCUEngagementSpare":
        """Creates an instance from a dictionary"""
        return cls(data["seeker_mode"])

    @classmethod
    def get_size(cls) -> int:
        """get_size"""
        return cls.SIZE

    def __eq__(self, other) -> bool:
        """Equality comparison"""
        return (
            isinstance(other, COPMCUEngagementSpare)
            and self._seeker_mode == other._seeker_mode
        )

    def __str__(self) -> str:
        """Description"""
        data = f"COP_MCU_Engagement_Spare: Seeker_Mode: {self.seeker_mode}\n"
        return data


class COPAllocatedMissileStatus(SupportPack):
    """COP_Missiles_Available_For_Fire"""

    FORMAT = "<BB"
    SIZE = struct.calcsize(FORMAT)

    def __init__(
        self,
        icp_allocated: ETypeOfICP | int = ETypeOfICP.OBJP,
        objp_icp_lock: EICPLockStatus | int = EICPLockStatus.NOT_LOCKED,
        derby_icp_lock: EICPLockStatus | int = EICPLockStatus.NOT_LOCKED,
        er_derby_icp_lock: EICPLockStatus | int = EICPLockStatus.NOT_LOCKED,
        lr_derby_icp_lock: EICPLockStatus | int = EICPLockStatus.NOT_LOCKED,
        icp_stage: EICPStage | int = EICPStage.ON_GROUND,
        lock_type: ELockingPolicy | int = ELockingPolicy.LOAL,
        misfire_indication: ENoYesBoolean | int = ENoYesBoolean.NO,
        misfire_reason: ECOPMisfireReason | int = ECOPMisfireReason.EMPTY,
    ):
        """Construct"""
        if isinstance(icp_allocated, int):
            self._icp_allocated = ETypeOfICP(icp_allocated)
        else:
            self._icp_allocated = icp_allocated

        if isinstance(objp_icp_lock, int):
            self._objp_icp_lock = EICPLockStatus(objp_icp_lock)
        else:
            self._objp_icp_lock = objp_icp_lock

        if isinstance(derby_icp_lock, int):
            self._derby_icp_lock = EICPLockStatus(derby_icp_lock)
        else:
            self._derby_icp_lock = derby_icp_lock

        if isinstance(er_derby_icp_lock, int):
            self._er_derby_icp_lock = EICPLockStatus(er_derby_icp_lock)
        else:
            self._er_derby_icp_lock = er_derby_icp_lock

        if isinstance(lr_derby_icp_lock, int):
            self._lr_derby_icp_lock = EICPLockStatus(lr_derby_icp_lock)
        else:
            self._lr_derby_icp_lock = lr_derby_icp_lock

        if isinstance(icp_stage, int):
            self._icp_stage = EICPStage(icp_stage)
        else:
            self._icp_stage = icp_stage

        if isinstance(lock_type, int):
            self._lock_type = ELockingPolicy(lock_type)
        else:
            self._lock_type = lock_type

        if isinstance(misfire_indication, int):
            self._misfire_indication = ENoYesBoolean(misfire_indication)
        else:
            self._misfire_indication = misfire_indication

        if isinstance(misfire_reason, int):
            self._misfire_reason = ECOPMisfireReason(misfire_reason)
        else:
            self._misfire_reason = misfire_reason

    @property
    def icp_allocated(self) -> ETypeOfICP:
        """getter"""
        return self._icp_allocated

    @icp_allocated.setter
    def icp_allocated(self, value: ETypeOfICP | int) -> None:
        """setter"""
        if isinstance(value, ETypeOfICP):
            self._icp_allocated = value
        else:
            self._icp_allocated = ETypeOfICP(value)

    @property
    def objp_icp_lock(self) -> EICPLockStatus:
        """getter"""
        return self._objp_icp_lock

    @objp_icp_lock.setter
    def objp_icp_lock(self, value: EICPLockStatus | int) -> None:
        """setter"""
        if isinstance(value, EICPLockStatus):
            self._objp_icp_lock = value
        else:
            self._objp_icp_lock = EICPLockStatus(value)

    @property
    def derby_icp_lock(self) -> EICPLockStatus:
        """getter"""
        return EICPLockStatus(self._derby_icp_lock)

    @derby_icp_lock.setter
    def derby_icp_lock(self, value: EICPLockStatus | int) -> None:
        """setter"""
        if isinstance(value, EICPLockStatus):
            self._derby_icp_lock = value
        else:
            self._derby_icp_lock = EICPLockStatus(value)

    @property
    def er_derby_icp_lock(self) -> EICPLockStatus:
        """getter"""
        return EICPLockStatus(self._er_derby_icp_lock)

    @er_derby_icp_lock.setter
    def er_derby_icp_lock(self, value: EICPLockStatus | int) -> None:
        """setter"""
        if isinstance(value, EICPLockStatus):
            self._er_derby_icp_lock = value
        else:
            self._er_derby_icp_lock = EICPLockStatus(value)

    @property
    def lr_derby_icp_lock(self) -> EICPLockStatus:
        """getter"""
        return EICPLockStatus(self._lr_derby_icp_lock)

    @lr_derby_icp_lock.setter
    def lr_derby_icp_lock(self, value: EICPLockStatus | int) -> None:
        """setter"""
        if isinstance(value, EICPLockStatus):
            self._lr_derby_icp_lock = value
        else:
            self._lr_derby_icp_lock = EICPLockStatus(value)

    @property
    def icp_stage(self) -> EICPStage:
        """getter"""
        return EICPStage(self._icp_stage)

    @icp_stage.setter
    def icp_stage(self, value: EICPStage | int) -> None:
        """setter"""
        if isinstance(value, EICPStage):
            self._icp_stage = value
        else:
            self._icp_stage = EICPStage(value)

    @property
    def lock_type(self) -> ELockingPolicy:
        """getter"""
        return ELockingPolicy(self._lock_type)

    @lock_type.setter
    def lock_type(self, value: ELockingPolicy | int) -> None:
        """setter"""
        if isinstance(value, ELockingPolicy):
            self._lock_type = value
        else:
            self._lock_type = ELockingPolicy(value)

    @property
    def misfire_indication(self) -> ENoYesBoolean:
        """getter"""
        return ENoYesBoolean(self._misfire_indication)

    @misfire_indication.setter
    def misfire_indication(self, value: ENoYesBoolean | int) -> None:
        """setter"""
        if isinstance(value, ENoYesBoolean):
            self._misfire_indication = value
        else:
            self._misfire_indication = ENoYesBoolean(value)

    @property
    def misfire_reason(self) -> ECOPMisfireReason:
        """getter"""
        return ECOPMisfireReason(self._misfire_reason)

    @misfire_reason.setter
    def misfire_reason(self, value: ECOPMisfireReason | int) -> None:
        """setter"""
        if isinstance(value, ECOPMisfireReason):
            self._misfire_reason = value
        else:
            self._misfire_reason = ECOPMisfireReason(value)

    def pack(self) -> bytes:
        """pack"""
        packbyte = (
            self._icp_allocated.value
            | (self._objp_icp_lock.value << 2)
            | (self._derby_icp_lock.value << 3)
            | (self._er_derby_icp_lock.value << 4)
            | (self._lr_derby_icp_lock.value << 5)
            | (self._icp_stage.value << 6)
            | (self._lock_type.value << 7)
        )
        packbyte_1 = self._misfire_indication.value | (self._misfire_reason.value << 1)

        data = struct.pack(COPAllocatedMissileStatus.FORMAT, packbyte, packbyte_1)
        return data

    @classmethod
    def unpack(cls, data: bytes) -> "COPAllocatedMissileStatus":
        """unpack"""
        (unpacked_byte_1, unpacked_byte_2) = struct.unpack(cls.FORMAT, data[: cls.SIZE])
        icp_allocated = unpacked_byte_1 & 0x03
        objp_icp_lock = (unpacked_byte_1 >> 2) & 0x01
        derby_icp_lock = (unpacked_byte_1 >> 3) & 0x01
        er_derby_icp_lock = (unpacked_byte_1 >> 4) & 0x01
        lr_derby_icp_lock = (unpacked_byte_1 >> 5) & 0x01
        icp_stage = (unpacked_byte_1 >> 6) & 0x01
        lock_type = (unpacked_byte_1 >> 7) & 0x01
        misfire_indication = unpacked_byte_2 & 0x01
        misfire_reason = (unpacked_byte_2 >> 1) & 0x03
        return cls(
            icp_allocated,
            objp_icp_lock,
            derby_icp_lock,
            er_derby_icp_lock,
            lr_derby_icp_lock,
            icp_stage,
            lock_type,
            misfire_indication,
            misfire_reason,
        )

    def to_dict(self) -> dict:
        """to JSON"""
        return {
            "icp_allocated": self.icp_allocated.value,
            "objp_icp_lock": self.objp_icp_lock.value,
            "derby_icp_lock": self.derby_icp_lock.value,
            "er_derby_icp_lock": self.er_derby_icp_lock.value,
            "lr_derby_icp_lock": self.lr_derby_icp_lock.value,
            "icp_stage": self.icp_stage.value,
            "lock_type": self.lock_type.value,
            "misfire_indication": self.misfire_indication.value,
            "misfire_reason": self.misfire_reason.value,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "COPAllocatedMissileStatus":
        """From JSON"""
        return cls(
            data["icp_allocated"],
            data["objp_icp_lock"],
            data["derby_icp_lock"],
            data["er_derby_icp_lock"],
            data["lr_derby_icp_lock"],
            data["icp_stage"],
            data["lock_type"],
            data["misfire_indication"],
            data["misfire_reason"],
        )

    @classmethod
    def get_size(cls) -> int:
        """get_size"""
        return cls.SIZE

    def __eq__(self, other) -> bool:
        """Compare"""
        if not isinstance(other, COPAllocatedMissileStatus):
            return False
        return all(
            getattr(self, attr) == getattr(other, attr)
            for attr in [
                "icp_allocated",
                "objp_icp_lock",
                "derby_icp_lock",
                "er_derby_icp_lock",
                "lr_derby_icp_lock",
                "icp_stage",
                "lock_type",
                "misfire_indication",
                "misfire_reason",
            ]
        )

    def __str__(self) -> str:
        """Description"""
        return (
            f"COPAllocatedMissileStatus - icp_allocated: {self.icp_allocated}, "
            f"objp_icp_lock: {self.objp_icp_lock}, "
            f"derby_icp_lock: {self.derby_icp_lock}, "
            f"er_derby_icp_lock: {self.er_derby_icp_lock}, "
            f"lr_derby_icp_lock: {self.lr_derby_icp_lock}, "
            f"icp_stage: {self.icp_stage}, "
            f"lock_type: {self.lock_type}, "
            f"misfire_indication: {self.misfire_indication}, "
            f"misfire_reason: {self.misfire_reason}"
        )


class COPEngagementStatus(SupportPack):
    """COPEngagementStatus"""

    FORMAT = "<HHH"
    SIZE = struct.calcsize(FORMAT)
    FORMAT2 = "<LH"
    FORMAT3 = "<B"

    def __init__(
        self, track_id: int, ccu_engagement_id: int, cop_engagement_id: int
    ) -> None:
        """Construct"""
        self.track_id = track_id
        self.ccu_engagement_id = ccu_engagement_id
        self.cop_engagement_id = cop_engagement_id
        self.mcu_engagement = COPMCUEngagement()
        self.allocated_missile_status = COPAllocatedMissileStatus()
        self.engagement_launch_time = 0
        self.turret_launching_azimuth = 0
        self.mcu_engagement_spare = COPMCUEngagementSpare()
        self.coupled_station_id = 0

    def pack(self) -> bytes:
        """pack"""
        data = struct.pack(
            COPEngagementStatus.FORMAT,
            self.track_id,
            self.ccu_engagement_id,
            self.cop_engagement_id,
        )
        data += self.mcu_engagement.pack()
        data += self.allocated_missile_status.pack()
        data += struct.pack(
            COPEngagementStatus.FORMAT2,
            self.engagement_launch_time,
            self.turret_launching_azimuth,
        )
        data += self.mcu_engagement_spare.pack()
        data += struct.pack(COPEngagementStatus.FORMAT3, self.coupled_station_id)
        return data

    @classmethod
    def unpack(cls, data: bytes) -> "COPEngagementStatus":
        """unpack"""
        (track_id, ccu_engagement_id, cop_engagement_id) = struct.unpack(
            cls.FORMAT, data[: cls.SIZE]
        )
        sta = cls(track_id, ccu_engagement_id, cop_engagement_id)
        data = data[cls.SIZE :]
        sta.mcu_engagement = COPMCUEngagement.unpack(
            data[: COPMCUEngagement.get_size()]
        )
        data = data[COPMCUEngagement.get_size() :]
        sta.allocated_missile_status = COPAllocatedMissileStatus.unpack(
            data[: COPAllocatedMissileStatus.get_size()]
        )
        data = data[COPAllocatedMissileStatus.get_size() :]
        posun = struct.calcsize(COPEngagementStatus.FORMAT2)
        (engagement_launch_time, turret_launching_azimuth) = struct.unpack(
            COPEngagementStatus.FORMAT2, data[:posun]
        )
        sta.engagement_launch_time = engagement_launch_time
        sta.turret_launching_azimuth = turret_launching_azimuth
        data = data[posun:]
        sta.mcu_engagement_spare = COPMCUEngagementSpare.unpack(
            data[: COPMCUEngagementSpare.get_size()]
        )
        posun = struct.calcsize(COPEngagementStatus.FORMAT3)
        (coupled_id,) = struct.unpack(COPEngagementStatus.FORMAT3, data[:posun])
        sta.coupled_station_id = coupled_id
        return sta

    def __eq__(self, other) -> bool:
        if not isinstance(other, COPEngagementStatus):
            return False
        return (
            self.track_id == other.track_id
            and self.ccu_engagement_id == other.ccu_engagement_id
            and self.cop_engagement_id == other.cop_engagement_id
            and self.mcu_engagement == other.mcu_engagement
            and self.allocated_missile_status == other.allocated_missile_status
            and self.engagement_launch_time == other.engagement_launch_time
            and self.turret_launching_azimuth == other.turret_launching_azimuth
            and self.mcu_engagement_spare == other.mcu_engagement_spare
            and self.coupled_station_id == other.coupled_station_id
        )

    def __hash__(self):
        return hash(
            (
                self.track_id,
                self.ccu_engagement_id,
                self.cop_engagement_id,
                self.mcu_engagement,
                self.allocated_missile_status,
                self.engagement_launch_time,
                self.turret_launching_azimuth,
                self.mcu_engagement_spare,
                self.coupled_station_id,
            )
        )

    def to_dict(self) -> dict:
        """To JSON"""
        return {
            "track_id": self.track_id,
            "ccu_engagement_id": self.ccu_engagement_id,
            "cop_engagement_id": self.cop_engagement_id,
            "mcu_engagement": self.mcu_engagement.to_dict(),
            "allocated_missile_status": self.allocated_missile_status.to_dict(),
            "engagement_launch_time": self.engagement_launch_time,
            "turret_launching_azimuth": self.turret_launching_azimuth,
            "mcu_engagement_spare": self.mcu_engagement_spare.to_dict(),
            "coupled_station_id": self.coupled_station_id,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "COPEngagementStatus":
        """From JSON"""
        status = cls(
            data["track_id"],
            data["ccu_engagement_id"],
            data["cop_engagement_id"],
        )
        status.mcu_engagement = COPMCUEngagement.from_dict(data["mcu_engagement"])
        status.allocated_missile_status = COPAllocatedMissileStatus.from_dict(
            data["allocated_missile_status"]
        )
        status.engagement_launch_time = data["engagement_launch_time"]
        status.turret_launching_azimuth = data["turret_launching_azimuth"]
        status.mcu_engagement_spare = COPMCUEngagementSpare.from_dict(
            data["mcu_engagement_spare"]
        )
        status.coupled_station_id = data["coupled_station_id"]
        return status

    @classmethod
    def get_size(cls) -> int:
        """get_size"""
        return (
            cls.SIZE
            + COPMCUEngagement.SIZE
            + COPAllocatedMissileStatus.SIZE
            + struct.calcsize(cls.FORMAT2)
            + COPMCUEngagementSpare.SIZE
            + struct.calcsize(cls.FORMAT3)
        )

    def __str__(self) -> str:
        """Description"""
        return (
            f"COP_Engagement_Status: Track_ID {self.track_id},"
            f"CCU_Engagement_ID: {self.ccu_engagement_id},"
            f"COP_Engagement_ID:{self.cop_engagement_id}\n"
            f"{self.mcu_engagement}"
            f"{self.allocated_missile_status}"
            f"engagement_launch_time: {self.engagement_launch_time},"
            f" turret_launching_azimuth: {self.turret_launching_azimuth}\n"
            f"{self.mcu_engagement_spare}"
            f"coupled_station_id: {self.coupled_station_id}"
        )


class COPC2EngagementStatus(COPMessage):
    """COPC2EngagementStatus"""

    FORMAT = "<B"
    SIZE = struct.calcsize(FORMAT)

    MSG_TYPE = CopMessageType.COP_C2_Engagement_Status

    def __init__(self) -> None:
        """Construct"""
        super().__init__()
        self.number_of_engagements = 0
        self.eng_status_arr: list[COPEngagementStatus] = []
        self.spare = 0

    def add_eng_status(self, status: COPEngagementStatus) -> None:
        """add_eng_status"""
        self.eng_status_arr.append(status)
        self.number_of_engagements += 1

    def pack(self) -> bytes:
        """pack"""
        self.number_of_engagements = len(self.eng_status_arr)
        data = struct.pack(COPC2EngagementStatus.FORMAT, self.number_of_engagements)
        for eng in self.eng_status_arr:
            data += eng.pack()
        data += struct.pack("<B", self.spare)
        return data

    @classmethod
    def unpack(cls, data: bytes) -> "COPC2EngagementStatus":
        """unpack"""
        (number_of_engagements,) = struct.unpack(cls.FORMAT, data[: cls.SIZE])
        msg = cls()
        data = data[cls.SIZE :]
        for _i in range(number_of_engagements):
            msg.add_eng_status(
                COPEngagementStatus.unpack(data[: COPEngagementStatus.get_size()])
            )
            data = data[COPEngagementStatus.get_size() :]
        return msg

    def to_dict(self) -> dict:
        """To JSON"""
        return {
            "eng_statuses": [
                eng_status.to_dict() for eng_status in self.eng_status_arr
            ],
        }

    @classmethod
    def from_dict(cls, data: dict) -> "COPC2EngagementStatus":
        """From JSON"""
        c2_eng_status = cls()
        for eng_status_data in data["eng_statuses"]:
            c2_eng_status.add_eng_status(COPEngagementStatus.from_dict(eng_status_data))

        return c2_eng_status

    def __eq__(self, other) -> bool:
        if not isinstance(other, COPC2EngagementStatus):
            return False
        return all(
            getattr(self, attr) == getattr(other, attr)
            for attr in ["number_of_engagements", "eng_status_arr"]
        )

    def __str__(self) -> str:
        """Description"""
        data = (
            "COP_C2_Engagement_Status: "
            f"number_of_engagements = {self.number_of_engagements}:\n"
        )
        for stat in self.eng_status_arr:
            data += stat.__str__()
        return data
