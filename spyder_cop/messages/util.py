#!/usr/bin/env python3
# coding:utf-8
"""
  Author:   --<>
  Purpose:
  Created: 03/04/2023


struct module:

x: pad byte
c: char (1 byte)
b: signed char (1 byte)
T_UINT8 - B: unsigned char (1 byte)
?: bool (1 byte)
h: short (2 bytes)
T_UINT16 - H: unsigned short (2 bytes)
i: int (4 bytes)
I: unsigned int (4 bytes)
T_SINT32 - l: long (4 bytes)
T_UINT32 - L: unsigned long (4 bytes)
q: long long (8 bytes)
Q: unsigned long long (8 bytes)
n: short (2 bytes, network byte order)
N: unsigned int (4 bytes, network byte order)
f: float (4 bytes)
d: double (8 bytes)
s: string (variable length)
p: pascal string (variable length)
P: void pointer (size depends on platform)

"""

import struct
from abc import abstractmethod
from enum import Enum
from typing import Protocol

CABIN_AZIMUTH_KOEF = 2.98023223876953e-06

# delostrelecke dilce na deg = 360/6400
# TURRET_AZIMUTH_KOEF = 0.05625
TURRET_AZIMUTH_KOEF = 1.0


class EHeaderSimFlag(Enum):
    """EHeaderSimFlag"""

    OPERATIONA_MODE = 0
    SIMULATION_MODE = 1


class ECOPOperability(Enum):
    """ECOPOperability"""

    OK = 0
    DEGRADED = 1
    FAULTY = 2


class ECOPMode(Enum):
    """ECOPMode"""

    MAINTENANCE = 0
    TRAINING_SETUP = 1
    TRAINING_SURVEILANCE = 2
    TRAINING_IMMEDIATE = 3
    TRAINING_CONFLICT = 4
    OPERATIONAL_SETUP = 5
    OPERATIONAL_SURVEILANCE = 6
    OPERATIONAL_IMMEDIATE = 7
    OPERATIONAL_CONFLICT = 8
    NAVIGATION = 9
    AUTONOMOUS = 10


class ECOPType(Enum):
    """Launcher type"""

    SR = 0
    MR = 1


class ECOPMCUState(Enum):
    """ECOPMCUState"""

    STANDBY = 0
    OPERATIONAL = 1
    MAINTENANCE = 2
    TRAINING = 3
    DISCONNECTED = 4


class ECOPReadinessToEngage(Enum):
    """E_COP_Readiness_To_Engage"""

    READY_TO_ENGAGE = 0
    DEGRADED_BUT_READY_TO_ENGAGE = 1
    NOT_READY_TO_ENGAGE = 2


class ECOPLMArmed(Enum):
    """ECOPLMArmed"""

    NOT_ARMED = 0
    ARMED = 1


class ECOPComponentStatus(Enum):
    """ECOPComponentStatus"""

    OK = 0
    FAIL = 1


class EDLQDataSource(Enum):
    """EDLQDataSource"""

    CBIT = 0
    IBIT_REQUEST = 1


class ECOPTopliteEngagementIndication(Enum):
    """E_COPTopliteEngagementIndication"""

    NO_ENGAGEMENT = 0
    GROUND_ENGAGEMENT = 1
    AIR_ENGAGEMENT = 2


class ECOPEngagementHandling(Enum):
    """ECOPEngagementHandling"""

    WILL_PRO = 0
    CANT_PRO = 1
    WILL_COMPLY = 2
    CANT_COMPLY = 3
    ENGAGEMENT_CANNOT_BE_CREATED = 4


class ECOPMCUEngagementStatus(Enum):
    """ECOPMCUEngagementStatus"""

    CLOSED = 0
    OPENED = 1
    ABORT_PENDING = 2


class ECOPEngagementAbortSource(Enum):
    """ECOPEngagementAbortSource"""

    NO_ABORT = 0
    CM = 1
    COP = 2
    CP = 3


class EICPLockStatus(Enum):
    """EICPLockStatus"""

    NOT_LOCKED = 0
    LOCKED = 1


class EICPStage(Enum):
    """EICPStage"""

    ON_GROUND = 0
    ON_AIR = 1


class ECOPMisfireReason(Enum):
    """ECOPMisfireReason"""

    EMPTY = 0
    LAUNCH_FAILED = 1
    MISFIRE = 2
    NO_FIRE = 3


class ECOPSeekerMode(Enum):
    """ECOPSeekerMode"""

    FREE = 0
    FOLLOWS_DESIGNATION = 1


class EASPTargetType(Enum):
    """EASPTargetType"""

    FIGHTER = 0
    HELICOPTER = 1
    UAV = 2
    CRUISE_MISSILE = 3
    SPARE = 4
    COMMERCIAL_JET = 5
    POS = 6
    PROPELLED_POS = 7
    LARGE_UAV = 8
    ICP = 100
    UNKNOWN = 255


class EASPTargetIdentity(Enum):
    """Target identity"""

    PENDING = 0
    HOSTILE = 1
    SUSPECT = 2
    UNKNOWN = 3
    NEUTRAL = 4
    ASSUMED_FRIEND = 5
    FRIEND = 6
    FAKER = 7
    JOKER = 8
    EXERCISE_PENDING = 9
    EXERCISE_UNKNOWN = 10
    EXERCISE_ASSUMED_FRIEND = 11
    EXERCISE_FRIEND = 12
    EXERCISE_NEUTRAL = 13


class EASPThreatLevel(Enum):
    """EASPThreatLevel"""

    NO_THREAT = 0
    ONE = 1
    TWO = 2
    THREE = 3
    FOUR = 4
    FIVE = 5
    SIX = 6
    SEVEN = 7
    EIGHT = 8
    NINE = 9
    TEN = 10


class EASPTrackingStatus(Enum):
    """EASPTrackingStatus"""

    NOT_TRACKED = 0
    LOST = 1
    TRACKED = 2


class EIFFMilitaryEmergency(Enum):
    """EIFFMilitaryEmergency"""

    NO_EMERGENCY = 0
    EMERGENCY = 1


class EIFFSpecialIdentification(Enum):
    """EIFFSpecialIdentification"""

    NO_IDENTIFICATION = 0
    SPECIAL_IDENTIFICATION = 1


class EIFFFoeFriend(Enum):
    """EIFFFoeFriend"""

    NO_NSM_INTERROGATION = 0
    FRIENDLY_TARGET = 1
    UNKNOWN_TARGET = 2
    NO_NSM_REPLY = 3


class EIFFJammer(Enum):
    """EIFFJammer"""

    NO_JAMMER = 0
    JAMMER = 1


class ESysRadarType(Enum):
    """ESysRadarType"""

    ELM2106 = 0
    A_MMR = 1
    B_GIRAF = 2
    C_TIRION3 = 3
    D_TIRION4 = 4
    E_TIRION5 = 5
    TOPLITE = 6
    MANUALTARGETNORADAROPER = 7
    ELM2138 = 8


class EC2Mode(Enum):
    """EC2Mode"""

    NOT_RELEVENT = 0
    OPERATIONAL = 1
    SIMULATION = 2


class EC2Substate(Enum):
    """EC2Substate"""

    UNKNOWN = 0
    SETUP = 1
    SURVEILLANCE = 2
    IMMEDIATE = 3


class EC2SafetyState(Enum):
    """EC2SafetyState"""

    UNKNOWN = 0
    DISARMED = 1
    ARMED = 2


class EC2FireReadinessStatus(Enum):
    """EC2FireReadinessStatus"""

    UNKNOWN = 0
    READY = 1
    NOT_READY = 2


class EComponentStatus(Enum):
    """EComponentStatus"""

    DISCONNECTED = 0
    OK = 1
    DEGRADED = 2
    FAULTY = 3


class ERadarType(Enum):
    """ERadarType"""

    MMR = 0
    ATAR_FCR = 1
    ATAR_SSR = 2
    ELM2138 = 3


class EIBITAction(Enum):
    """EIBITAction"""

    STOP = 0
    START = 1


class EInitLinkType(Enum):
    """EInitLinkType"""

    WIRED = 0
    WIRELESS = 1


class ELockingPolicy(Enum):
    """ELockingPolicy"""

    LOBL = 0
    LOAL = 1


class EInitFireSource(Enum):
    """EInitFireSource"""

    LOCAL = 0
    REMOTE = 1


class ENoYesBoolean(Enum):
    """ENoYesBoolean"""

    NO = 0
    YES = 1


class EEngagementPriority(Enum):
    """EEngagementPriority"""

    HIGH = 0
    LOW = 1


class ETypeOfICP(Enum):
    """ETypeOfICP"""

    OBJP = 0
    DERBY = 1
    ER_DERBY = 2
    LR_DERBY = 3


class EC2EngagementRequest(Enum):
    """EC2EngagementRequest"""

    NEW = 0
    UPDATE = 1
    DELETE_GROUND_ENGAGEMENT = 2
    DELETE_AIR_ENGAGEMENT = 3


class EPlanStatus(Enum):
    """EPlanStatus"""

    PLAN_EXISTS = 0
    NO_VALID_PLAN = 1
    NOT_IN_OVERALL_SOLUTION = 2
    NOT_CALCULATED_YET = 3


class EEngagementStatus(Enum):
    """EEngagementStatus"""

    PLAN_ONLY = 0
    PRELAUNCH = 1
    SENT_TO_MFU = 2
    ON_AIR = 3
    AFTER_FLYBY = 4
    AFTER_ABORT = 5
    CLOSED = 6


class EEngagementFireControl1(Enum):
    """EEngagementFireControl1"""

    NORMAL = 0
    SALVO = 1


class EEngagementFireControl2(Enum):
    """EEngagementFireControl2"""

    HOLD = 0
    READY = 1
    ENABLE = 2


def convert_bool_to_enum_bool(value: bool) -> ENoYesBoolean:
    if value:
        return ENoYesBoolean.YES
    return ENoYesBoolean.NO


class CopMessageType(Enum):
    """CopMessageType"""

    C2_COP_ASP_Track = 0x01
    C2_COP_Engaged_Track = 0x02
    C2_COP_Status = 0x04
    C2_COP_GSP = 0x06
    C2_COP_MslsLocationFromRdr = 0x07
    C2_COP_Chat = 0x0A
    C2_COP_Engagement = 0x0B
    C2_COP_Pointer = 0x0C
    C2_COP_Training_Failures = 0x71
    C2_COP_Training_Settings = 0x72

    COP_C2_Status = 0x08
    COP_C2_Engagement_Status = 0x81
    COP_C2_Msl_DL = 0x86
    COP_C2_Msl_Metry = 0x87
    COP_C2_Toplite_Track = 0x88
    COP_C2_Pointer = 0x8C


class COPMessage:
    """COPMessage"""

    MSG_TYPE: CopMessageType

    @classmethod
    def get_msg_type(cls) -> int:
        """get_msg_type"""
        return cls.MSG_TYPE.value

    @abstractmethod
    def pack(self) -> bytes:
        """Pack Datagram

        Returns:
            bytes: _description_
        """


class SupportPack(Protocol):
    """SupportPack"""

    def pack(self) -> bytes:
        """pack"""
        ...


class ECEFUncertainty(SupportPack):
    """ECEFUncertainty"""

    ECEF_uncertainty_FORMAT = "<ffffff"
    ECEF_uncertainty_SIZE = struct.calcsize(ECEF_uncertainty_FORMAT)

    JSON_VAR_XX = "var_xx"
    JSON_COV_XY = "cov_xy"
    JSON_COV_XZ = "cov_xz"
    JSON_VAR_YY = "var_yy"
    JSON_COV_YZ = "cov_yz"
    JSON_VAR_ZZ = "var_zz"

    def __init__(
        self,
        position_var_xx: float,
        position_cov_xy: float,
        position_cov_xz: float,
        position_var_yy: float,
        position_cov_yz: float,
        position_var_zz: float,
    ) -> None:
        """Constructor"""
        self.position_var_xx = position_var_xx
        self.position_cov_xy = position_cov_xy
        self.position_cov_xz = position_cov_xz
        self.position_var_yy = position_var_yy
        self.position_cov_yz = position_cov_yz
        self.position_var_zz = position_var_zz

    def pack(self) -> bytes:
        """pack"""
        return struct.pack(
            ECEFUncertainty.ECEF_uncertainty_FORMAT,
            self.position_var_xx,
            self.position_cov_xy,
            self.position_cov_xz,
            self.position_var_yy,
            self.position_cov_yz,
            self.position_var_zz,
        )

    @classmethod
    def unpack(cls, data: bytes) -> "ECEFUncertainty":
        """unpack"""
        (
            position_var_xx,
            position_cov_xy,
            position_cov_xz,
            position_var_yy,
            position_cov_yz,
            position_var_zz,
        ) = struct.unpack(
            cls.ECEF_uncertainty_FORMAT,
            data[: cls.ECEF_uncertainty_SIZE],
        )
        return cls(
            position_var_xx,
            position_cov_xy,
            position_cov_xz,
            position_var_yy,
            position_cov_yz,
            position_var_zz,
        )

    def to_dict(self) -> dict:
        """Converts object attributes to a dictionary"""
        return {
            ECEFUncertainty.JSON_VAR_XX: self.position_var_xx,
            ECEFUncertainty.JSON_VAR_YY: self.position_var_yy,
            ECEFUncertainty.JSON_VAR_ZZ: self.position_var_zz,
            ECEFUncertainty.JSON_COV_XY: self.position_cov_xy,
            ECEFUncertainty.JSON_COV_XZ: self.position_cov_xz,
            ECEFUncertainty.JSON_COV_YZ: self.position_cov_yz,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "ECEFUncertainty":
        """Creates an instance from a dictionary"""
        return cls(
            data[ECEFUncertainty.JSON_VAR_XX],
            data[ECEFUncertainty.JSON_COV_XY],
            data[ECEFUncertainty.JSON_COV_XZ],
            data[ECEFUncertainty.JSON_VAR_YY],
            data[ECEFUncertainty.JSON_COV_YZ],
            data[ECEFUncertainty.JSON_VAR_ZZ],
        )

    @classmethod
    def get_size(cls) -> int:
        """get_size"""
        return cls.ECEF_uncertainty_SIZE

    def __eq__(self, other) -> bool:
        """Check if two ECEFPosition objects are equal"""
        if not isinstance(other, ECEFUncertainty):
            return False
        return (
            self.position_var_xx == other.position_var_xx
            and self.position_cov_xy == other.position_cov_xy
            and self.position_cov_xz == other.position_cov_xz
            and self.position_cov_yz == other.position_cov_yz
            and self.position_var_yy == other.position_var_yy
            and self.position_var_zz == other.position_var_zz
        )

    def __str__(self) -> str:
        """description"""
        return (
            f"ECEF_uncertainty: \n"
            f"{self.position_var_xx} {self.position_cov_xy} {self.position_cov_xz}\n"
            f"{self.position_cov_xy} {self.position_var_yy} {self.position_cov_yz}\n"
            f"{self.position_cov_xz} {self.position_cov_xz} {self.position_var_zz}\n"
        )


class LLLNPosition(SupportPack):
    """LLLNPosition"""

    LLLN_Position_FORMAT = "<fff"
    LLLN_Position_SIZE = struct.calcsize(LLLN_Position_FORMAT)

    JSON_X_POS = "x_pos"
    JSON_Y_POS = "y_pos"
    JSON_Z_POS = "z_pos"

    def __init__(self, x_pos: float, y_pos: float, z_pos: float) -> None:
        """Constructor"""
        self.x_pos = x_pos
        self.y_pos = y_pos
        self.z_pos = z_pos

    def pack(self) -> bytes:
        """pack"""
        return struct.pack(
            LLLNPosition.LLLN_Position_FORMAT, self.x_pos, self.y_pos, self.z_pos
        )

    @classmethod
    def unpack(cls, data: bytes) -> "LLLNPosition":
        """unpack"""
        (pos_x, pos_y, pos_z) = struct.unpack(
            cls.LLLN_Position_FORMAT, data[: cls.LLLN_Position_SIZE]
        )
        return cls(pos_x, pos_y, pos_z)

    def to_dict(self) -> dict:
        """Converts object attributes to a dictionary"""
        return {
            LLLNPosition.JSON_X_POS: self.x_pos,
            LLLNPosition.JSON_Y_POS: self.y_pos,
            LLLNPosition.JSON_Z_POS: self.z_pos,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "LLLNPosition":
        """Creates an instance from a dictionary"""
        return cls(
            data[LLLNPosition.JSON_X_POS],
            data[LLLNPosition.JSON_Y_POS],
            data[LLLNPosition.JSON_Z_POS],
        )

    @classmethod
    def get_size(cls) -> int:
        """get_size"""
        return cls.LLLN_Position_SIZE

    def __eq__(self, other) -> bool:
        """Check if two ECEFPosition objects are equal"""
        if not isinstance(other, LLLNPosition):
            return False
        return (
            self.x_pos == other.x_pos
            and self.y_pos == other.y_pos
            and self.z_pos == other.z_pos
        )

    def __str__(self) -> str:
        """Description"""
        return f"LLLN_Position: [{self.x_pos}, {self.y_pos}, {self.z_pos}]"


class ECEFPosition(SupportPack):
    """ECEFPosition"""

    ECEF_Position_FORMAT = "<lll"
    ECEF_Position_SIZE = struct.calcsize(ECEF_Position_FORMAT)

    JSON_X_POS = "x_pos"
    JSON_Y_POS = "y_pos"
    JSON_Z_POS = "z_pos"

    def __init__(self, x_pos: int, y_pos: int, z_pos: int) -> None:
        """Constructor"""
        self.x_pos = x_pos
        self.y_pos = y_pos
        self.z_pos = z_pos

    def pack(self) -> bytes:
        """pack"""
        return struct.pack(
            ECEFPosition.ECEF_Position_FORMAT, self.x_pos, self.y_pos, self.z_pos
        )

    @classmethod
    def unpack(cls, data: bytes) -> "ECEFPosition":
        """unpack"""
        (pos_x, pos_y, pos_z) = struct.unpack(
            cls.ECEF_Position_FORMAT, data[: cls.ECEF_Position_SIZE]
        )
        return cls(pos_x, pos_y, pos_z)

    def to_dict(self) -> dict:
        """Converts object attributes to a dictionary"""
        return {
            ECEFPosition.JSON_X_POS: self.x_pos,
            ECEFPosition.JSON_Y_POS: self.y_pos,
            ECEFPosition.JSON_Z_POS: self.z_pos,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "ECEFPosition":
        """Creates an instance from a dictionary"""
        return cls(
            data[ECEFPosition.JSON_X_POS],
            data[ECEFPosition.JSON_Y_POS],
            data[ECEFPosition.JSON_Z_POS],
        )

    @classmethod
    def get_size(cls) -> int:
        """get_size"""
        return cls.ECEF_Position_SIZE

    def __eq__(self, other) -> bool:
        """Check if two ECEFPosition objects are equal"""
        if not isinstance(other, ECEFPosition):
            return False
        return (
            self.x_pos == other.x_pos
            and self.y_pos == other.y_pos
            and self.z_pos == other.z_pos
        )

    def __str__(self) -> str:
        """Description"""
        return f"ECEF_Position: [{self.x_pos}, {self.y_pos}, {self.z_pos}]"


class ECEFPositionAndTime(SupportPack):
    """ECEFPositionAndTime"""

    ECEF_Position_And_Time_SIZE = struct.calcsize("<lllL")
    ECEF_Position_And_Time_Add_Format = "<L"

    JSON_X_POS = "x_pos"
    JSON_Y_POS = "y_pos"
    JSON_Z_POS = "z_pos"
    JSON_REL_TIME = "rel_time"

    def __init__(self, x_pos: int, y_pos: int, z_pos: int, rel_time: int) -> None:
        """Constructor"""
        self.x_pos = x_pos
        self.y_pos = y_pos
        self.z_pos = z_pos
        self.position = ECEFPosition(x_pos, y_pos, z_pos)
        self.rel_time = rel_time

    def pack(self) -> bytes:
        """pack"""
        data = self.position.pack()
        data += struct.pack(
            ECEFPositionAndTime.ECEF_Position_And_Time_Add_Format, self.rel_time
        )
        return data

    @classmethod
    def unpack(cls, data: bytes) -> "ECEFPositionAndTime":
        """unpack"""
        position = ECEFPosition.unpack(data[: ECEFPosition.get_size()])
        nextdata = data[ECEFPosition.get_size() :]
        (rel_time,) = struct.unpack(
            cls.ECEF_Position_And_Time_Add_Format,
            nextdata[: struct.calcsize(cls.ECEF_Position_And_Time_Add_Format)],
        )
        return cls(position.x_pos, position.y_pos, position.z_pos, rel_time)

    def to_dict(self) -> dict:
        """Converts object attributes to a dictionary"""
        return {
            ECEFPositionAndTime.JSON_X_POS: self.x_pos,
            ECEFPositionAndTime.JSON_Y_POS: self.y_pos,
            ECEFPositionAndTime.JSON_Z_POS: self.z_pos,
            ECEFPositionAndTime.JSON_REL_TIME: self.rel_time,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "ECEFPositionAndTime":
        """Creates an instance from a dictionary"""
        return cls(
            data[ECEFPositionAndTime.JSON_X_POS],
            data[ECEFPositionAndTime.JSON_Y_POS],
            data[ECEFPositionAndTime.JSON_Z_POS],
            data[ECEFPositionAndTime.JSON_REL_TIME],
        )

    @classmethod
    def get_size(cls) -> int:
        """get_size"""
        return cls.ECEF_Position_And_Time_SIZE

    def __eq__(self, other) -> bool:
        """Check if two ECEFPosition objects are equal"""
        if not isinstance(other, ECEFPositionAndTime):
            return False
        return (
            self.x_pos == other.x_pos
            and self.y_pos == other.y_pos
            and self.z_pos == other.z_pos
            and self.rel_time == other.rel_time
        )

    def __str__(self) -> str:
        """Description"""
        return (
            f"ECEF_Position_And_Time: "
            f"[{self.x_pos}, {self.y_pos}, {self.z_pos}] - {self.rel_time}"
        )


class RadarInfo(SupportPack):
    """RadarInfo"""

    Radar_Info_SIZE = struct.calcsize("<lllB B")
    Radar_Info_Add_Format = "<B B"

    JSON_X_POS = "x_pos"
    JSON_Y_POS = "y_pos"
    JSON_Z_POS = "z_pos"
    JSON_RADAR_TYPE = "radar_type"
    JSON_VALID_LOCATION = "valid_location"

    def __init__(
        self,
        x_pos: int,
        y_pos: int,
        z_pos: int,
        radar_type: int | ERadarType = ERadarType.MMR,
        valid_location: int | ENoYesBoolean = ENoYesBoolean.YES,
    ) -> None:
        """Constructor"""
        self.x_pos = x_pos
        self.y_pos = y_pos
        self.z_pos = z_pos
        self.position = ECEFPosition(x_pos, y_pos, z_pos)
        if isinstance(radar_type, int):
            self._radar_type = ERadarType(radar_type)
        else:
            self._radar_type = radar_type
        if isinstance(valid_location, int):
            self._valid_location = ENoYesBoolean(valid_location)
        else:
            self._valid_location = valid_location
        self._spare7bits: int = 0

    @property
    def radar_type(self) -> ERadarType:
        """getter"""
        return ERadarType(self._radar_type)

    @radar_type.setter
    def radar_type(self, value: ERadarType | int) -> None:
        """setter"""
        if isinstance(value, ERadarType):
            self._radar_type = value
        else:
            self._radar_type = ERadarType(value)

    @property
    def valid_location(self) -> ENoYesBoolean:
        """getter"""
        return self._valid_location

    @valid_location.setter
    def valid_location(self, value: bool | ENoYesBoolean) -> None:
        """setter"""
        if isinstance(value, bool):
            self._valid_location = ENoYesBoolean(value)
        else:
            self._valid_location = value

    def pack(self) -> bytes:
        """pack"""
        data = self.position.pack()
        valid_spare_byte = self._valid_location.value << 7 | self._spare7bits
        data += struct.pack(
            RadarInfo.Radar_Info_Add_Format, self._radar_type.value, valid_spare_byte
        )
        return data

    @classmethod
    def unpack(cls, data: bytes) -> "RadarInfo":
        """unpack"""
        position = ECEFPosition.unpack(data[: ECEFPosition.get_size()])
        nextdata = data[ECEFPosition.get_size() :]
        (
            radar_type,
            valid_spare_byte,
        ) = struct.unpack(
            cls.Radar_Info_Add_Format,
            nextdata[: struct.calcsize(cls.Radar_Info_Add_Format)],
        )
        valid_location = (valid_spare_byte & 0x80) >> 7
        return cls(
            position.x_pos, position.y_pos, position.z_pos, radar_type, valid_location
        )

    def to_dict(self) -> dict:
        """Converts object attributes to a dictionary"""
        return {
            RadarInfo.JSON_X_POS: self.x_pos,
            RadarInfo.JSON_Y_POS: self.y_pos,
            RadarInfo.JSON_Z_POS: self.z_pos,
            RadarInfo.JSON_RADAR_TYPE: self.radar_type.value,
            RadarInfo.JSON_VALID_LOCATION: self.valid_location.value,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "RadarInfo":
        """Creates an instance from a dictionary"""
        return cls(
            data[RadarInfo.JSON_X_POS],
            data[RadarInfo.JSON_Y_POS],
            data[RadarInfo.JSON_Z_POS],
            data[RadarInfo.JSON_RADAR_TYPE],
            data[RadarInfo.JSON_VALID_LOCATION],
        )

    @classmethod
    def get_size(cls) -> int:
        """get_size"""
        return cls.Radar_Info_SIZE

    def __eq__(self, other) -> bool:
        """Check if two ECEFPosition objects are equal"""
        if not isinstance(other, RadarInfo):
            return False
        return (
            self.x_pos == other.x_pos
            and self.y_pos == other.y_pos
            and self.z_pos == other.z_pos
            and self.radar_type == other.radar_type
            and self.valid_location == other.valid_location
        )

    def __str__(self) -> str:
        """Description"""
        return (
            f"Radar_Info: \n"
            f"Location [{self.x_pos}, {self.y_pos}, {self.z_pos}], \n"
            f"Type: {self.radar_type}, valid_location: {self.valid_location}"
        )


class ECEFVelocity(SupportPack):
    """ECEFVelocity"""

    ECEF_Velocity_FORMAT = "<lll"
    ECEF_Velocity_SIZE = struct.calcsize(ECEF_Velocity_FORMAT)
    ScaleFactor = 0.01

    JSON_X_POS = "x_pos"
    JSON_Y_POS = "y_pos"
    JSON_Z_POS = "z_pos"

    def __init__(self, x_pos: int, y_pos: int, z_pos: int) -> None:
        """Constructor"""
        self.x_pos = x_pos
        self.y_pos = y_pos
        self.z_pos = z_pos

    def pack(self) -> bytes:
        """pack"""
        return struct.pack(
            ECEFVelocity.ECEF_Velocity_FORMAT, self.x_pos, self.y_pos, self.z_pos
        )

    @classmethod
    def unpack(cls, data: bytes) -> "ECEFVelocity":
        """unpack"""
        (pos_x, pos_y, pos_z) = struct.unpack(
            cls.ECEF_Velocity_FORMAT, data[: cls.ECEF_Velocity_SIZE]
        )
        return cls(pos_x, pos_y, pos_z)

    def to_dict(self) -> dict:
        """Converts object attributes to a dictionary"""
        return {
            ECEFVelocity.JSON_X_POS: self.x_pos,
            ECEFVelocity.JSON_Y_POS: self.y_pos,
            ECEFVelocity.JSON_Z_POS: self.z_pos,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "ECEFVelocity":
        """Creates an instance from a dictionary"""
        return cls(
            data[ECEFVelocity.JSON_X_POS],
            data[ECEFVelocity.JSON_Y_POS],
            data[ECEFVelocity.JSON_Z_POS],
        )

    @classmethod
    def get_size(cls) -> int:
        """get_size"""
        return cls.ECEF_Velocity_SIZE

    def __eq__(self, other) -> bool:
        """Check if two ECEFPosition objects are equal"""
        if not isinstance(other, ECEFVelocity):
            return False
        return (
            self.x_pos == other.x_pos
            and self.y_pos == other.y_pos
            and self.z_pos == other.z_pos
        )

    def __str__(self) -> str:
        """Description"""
        return (
            f"ECEF_Velocity: [{self.x_pos * ECEFVelocity.ScaleFactor}, "
            f"{self.y_pos * ECEFVelocity.ScaleFactor}, "
            f"{self.z_pos * ECEFVelocity.ScaleFactor}]"
        )
