#!/usr/bin/env python3
# coding:utf-8
"""
Author:   --<>
Purpose:
Created: 03/04/2023
"""
import struct

from spyder_cop.messages.util import SupportPack

from .util import COPMessage, CopMessageType
from .util_method import check_range


class TripleShort(SupportPack):
    FORMAT = "<3h"
    SIZE = struct.calcsize(FORMAT)

    def __init__(
        self,
        x: int | float = 0,
        y: int | float = 0,
        z: int | float = 0,
        scale_factor: float = 10.0,
    ) -> None:
        self.x: int = check_range(x / scale_factor, "h")
        self.y: int = check_range(y / scale_factor, "h")
        self.z: int = check_range(z / scale_factor, "h")

    def pack(self) -> bytes:
        """pack"""
        return struct.pack(TripleShort.FORMAT, self.x, self.y, self.z)

    @classmethod
    def unpack(cls, data: bytes) -> "TripleShort":
        """unpack"""
        (x, y, z) = struct.unpack(cls.FORMAT, data)
        return cls(x, y, z, 1.0)

    @staticmethod
    def get_size() -> int:
        return TripleShort.SIZE

    @classmethod
    def from_dict(cls, data: dict) -> "TripleShort":
        """create from dict"""
        return cls(
            data.get("x", 0),
            data.get("y", 0),
            data.get("z", 0),
            data.get("scale_factor", 1.0),
        )

    def to_dict(self) -> dict:
        """convert to dict"""
        return {
            "x": self.x,
            "y": self.y,
            "z": self.z,
            "scale_factor": 10.0,
        }


class TripleUnsignedShort(SupportPack):
    FORMAT = "<3H"
    SIZE = struct.calcsize(FORMAT)

    def __init__(
        self,
        x: int | float = 0,
        y: int | float = 0,
        z: int | float = 0,
        scale_factor: float = 10.0,
    ) -> None:
        self.x: int = check_range(x / scale_factor, "H")
        self.y: int = check_range(y / scale_factor, "H")
        self.z: int = check_range(z / scale_factor, "H")

    def pack(self) -> bytes:
        """pack"""
        return struct.pack(TripleShort.FORMAT, self.x, self.y, self.z)

    @classmethod
    def unpack(cls, data: bytes) -> "TripleUnsignedShort":
        """unpack"""
        (x, y, z) = struct.unpack(cls.FORMAT, data)
        return cls(x, y, z, 1.0)

    @staticmethod
    def get_size() -> int:
        return TripleShort.SIZE

    @classmethod
    def from_dict(cls, data: dict) -> "TripleUnsignedShort":
        """create from dict"""
        return cls(
            data.get("x", 0),
            data.get("y", 0),
            data.get("z", 0),
            data.get("scale_factor", 1.0),
        )

    def to_dict(self) -> dict:
        """convert to dict"""
        return {
            "x": self.x,
            "y": self.y,
            "z": self.z,
            "scale_factor": 10.0,
        }


# COP 3.4 - SMslDlType == SMslDlData
class SMslDlType(SupportPack):
    FORMAT = "<BiIH"
    SIZE = struct.calcsize(FORMAT)

    FORMAT2 = "<h BBB H b BBBB b BB b B 5I"
    SIZE2 = struct.calcsize(FORMAT2)

    # FORMAT2 - is not used. in further communication
    FORMAT2_SIMPLE = "<20s"
    SIZE2_SIMPLE = struct.calcsize(FORMAT2_SIMPLE)

    def __init__(
        self,
        msl_id: int,
        q_time_of_last_dl_msg_rcv: int,
        q_timefromml: int,
        q_servopressure: int,
        location: TripleShort | None = None,
        velocity: TripleShort | None = None,
        pos_uncert: TripleUnsignedShort | None = None,
        vel_uncert: TripleUnsignedShort | None = None,
    ) -> None:
        self.msl_id = check_range(msl_id, "B")
        self.q_time_of_last_dl_msg_rcv = check_range(q_time_of_last_dl_msg_rcv, "i")
        self.q_time_from_mml = check_range(q_timefromml, "I")
        self.q_servo_pressure = check_range(q_servopressure, "H")

        self.location = location if location is not None else TripleShort()
        self.velocity = velocity if velocity is not None else TripleShort()
        self.pos_uncert = (
            pos_uncert if pos_uncert is not None else TripleUnsignedShort()
        )
        self.vel_uncert = (
            vel_uncert if vel_uncert is not None else TripleUnsignedShort()
        )
        self.spare: bytes = bytes([0] * 20)

    @staticmethod
    def get_size() -> int:
        return (
            SMslDlType.SIZE
            + SMslDlType.SIZE2_SIMPLE
            + 2 * TripleShort.get_size()
            + 2 * TripleUnsignedShort.get_size()
        )

    def pack(self) -> bytes:
        """pack"""
        data: bytes = struct.pack(
            SMslDlType.FORMAT,
            self.msl_id,
            self.q_time_of_last_dl_msg_rcv,
            self.q_time_from_mml,
            self.q_servo_pressure,
        )
        data += self.location.pack()
        data += self.velocity.pack()
        data += self.pos_uncert.pack()
        data += self.vel_uncert.pack()
        data += struct.pack(SMslDlType.FORMAT2_SIMPLE, self.spare)
        return data

    @classmethod
    def unpack(cls, data: bytes) -> "SMslDlType":
        """unpack"""
        offset = 0
        (msl_id, nav_time, time_mll, servo) = struct.unpack_from(
            cls.FORMAT, data, offset
        )
        offset += struct.calcsize(cls.FORMAT)

        location = TripleShort.unpack(data[offset : offset + TripleShort.get_size()])
        offset += TripleShort.get_size()

        velocity = TripleShort.unpack(data[offset : offset + TripleShort.get_size()])
        offset += TripleShort.get_size()

        pos_uncert = TripleUnsignedShort.unpack(
            data[offset : offset + TripleUnsignedShort.get_size()]
        )
        offset += TripleUnsignedShort.get_size()

        vel_uncert = TripleUnsignedShort.unpack(
            data[offset : offset + TripleUnsignedShort.get_size()]
        )
        offset += TripleUnsignedShort.get_size()

        (spare,) = struct.unpack_from(cls.FORMAT2_SIMPLE, data, offset)

        obj = cls(
            msl_id,
            nav_time,
            time_mll,
            servo,
            location,
            velocity,
            pos_uncert,
            vel_uncert,
        )
        obj.spare = bytes(spare)
        return obj

    @classmethod
    def from_dict(cls, data: dict) -> "SMslDlType":
        """create from dict"""
        return cls(
            data.get("msl_id", 0),
            data.get("nav_time", 0),
            data.get("time_mll", 0),
            data.get("servo", 0),
            TripleShort.from_dict(data.get("location", {})),
            TripleShort.from_dict(data.get("velocity", {})),
            TripleUnsignedShort.from_dict(data.get("pos_uncert", {})),
            TripleUnsignedShort.from_dict(data.get("vel_uncert", {})),
        )

    def to_dict(self) -> dict:
        """convert to dict"""
        return {
            "msl_id": self.msl_id,
            "nav_time": self.q_time_of_last_dl_msg_rcv,
            "time_mll": self.q_time_from_mml,
            "servo": self.q_servo_pressure,
            "location": self.location.to_dict(),
            "velocity": self.velocity.to_dict(),
            "pos_uncert": self.pos_uncert.to_dict(),
            "vel_uncert": self.vel_uncert.to_dict(),
        }


class SMslsInAirDlData(SupportPack):
    FORMAT = "<H"

    MAX_ITEMS = 8

    def __init__(self) -> None:
        self.msl_array: set[SMslDlType] = set()
        self.msl_cnt = 0

    def add_msl(self, msl_data: SMslDlType) -> bool:
        for data in self.msl_array:
            if data.msl_id == msl_data.msl_id:
                return False
        else:
            if self.msl_cnt < SMslsInAirDlData.MAX_ITEMS:
                self.msl_array.add(msl_data)
                self.msl_cnt = len(self.msl_array)
                return True
        return False

    def get_msl_data(self, msl_id: int) -> SMslDlType | None:
        for data in self.msl_array:
            if data.msl_id == msl_id:
                return data
        else:
            return None

    def pack(self) -> bytes:
        """Pack"""
        data = struct.pack(SMslsInAirDlData.FORMAT, self.msl_cnt)
        for msl_data in self.msl_array:
            data += msl_data.pack()
        return bytes(data)

    @classmethod
    def unpack(cls, data: bytes) -> "SMslsInAirDlData":
        """Upack"""
        (msl_cnt,) = struct.unpack_from(cls.FORMAT, data)
        offset = struct.calcsize(cls.FORMAT)
        msl_data_list = []
        for _ in range(msl_cnt):
            msl_data = SMslDlType.unpack(data[offset : offset + SMslDlType.get_size()])
            msl_data_list.append(msl_data)
            offset += SMslDlType.get_size()
        obj = cls()
        obj.msl_cnt = msl_cnt
        obj.msl_array = set(msl_data_list)
        return obj

    @classmethod
    def from_dict(cls, data: dict) -> "SMslsInAirDlData":
        """Create from dict"""
        obj = cls()
        obj.msl_cnt = data.get("msl_cnt", 0)
        obj.msl_array = set()
        for data_item in data.get("msl_array", []):
            try:
                msl_data = SMslDlType.from_dict(data_item)
                obj.msl_array.add(msl_data)
            except KeyError:
                pass
        return obj

    def to_dict(self) -> dict:
        """Create dict"""
        return {
            "msl_cnt": self.msl_cnt,
            "msl_array": [msl_data.to_dict() for msl_data in self.msl_array],
        }


class COPC2MslDL(COPMessage):
    """COP_C2_Missile_DownLink"""

    MSG_TYPE = CopMessageType.COP_C2_Msl_DL

    def __init__(self) -> None:
        """constructor"""
        super().__init__()
        self.data: SMslsInAirDlData = SMslsInAirDlData()

    def add_msl_data(self, msl_data: SMslDlType) -> None:
        self.data.add_msl(msl_data)

    def pack(self) -> bytes:
        """pack"""
        return self.data.pack()

    @classmethod
    def unpack(cls, data: bytes) -> "COPC2MslDL":
        """unpack"""
        obj = cls()
        obj.data = SMslsInAirDlData.unpack(data)
        return obj

    @classmethod
    def from_dict(cls, data: dict) -> "COPC2MslDL":
        """Create from dict"""
        obj = cls()
        obj.data = SMslsInAirDlData.from_dict(data.get("data", {}))
        return obj

    def to_dict(self) -> dict:
        """Convert to dict"""
        return {"data": self.data.to_dict()}

    def __str__(self) -> str:
        """Description"""
        return f"COPC2MslDL: data   = {self.data}\n"
