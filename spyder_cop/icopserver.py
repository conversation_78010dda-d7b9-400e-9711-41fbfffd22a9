#!/usr/bin/env python3
# coding:utf-8
"""
Author:   --<>
Purpose:
Created: 03/10/2023
"""
from typing import Protocol

from spyder_simul.simulation import SimCommand

from spyder_cop.cop_data.ASPTrack import ASPTracks, EngagedTracks
from spyder_cop.cop_data.ComponentStatusWrapper import ComponentStatusWrapper
from spyder_cop.cop_data.CopNotReadyStatusWrapper import CopNotReadyStatusWrapper
from spyder_cop.cop_data.CopStateStatusWrapper import CopStateStatusWrapper
from spyder_cop.cop_data.engagement import Engagements, Inventory
from spyder_cop.cop_data.LmStatusWrapper import LmStatus<PERSON>rapper
from spyder_cop.cop_data.McuNotReadyStatusWrapper import McuNotReady<PERSON>tatusWrapper
from spyder_cop.cop_data.SafetyStatusWrapper import Safety
from spyder_cop.cop_data.stop_send import StopSending
from spyder_cop.messages.class_C2_COP_Status import (
    C2COPStatus,
    COPInitializationRequest,
)
from spyder_cop.messages.class_COP_C2_Status import COPC2Status
from spyder_cop.messages.util import ETypeOfICP, LLLNPosition


class ICOPServer(Protocol):
    """
    Interface to COP server.

    This class provides methods to retrieve various information
    related to the COP server, such as track counts, COP status,
    readiness, and component status.
    """

    def close_all_missiles_in_air(self) -> None:
        """close all missiles in the air ans close communication"""

    def get_current_sim_command(self) -> SimCommand:
        """Get latest simulation command"""

    def get_sim_config(self) -> str:
        """Get Current sim config"""

    def get_stop_send(self) -> StopSending:
        """Get Stop sending settings"""

    def increment_missiles(self, missile_type: ETypeOfICP) -> None:
        """Increment number of missiles by type"""

    def toggle_misfire_indicator(self, eng_id: int) -> None:
        """Toggle misfire indication"""

    def try_create_autonomous_engagement(self, id_plane: int) -> bool:
        """try to create autonomous engagement"""
        ...

    def get_mfu_logical_id(self) -> int:
        """Get MFU logical ID"""
        ...

    def get_mfu_physical_id(self) -> int:
        """Get MFU physical ID"""
        ...

    def get_mfu_mcu_serial_id(self) -> int:
        """Get MFU MCU serial ID"""
        ...

    def get_version(self) -> str:
        """Get version"""
        ...

    def get_inventory(self) -> Inventory:
        """
        Returns the inventory.

        Returns:
            Inventory: The inventory.
        """
        ...

    def reset_statistics(self) -> None:
        """
        Reset the statistics.
        """
        ...

    def get_statistics_report(self) -> str:
        """
        Returns the statistics report.

        Returns:
            str: The statistics report.
        """
        ...

    def reset(self) -> None:
        """
        Reset the COP server.
        """
        ...

    def get_engagements_cnt(self) -> int:
        """
        Returns the count of engagements.

        Returns:
            int: The count of engagements.
        """
        ...

    def get_engagements(self) -> Engagements:
        """
        Returns the engagements.

        Returns:
            Engagements: The engagements.
        """
        ...

    def get_asp_tracks(self) -> ASPTracks:
        """
        Returns the ASP tracks.

        Returns:
            ASPTracks: The ASP tracks.
        """
        ...

    def get_eng_tracks(self) -> EngagedTracks:
        """
        Returns the engaged tracks.

        Returns:
            EngagedTracks: The engaged tracks.
        """
        ...

    def get_bmc_status(self) -> C2COPStatus:
        """
        Returns the BMC status.

        Returns:
            C2COPStatus: The BMC status.
        """
        ...

    def get_cop_status(self) -> COPC2Status:
        """Get COP status"""

    def get_cop_state(self) -> CopStateStatusWrapper:
        """
        Returns the COP state.

        Returns:
            CopStateStatusWrapper: The COP state.
        """
        ...

    def get_cop_lm_readiness(self) -> LmStatusWrapper:
        """
        Returns the COP LM readiness.

        Returns:
            LmStatusWrapper: The COP LM readiness.
        """
        ...

    def get_mcu_not_ready_reason(self) -> McuNotReadyStatusWrapper:
        """
        Returns the MCU not ready reason.

        Returns:
            McuNotReadyStatusWrapper: The MCU not ready reason.
        """
        ...

    def get_cop_not_ready_reason(self) -> CopNotReadyStatusWrapper:
        """
        Returns the COP not ready reason.

        Returns:
            COPNotReadyReason: The COP not ready reason.
        """
        ...

    def get_cop_component_status(self) -> ComponentStatusWrapper:
        """
        Returns the COP component status.

        Returns:
            ComponentStatusWrapper: The COP component status Wrapper.
        """
        ...

    def get_cop_safety(self) -> Safety:
        """
        Returns the COP safety status.

        Returns:
            Safety: The COP safety status.
        """
        ...

    def get_mfu_required_state(self) -> COPInitializationRequest | None:
        """
        Returns the MFU required state.

        Returns:
            COPInitializationRequest | None: The MFU required state.
        """

    def get_mfu_location(self) -> LLLNPosition:
        """Get MFU location"""
        ...

    def reload_missiles(self) -> None:
        """Reload missiles"""

    def deplete_missiles(self) -> None:
        """Reload missiles"""

    def randomize_cabin_azimuth(self) -> None:
        """Randomize cabin azimuth"""

    def randomize_turret_azimuth(self) -> None:
        """Randomize turret azimuth"""

    def generate_sr_sectors(self) -> None:
        """Randomize mechanical limit"""
