#!/usr/bin/env python3
"""
Author:   --<>
Purpose:
Created: 03/10/2023
"""

import asyncio
import logging
import struct
import sys
import traceback
from collections.abc import Callable
from datetime import datetime, timezone
from math import degrees, pi
from random import randint, uniform
from socket import (
    AF_INET,
    INADDR_ANY,
    IP_ADD_MEMBERSHIP,
    IP_MULTICAST_IF,
    IP_MULTICAST_TTL,
    IPPROTO_IP,
    IPPROTO_UDP,
    SO_REUSEADDR,
    SOCK_DGRAM,
    SOL_IP,
    SOL_SOCKET,
    error,
    inet_aton,
    socket,
    timeout,
)
from time import monotonic

import navpy
from requests import PreparedRequest, Request
from requests import Response as ReqResponse
from requests import Session, exceptions, post
from spyder_bmc.geoutil import Geo<PERSON>til
from spyder_simul.receiver import Sc<PERSON>rioReceiver
from spyder_simul.simulation import (
    SimCommand,
    SimCOPNotReady,
    SimCOPState,
    SimInventory,
    Sim<PERSON><PERSON>otReady,
    Sim<PERSON><PERSON>,
    SimMFUType,
    Simulation,
)

from spyder_cop.cop_data.ASPTrack import (
    ASPTracks,
    EngagedTracks,
    MissileLocation,
    TimeUtil,
    missile_location_to_dict,
)
from spyder_cop.cop_data.engagement import (
    EngagementKey,
    EngagementOrder,
    Engagements,
    Inventory,
)
from spyder_cop.cop_data.headergen import HeaderGen
from spyder_cop.cop_data.SafetyStatusWrapper import Safety
from spyder_cop.cop_data.simulation_events import SimulationEvents
from spyder_cop.cop_data.stop_send import EMsgType, StopSending
from spyder_cop.messages.class_C2_COP_ASP_Track import C2COPASPTrack
from spyder_cop.messages.class_C2_COP_Chat import C2COPChat
from spyder_cop.messages.class_C2_COP_Engaged_Track import C2COPEngagedTrack
from spyder_cop.messages.class_C2_COP_Engagement import C2COPEngagement
from spyder_cop.messages.class_C2_COP_GSP import C2COPGsp
from spyder_cop.messages.class_C2_COP_Msls_Location_From_Rdr import (
    C2COPMslsLocationFromRdr,
)
from spyder_cop.messages.class_C2_COP_Status import (
    C2COPStatus,
    COPInitData,
    COPInitializationRequest,
)
from spyder_cop.messages.class_COP_C2_Engagement_Status import (
    COPAllocatedMissileStatus,
    COPC2EngagementStatus,
    COPEngagementStatus,
    COPMCUEngagement,
)
from spyder_cop.messages.class_COP_C2_Msl_DL import COPC2MslDL
from spyder_cop.messages.class_COP_C2_Status import (
    COPC2Status,
    COPMissilesAvailableForFire,
    COPSectorElement,
)
from spyder_cop.messages.class_COP_C2_Toplite_Track import COPC2TopliteTrack
from spyder_cop.messages.class_header import Header
from spyder_cop.messages.util import (
    EASPTargetType,
    ECOPEngagementAbortSource,
    ECOPEngagementHandling,
    ECOPLMArmed,
    ECOPMCUEngagementStatus,
    ECOPMCUState,
    ECOPMisfireReason,
    ECOPMode,
    ECOPReadinessToEngage,
    ECOPTopliteEngagementIndication,
    ECOPType,
    EEngagementFireControl2,
    EICPLockStatus,
    EICPStage,
    EInitFireSource,
    ELockingPolicy,
    ENoYesBoolean,
    ETypeOfICP,
    LLLNPosition,
)
from spyder_cop.messages.util_method import get_checksum
from spyder_cop.statistics import Statistics

from .config import Config, COPConfig
from .cop_data.ComponentStatusWrapper import ComponentStatusWrapper
from .cop_data.CopNotReadyStatusWrapper import CopNotReadyStatusWrapper
from .cop_data.CopStateStatusWrapper import CopStateStatusWrapper
from .cop_data.LmStatusWrapper import LmStatusWrapper
from .cop_data.McuNotReadyStatusWrapper import McuNotReadyStatusWrapper
from .downlink import Downlink
from .icop_web import ICopWeb
from .icopserver import ICOPServer
from .sil_gui import Gui
from .web_server import CopWebServer

RAD_360 = 2 * pi
FEET2METER = 0.3048


class COPServer(ICOPServer, ICopWeb):
    """COP_server"""

    TIMEOUT_MANAGEMENT_MONITOR = 1

    TIMEOUT_MISSILE_POSITION_UPDATE = 0.2
    TIMEOUT_ENGAGEMENT_QUEUE = 0.25

    VERSION = "ICD:3.3.2_24.02.2025"

    RECEIVED_CHAT_PREFIX = "Received: "

    def __init__(
        self, general_conf: Config, cop_config: COPConfig, log_fac: logging.Logger
    ) -> None:
        """Constructor"""
        self.loop: asyncio.AbstractEventLoop | None = None
        self.my_logger = log_fac
        self.status_thread_server: asyncio.Task[None] | None = None
        self.bmc_address = (cop_config.bmc_ip, cop_config.bmc_port)
        self.general_conf = general_conf
        self.cop_conf = cop_config
        self.mfu_type = self.cop_conf.mfu_type

        self.mcu_serial_id = self.cop_conf.mcu_serial_id
        self.cop_logical_id: int = 0
        self.cop_physical_id = self.cop_conf.sender_id

        try:
            self.cop_send_sock = socket(AF_INET, SOCK_DGRAM)
            self.cop_send_sock.bind((self.cop_conf.cop_ip, self.cop_conf.cop_port))
        except error as exception:
            error_msg = f"Error: Unable bind COP address {exception}"
            self.my_logger.error(error_msg)
            sys.exit(1)

        self.remote_control: ScenarioReceiver | None = None
        if cop_config.remote_control_port > 0:
            self.remote_control = ScenarioReceiver(
                cop_config.remote_control_ip,
                cop_config.remote_control_port,
                self.my_logger,
                cop_config.remote_control_strict_json,
            )
            self.remote_control.set_on_event_callback(self.my_event_callback)
        else:
            self.my_logger.error("Remote control on this COP is off.")
        self.asp_tracks = ASPTracks(self.my_logger)
        self.eng_tracks = EngagedTracks(self.my_logger)
        self.engagements = Engagements(
            self.eng_tracks, self.cop_conf.missile_speed, self.my_logger
        )

        self.current_cop_status = self._prepare_cop_status()
        self.downlink = Downlink(
            self.my_logger, self.current_cop_status.launcher_position
        )

        self.generate_sr_sectors()
        self.mcu_not_ready_status: McuNotReadyStatusWrapper = McuNotReadyStatusWrapper(
            self.current_cop_status.mcu_not_ready_reason
        )
        self.cop_not_ready_status: CopNotReadyStatusWrapper = CopNotReadyStatusWrapper(
            self.current_cop_status.cop_not_ready_reason
        )
        self.component_status: ComponentStatusWrapper = ComponentStatusWrapper(
            self.current_cop_status.component_status
        )
        self.lm_readiness_status: LmStatusWrapper = LmStatusWrapper(
            self.current_cop_status.lm_readiness
        )
        self.cop_state_status: CopStateStatusWrapper = CopStateStatusWrapper(
            self.current_cop_status.cop_state
        )

        self.current_bmc_status = C2COPStatus(0, 0, 0, 0, 0)
        self.mfu_required_state = COPInitializationRequest(0, 0, 0)
        self.mfu_required_state_is_valid = False
        self.mfu_required_state_update = monotonic()

        self.gui: Gui | None = None
        if self.cop_conf.gui_port > 0:
            self.gui = Gui(
                self, (self.cop_conf.gui_ip, self.cop_conf.gui_port), self.my_logger
            )
        else:
            self.my_logger.error("GUI on this COP is off.")
        self.header_gen = HeaderGen()
        self.init_status = False

        self.safety = Safety()
        self.safety.set_sim_flag(not cop_config.is_training)
        if cop_config.is_training:
            self.cop_state_status.mode = ECOPMode.MAINTENANCE
            self.lm_readiness_status.mcu_state = ECOPMCUState.DISCONNECTED
        self.statistics = Statistics()
        self.inventory = Inventory(
            self.mfu_type,
            self.cop_conf.derby,
            self.cop_conf.python,
            self.cop_conf.derby_er,
            self.cop_conf.derby_lr,
        )
        self.destroyed_planes: dict[int, dict[str, bool]] = {}

        self.incoming_chat: str | None = None

        self.engagements_queue: asyncio.Queue[C2COPEngagement] = asyncio.Queue()
        self.stop_sending: StopSending = StopSending()
        self.simulation_config: Simulation | None = None
        self.simulation_event: SimulationEvents = SimulationEvents(self.my_logger)
        self.cop_simulation_start: bool = True
        self.current_sim_command: SimCommand = SimCommand.START

        self.web_server: CopWebServer | None = None
        if cop_config.cop_web_port > 0:
            self.web_server = CopWebServer(
                "COPWeb", self, cop_config.cop_web_ip, cop_config.cop_web_port
            )

    def close_all_missiles_in_air(self) -> None:
        """close all missiles in the air ans close communication"""
        self.engagements.close_all_missiles_in_air()

    def _prepare_cop_status(self) -> COPC2Status:
        """Prepare static part of current COP C2 Status"""
        current_cop_status = COPC2Status(self.mcu_serial_id, self.mfu_type)
        current_cop_status.launcher_position = LLLNPosition(
            self.cop_conf.mfu_lat, self.cop_conf.mfu_lon, self.cop_conf.mfu_alt
        )
        current_cop_status.cabin_azimuth = self.cop_conf.cabin_azimuth
        if self.mfu_type == ECOPType.SR:
            current_cop_status.turret_azimuth = self.cop_conf.turret_azimuth

        return current_cop_status

    def get_sim_config(self) -> str:
        """Get Current sim config"""
        if self.simulation_config is None:
            return ""
        return str(self.simulation_config)

    def my_event_callback(self, simulation: Simulation) -> None:
        """Event callback"""
        self.simulation_config = simulation
        if simulation.scenario is not None:
            if simulation.scenario.mfu_section is None:
                return
            self.my_logger.error("Scenario received")
            self.set_simulation_config(simulation.scenario.mfu_section)

        if simulation.command is not None:
            self.my_logger.error("Scenario Comment received")
            self.set_simulation_command(simulation.command)
            self.current_sim_command = simulation.command
        if simulation.events is not None:
            self.simulation_event.set_simulation_events(simulation.events)
            self.set_simulation_inventory(self.simulation_event.cop_inventory)
            self.set_simulation_state(self.simulation_event.cop_state)
            self.set_simulation_abort(self.simulation_event.abort)
            self.set_simulation_cop_not_ready(self.simulation_event.cop_not_ready)
            self.set_simulation_mcu_not_ready(self.simulation_event.mcu_not_ready)

    def get_stop_send(self) -> StopSending:
        """Get Stop sending settings"""
        return self.stop_sending

    def try_create_autonomous_engagement(self, id_plane: int) -> bool:
        """try to create autonomous engagement"""
        if self.safety.is_autonomous():
            track = self.asp_tracks.get_track(id_plane)
            if track:
                missile_type = self.inventory.get_autonomous_missile()
                if not missile_type:
                    return False
                return self.engagements.add_autonomous_engagement(
                    track, missile_type, self.mfu_type
                )

        return False

    def increment_missiles(self, missile_type: ETypeOfICP) -> None:
        """Increment number of missiles by type"""
        self.inventory.increment(missile_type)
        self.check_inventory()

    def reload_missiles(self) -> None:
        """Reload missiles"""
        self.inventory.reload()
        self.check_inventory()

    def deplete_missiles(self) -> None:
        """Reload missiles"""
        self.inventory.deplete()
        self.check_inventory()

    def get_mfu_logical_id(self) -> int:
        """Get MFU logical ID"""
        return self.cop_logical_id

    def get_mfu_physical_id(self) -> int:
        """Get MFU physical ID"""
        return self.cop_physical_id

    def get_mfu_mcu_serial_id(self) -> int:
        """Get MFU MCU serial ID"""
        return self.mcu_serial_id

    def check_maximum_missiles(self) -> None:
        """Check and set max up to 8 missiles"""
        self.inventory.set_max_missiles()

    def set_simulation_config(self, mfu_section: SimMFU) -> None:
        """Set simulation config"""
        self.safety.set_sim_flag(False)
        self.lm_readiness_status.mcu_state = ECOPMCUState.TRAINING

        if mfu_section.mfu_type is not None:
            match mfu_section.mfu_type:
                case SimMFUType.SR:
                    self.cop_state_status.cop_type = ECOPType.SR
                    self.mfu_type = ECOPType.SR
                case _:
                    self.cop_state_status.cop_type = ECOPType.MR
                    self.mfu_type = ECOPType.MR

        self.set_simulation_inventory(mfu_section.inventory)

        self.current_cop_status.launcher_position = LLLNPosition(
            mfu_section.location.latitude,
            mfu_section.location.longitude,
            (
                mfu_section.location.altitude * FEET2METER
                if mfu_section.location.altitude
                else 0
            ),
        )
        if mfu_section.mechanical_limit and self.mfu_type == ECOPType.SR:
            self.current_cop_status.mechanical_limit = COPSectorElement(
                mfu_section.mechanical_limit.start_azimuth,
                mfu_section.mechanical_limit.sector_size,
            )
        if mfu_section.no_launch_sectors and self.mfu_type == ECOPType.SR:
            self.current_cop_status.clear_no_launch_sectors()
            for no_launch_sector in mfu_section.no_launch_sectors:
                self.current_cop_status.add_no_launch_sector(
                    COPSectorElement(
                        no_launch_sector.start_azimuth, no_launch_sector.sector_size
                    )
                )
        if mfu_section.cabin_azimuth:
            self.current_cop_status.cabin_azimuth = mfu_section.cabin_azimuth

        self.set_simulation_state(mfu_section.state)

    def set_simulation_inventory(self, inventory: SimInventory | None) -> None:
        if inventory is None:
            return None
        self.inventory.cop_type = self.mfu_type
        self.inventory.derby = inventory.derby
        self.inventory.python = inventory.python
        self.inventory.derby_er = inventory.derby_er
        self.inventory.derby_lr = inventory.derby_lr
        self.check_maximum_missiles()

    def set_simulation_state(
        self,
        state: SimCOPState | None,
    ) -> None:
        if state is not None:
            if state.turret_azimuth:
                if self.mfu_type == ECOPType.SR:
                    self.current_cop_status.turret_azimuth = state.turret_azimuth
                else:
                    self.current_cop_status.turret_azimuth = 0

            self.cop_state_status.convert_sim_mode(state.mode)
            self.lm_readiness_status.lm_armed = (
                ECOPLMArmed.ARMED if state.armed else ECOPLMArmed.NOT_ARMED
            )
            self.cop_state_status.convert_sim_operability(state.operability)
            self.lm_readiness_status.convert_sim_readiness(state)
            self.safety.set_autonomous(self.cop_state_status.is_cop_autonomous)
            self.safety.set_fire_source(state.fire_source)

    def get_current_sim_command(self) -> SimCommand:
        """Get latest simulation command"""
        return self.current_sim_command

    def set_simulation_command(self, sim_command: SimCommand) -> None:
        """Set simulation command"""

        match sim_command:
            case SimCommand.START:
                self.do_sim_start()
            case SimCommand.STOP:
                self.do_sim_stop()
            case SimCommand.PAUSE:
                self.do_sim_pause()
            case SimCommand.RESUME:
                self.do_sim_resume()
            case SimCommand.RESET:
                self.do_sim_reset()

    def do_sim_start(self) -> None:
        """Do simulation start"""
        self.my_logger.info("Simulation start")
        self.cop_simulation_start = True

    def do_sim_stop(self) -> None:
        """Do simulation stop"""
        self.my_logger.info("Simulation stop")
        self.cop_simulation_start = False

    def do_sim_pause(self) -> None:
        """Do simulation pause"""
        self.my_logger.info("Simulation pause")
        self.do_sim_stop()

    def do_sim_resume(self) -> None:
        """Do simulation resume"""
        self.my_logger.info("Simulation resume")
        self.do_sim_stop()
        self.do_sim_start()

    def do_sim_reset(self) -> None:
        """Do simulation reset"""
        self.my_logger.info("Simulation reset")
        self.reset()

    def get_mfu_location(self) -> LLLNPosition:
        """Get MFU location"""
        return self.current_cop_status.launcher_position

    def get_version(self) -> str:
        """Get version"""
        return COPServer.VERSION

    async def start_web_server(self) -> None:
        """run task"""
        if self.web_server:
            self.my_logger.info(f"Web server started. {self.web_server}")
            await self.web_server.start()

    async def _timeout_handler(self) -> None:
        """TimeOut handler"""
        while True:
            if self.mfu_required_state_is_valid:
                current_time = monotonic()
                time_difference = current_time - self.mfu_required_state_update
                if time_difference > self.general_conf.timeout_status_valid:
                    self.mfu_required_state_is_valid = False
                    self.cop_logical_id = 0
                    self.mfu_required_state = COPInitializationRequest(0, 0, 0)
                    self.my_logger.info("Timeout MFU Required State")

            await asyncio.sleep(self.general_conf.timeout_status_timeout)

    def toggle_misfire_indicator(self, eng_id: int) -> None:
        """Toggle misfire indication"""
        self.engagements.toggle_misfire_indication(eng_id)

    def get_mfu_required_state(self) -> COPInitializationRequest | None:
        """Get MFU Required State

        Returns:
            COPInitializationRequest: _description_
        """
        if self.mfu_required_state_is_valid:
            return self.mfu_required_state
        return None

    def finish_abort(self):
        self.lm_readiness_status.mcu_abort_button_pressed = ENoYesBoolean.NO
        self.safety.set_abort(False)

    def do_abort(self):
        self.lm_readiness_status.mcu_abort_button_pressed = ENoYesBoolean.YES
        self.engagements.abort_all_engagements()

    def reset_statistics(self) -> None:
        """Reset statistics"""
        self.statistics.reset()

    def get_statistics_report(self) -> str:
        """Get statistics report"""
        return self.statistics.get_statistics()

    def reset(self) -> None:
        """Reset COP Server"""
        self.asp_tracks.reset()
        self.eng_tracks.reset()
        self.engagements.reset()
        self.statistics.reset()

    def get_inventory(self) -> Inventory:
        """Get Inventory"""
        return self.inventory

    def get_engagements(self) -> Engagements:
        """Returns the engagements."""
        return self.engagements

    def get_cop_safety(self) -> Safety:
        """Get Safety for GUI"""
        return self.safety

    def get_eng_tracks(self) -> EngagedTracks:
        """Returns the Engaged tracks."""
        return self.eng_tracks

    def get_asp_tracks(self) -> ASPTracks:
        """get ASP track object"""
        return self.asp_tracks

    def get_engagements_cnt(self) -> int:
        """Get Engagement cnt"""
        return self.engagements.get_engagements_cnt()

    def get_cop_status(self) -> COPC2Status:
        """Get COP status"""
        return self.current_cop_status

    def get_cop_state(self) -> CopStateStatusWrapper:
        """Get COP State"""
        return self.cop_state_status

    def get_cop_lm_readiness(self) -> LmStatusWrapper:
        """Get LM Readiness object"""
        return self.lm_readiness_status

    def get_bmc_status(self) -> C2COPStatus:
        """Get Current BMC Status"""
        return self.current_bmc_status

    def get_mcu_not_ready_reason(self) -> McuNotReadyStatusWrapper:
        """Get MCU Not ready reason

        Returns:
            MCUNotReadyReason: _description_
        """
        return self.mcu_not_ready_status

    def get_cop_not_ready_reason(self) -> CopNotReadyStatusWrapper:
        """Get COP Not ready reason

        Returns:
            CopNotReadyStatusWrapper: _description_
        """
        return self.cop_not_ready_status

    def get_cop_component_status(self) -> ComponentStatusWrapper:
        """Get Component Status

        Returns:
            ComponentStatus: _description_
        """
        return self.component_status

    async def start(self, loop: asyncio.AbstractEventLoop) -> None:
        """start threads"""
        self.loop = loop
        try:
            async with asyncio.TaskGroup() as tg:
                if self.gui:
                    tg.create_task(self.gui.start_server())
                if self.remote_control:
                    tg.create_task(self.remote_control.start())
                tg.create_task(self.asp_tracks.start())
                tg.create_task(self.eng_tracks.start())
                tg.create_task(self._timeout_handler())
                if self.general_conf.enable_flightradar:
                    tg.create_task(self.flightradar_register_loop())
                    tg.create_task(self.start_web_server())
                tg.create_task(self.process_engagements_queue())
                tg.create_task(self.process_and_send_downlink_data())
                tg.create_task(self.monitor_engagements())
                tg.create_task(self.update_engagement_missile_position())
                tg.create_task(self.multicast_receive_data())
        except Exception:
            self.my_logger.error(traceback.format_exc())
        self.my_logger.info("COP Server stopped")

    def get_missiles_data(self) -> list[dict[str, str | float]]:
        """get missiles data"""
        data: list[dict[str, float | int | bool | str]] = []
        for engagement in list(self.engagements.engagements.values()):
            if engagement.is_missile_started:
                fcr_id = engagement.track_id
                if engagement.target.asp_track:
                    fcr_id = engagement.target.asp_track.fcr_track_id
                miss = MissileLocation(
                    miss_id=engagement.missile.missile_id,
                    miss_lat=engagement.missile.missile_pos.x_pos,
                    miss_lon=engagement.missile.missile_pos.y_pos,
                    miss_alt=engagement.missile.missile_pos.z_pos,
                    bearing=degrees(engagement.missile.bearing),
                    miss_type="derby",
                    target_id=fcr_id,
                    detonation=engagement.missile.detonation,
                    cop_id=self.mcu_serial_id,
                )
                data.append(missile_location_to_dict(miss))
        return data

    def get_missiles_target_data(self) -> dict[int, dict[str, bool]]:
        """get missiles data"""
        return self.destroyed_planes

    def calculate_cop_eng_readiness(self) -> ECOPReadinessToEngage | None:
        """Calculate Eng readiness based on nuber of missiles and state"""

        if (
            self.lm_readiness_status.is_mcu_in_operational
            and not self.cop_state_status.is_cop_in_operational
            and not self.cop_state_status.is_cop_autonomous
        ):
            self.my_logger.debug(
                "Not ready because conflict MCU state and COP Mode operational"
            )
            return ECOPReadinessToEngage.NOT_READY_TO_ENGAGE

        if (
            self.lm_readiness_status.is_mcu_in_training
            and not self.cop_state_status.is_cop_in_training
            and not self.cop_state_status.is_cop_autonomous
        ):
            self.my_logger.debug(
                "Not ready because conflict MCU state and COP Mode training"
            )
            return ECOPReadinessToEngage.NOT_READY_TO_ENGAGE

        if self.cop_state_status.is_cop_in_conflict:
            self.my_logger.debug("Not ready because conflict in COP Mode ")
            return ECOPReadinessToEngage.NOT_READY_TO_ENGAGE

        missiles = self.inventory.total_missiles()
        if missiles <= 0:
            self.my_logger.debug("Not ready because not enough missiles")
            return ECOPReadinessToEngage.NOT_READY_TO_ENGAGE

        if self.cop_state_status.is_cop_faulty:
            self.my_logger.debug("Not ready because operability Faulty")
            return ECOPReadinessToEngage.NOT_READY_TO_ENGAGE

        if not self.lm_readiness_status.is_mcu_ready:
            self.my_logger.debug("Not ready because wrong MCU state")
            return ECOPReadinessToEngage.NOT_READY_TO_ENGAGE

        if not (
            self.cop_state_status.is_cop_immediate
            or self.cop_state_status.is_cop_autonomous
        ):
            self.my_logger.debug("Not ready because Not Immediate or Autonomous")
            self.mcu_not_ready_status.set_no_immediate_state(ENoYesBoolean.YES)
            return ECOPReadinessToEngage.NOT_READY_TO_ENGAGE
        else:
            self.mcu_not_ready_status.set_no_immediate_state(ENoYesBoolean.NO)

        if self.mcu_not_ready_status.is_mcu_in_any_error():
            self.my_logger.debug("Not ready because wrong MCU state")
            return ECOPReadinessToEngage.NOT_READY_TO_ENGAGE

        if (
            missiles > 0
            and self.cop_state_status.is_cop_degraded
            and (
                self.cop_state_status.is_cop_immediate
                or self.cop_state_status.is_cop_autonomous
            )
            and self.lm_readiness_status.is_mcu_ready
        ):
            self.my_logger.debug("Not ready degraded because operability Degraded")
            return ECOPReadinessToEngage.DEGRADED_BUT_READY_TO_ENGAGE

        if (
            missiles > 0
            and self.cop_state_status.is_cop_ok
            and (
                self.cop_state_status.is_cop_immediate
                or self.cop_state_status.is_cop_autonomous
            )
            and self.lm_readiness_status.is_mcu_ready
        ):
            self.my_logger.debug("Ready to eng, all conditions met")
            return ECOPReadinessToEngage.READY_TO_ENGAGE

        return None

    def get_cop_c2_status(self) -> bytes:
        """get_cop_c2_status"""

        if self.safety.is_autonomous():
            self.cop_state_status.set_autonomous_mode(True)
        else:
            # return to previous state
            self.cop_state_status.set_autonomous_mode(False)

        if change_readiness := self.calculate_cop_eng_readiness():
            if self.lm_readiness_status.is_enabled_allow_override:
                self.my_logger.error(
                    "Ready to Engage will not be overwritten. Current: %s, New: %s",
                    self.lm_readiness_status.lm_readiness_to_engage,
                    change_readiness,
                )
            else:
                self.lm_readiness_status.lm_readiness_to_engage = change_readiness

        self.current_cop_status.total_missiles = COPMissilesAvailableForFire(
            self.inventory.python_max,
            self.inventory.derby_max,
            self.inventory.derby_er_max,
            self.inventory.derby_lr_max,
        )

        self.current_cop_status.missiles_available_for_fire = (
            COPMissilesAvailableForFire(
                self.inventory.python,
                self.inventory.derby,
                self.inventory.derby_er,
                self.inventory.derby_lr,
            )
        )

        if self.incoming_chat:
            size = COPC2Status.MAX_CHAT_TEXT - len(COPServer.RECEIVED_CHAT_PREFIX)
            chat_text = COPServer.RECEIVED_CHAT_PREFIX + self.incoming_chat
            self.current_cop_status.chat_text = chat_text[: COPC2Status.MAX_CHAT_TEXT]
            remaining_chat = self.incoming_chat[size:]
            # self.my_logger.error("Remaining chat: %s", remaining_chat)
            self.incoming_chat = remaining_chat or None
        else:
            self.current_cop_status.chat_text = ""

        msg_data = self.current_cop_status.pack()
        return self.header_gen.get_header_msg(
            COPC2Status.get_msg_type(),
            msg_data,
            self.cop_physical_id,
            self.safety.is_oper(),
        )

    def cop_is_running(self) -> bool:
        """cop_is_running"""
        return self.cop_simulation_start and self.safety.is_master_on()

    async def status_sender(self) -> None:
        """Sending Status msg periodically"""
        while True:
            if self.mfu_required_state_is_valid and self.cop_is_running():
                message = self.get_cop_c2_status()
                try:
                    if (
                        self.stop_sending.get_state(EMsgType.COP_STATUS)
                        and self.loop is not None
                    ):
                        await self.loop.sock_sendto(
                            self.cop_send_sock,
                            message,
                            self.bmc_address,
                        )
                        self.statistics.incr("COP_C2_Status")
                        self.finish_abort()
                    else:
                        self.my_logger.error("Sending of COP C2 Status is disabled.")
                except OSError as err:
                    self.my_logger.error("Error sending status: %s", err)

                self.my_logger.debug("status sent")
            await asyncio.sleep(self.general_conf.timeout_status)

    def _process_c2_cop_eng_order(self, eng: C2COPEngagement) -> None:
        """Receive Eng Order"""
        track_id = eng.track_id
        self.my_logger.info(
            "received C2_COP_Engagement receive track_id = %s", track_id
        )

        asp_target = self.asp_tracks.get_track(track_id)
        cop_plane = self.eng_tracks.get_target(track_id)
        if cop_plane is None:
            self.my_logger.debug("Create Target from ASP Track")
            cop_plane = self.eng_tracks.add_eng_track_from_asp(asp_target)

        self.engagements.add_engagement(
            eng=eng, target=cop_plane, mfu_type=self.mfu_type
        )

    async def _process_c2_cop_eng_track(self, next_data: bytes) -> None:
        """Receive Eng Track"""
        self.statistics.incr("C2COPEngagedTrack")
        self.my_logger.info("received C2_COP_Engaged_Track receive")
        eng_track = C2COPEngagedTrack.unpack(next_data)
        asp_track = self.asp_tracks.get_track(eng_track.track_id)
        self.eng_tracks.add_eng_track(eng_track, asp_track)

    async def _put_engagement_to_queue(self, next_data: bytes) -> None:
        """Receive Eng Order"""
        self.statistics.incr("C2COPEngagement")
        eng = C2COPEngagement.unpack(next_data)
        await self.engagements_queue.put(eng)

    async def _process_c2_cop_msls_location_from_rdr(self, next_data: bytes) -> None:
        """Process C2_COP_Msls_Location_From_Rdr"""
        self.statistics.incr("C2COPMSLSLocationFromRdr")
        msls = C2COPMslsLocationFromRdr.unpack(next_data)
        self.downlink.add_data(msls.data)

    async def unicast_receive_data(self) -> None:
        """Receive status msg from BMC"""

        buffer_size = 1024
        self.cop_send_sock.setblocking(False)
        message_handlers: dict[int, Callable] = {
            C2COPEngagedTrack.get_msg_type(): self._process_c2_cop_eng_track,
            C2COPEngagement.get_msg_type(): self._put_engagement_to_queue,
            C2COPMslsLocationFromRdr.get_msg_type(): self._process_c2_cop_msls_location_from_rdr,
        }
        while self.loop is not None:
            try:
                data, _ = await self.loop.sock_recvfrom(self.cop_send_sock, buffer_size)
                data_header = data[: Header.SIZE]
                header = Header.unpack(data_header)
                next_data = data[Header.SIZE :]
                check_sum = get_checksum(next_data)
                if (header.msg_checksum == 0) or (check_sum == header.msg_checksum):
                    if header.msg_type in message_handlers:
                        if self.cop_is_running():
                            await message_handlers[header.msg_type](next_data)
                    else:
                        self.my_logger.error(
                            "Unknown message type in unicast stream: %s",
                            header.msg_type,
                        )
                else:
                    self.my_logger.error(
                        "checksum error receive unicast_receive_data()"
                    )
            except struct.error:
                self.my_logger.error("No COP data error: unicast_receive_data()")
            except timeout:
                self.my_logger.error("unicast_receive_data timeout")

    def _process_c2_cop_chat_msg(self, next_data: bytes) -> None:
        """Receive Chat Message"""
        self.statistics.incr("C2COPChat")
        chat = C2COPChat.unpack(next_data)
        if chat.is_msg_for_this_cop(self.cop_logical_id):
            self.my_logger.info("received C2_COP_Chat")
            self.incoming_chat = chat.chat_msg

    @staticmethod
    def is_multicast(ip_address: str | None) -> bool:
        """Check if the address is multicast"""
        if ip_address is None:
            return False
        if ip_address == "":
            return False
        try:
            octets = list(map(int, ip_address.split(".")))
            return 224 <= octets[0] <= 239
        except ValueError:
            return False

    def _process_c2_cop_gsp(self, _next_data: bytes) -> None:
        """Process C2_COP_GSP"""
        self.statistics.incr("C2_COP_GSP")

    def _process_c2_cop_asp_track(self, next_data: bytes) -> None:
        """Process C2_COP_ASP_Track"""
        self.statistics.incr("C2COPASPTrack")
        try:
            c2track = C2COPASPTrack.unpack(next_data)
            self.my_logger.debug(c2track)
            if not self.init_status:
                self.start_bmc_unicast_comm()
            for track in c2track.tracks:
                self.asp_tracks.add_track(track)
        except ValueError:
            self.my_logger.error("No COP data error: start_asp_tracks()")

    def _process_c2_cop_status(self, cop_status_data: bytes) -> None:
        """Process C2_COP_Status"""
        self.statistics.incr("C2COPStatus")
        try:
            self.current_bmc_status = C2COPStatus.unpack(cop_status_data)
        except Exception:
            self.my_logger.error("No COP init data")
            return
        valid = False
        for cop_data in self.current_bmc_status.initialization_data_array:
            if cop_data.mcu_serial_id == self.mcu_serial_id:
                valid = self.set_required_mfu_state(cop_data)

        if not valid:
            self.mfu_required_state_is_valid = False
        else:
            self.my_logger.debug("received C2_COP_Status receive: valid: %s", valid)

    async def multicast_receive_data(self) -> None:
        """Multicast receive of ASP Track, starting communication here."""

        with socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP) as sock_multi:
            sock_multi.setsockopt(SOL_SOCKET, SO_REUSEADDR, 1)
            if self.is_multicast(self.general_conf.multicast_group):
                self.prepare_multicast(sock_multi)
            else:
                server_address = (
                    self.general_conf.multicast_group,
                    self.general_conf.multicast_port,
                )
                sock_multi.bind(server_address)
            sock_multi.setblocking(False)
            while self.loop is not None:
                data, _ = await self.loop.sock_recvfrom(sock_multi, 1024)
                try:
                    # self.my_logger.error("Receive multicast data: %s", data)
                    header = Header.unpack(data)
                    next_data = data[Header.SIZE :]
                    chk_sum = get_checksum(next_data)
                    process_dict: dict[int, Callable] = {
                        C2COPStatus.get_msg_type(): self._process_c2_cop_status,
                        C2COPASPTrack.get_msg_type(): self._process_c2_cop_asp_track,
                        C2COPGsp.get_msg_type(): self._process_c2_cop_gsp,
                        C2COPChat.get_msg_type(): self._process_c2_cop_chat_msg,
                    }
                    if (chk_sum == 0) or (chk_sum == header.msg_checksum):
                        msg_type = header.msg_type
                        if msg_type in process_dict:
                            if self.cop_is_running():
                                process_dict[msg_type](next_data)
                        else:
                            self.my_logger.error(
                                "Unknown message type in multicast stream: %s",
                                header.msg_type,
                            )
                    else:
                        self.my_logger.error(
                            "chk_sum error receive multicast_receive_data() - %s",
                            header.msg_type,
                        )
                except struct.error:
                    self.my_logger.error("No COP data error: multicast_receive_data()")

    def prepare_multicast(self, sock_multi: socket) -> None:
        """Prepare multicast receiver"""
        server_address = ("0.0.0.0", self.general_conf.multicast_port)
        sock_multi.bind(server_address)
        # group = socket.inet_aton(multicast_group)
        # mreq = struct.pack("4sL", group, socket.INADDR_ANY)
        # mreq = socket.inet_aton(self.config.multicast_group) + socket.inet_aton(
        #     "0.0.0.0"
        # )
        # listen
        mreq = struct.pack(
            "=4sl",
            inet_aton(self.general_conf.multicast_group),
            INADDR_ANY,
        )
        sock_multi.setsockopt(IPPROTO_IP, IP_ADD_MEMBERSHIP, mreq)

        sock_multi.setsockopt(SOL_IP, IP_MULTICAST_TTL, 20)
        # sock_multi.setsockopt(
        #     socket.SOL_SOCKET, socket.SO_BINDTODEVICE, "br_cop".encode()
        # )
        # sock_multi.setsockopt(socket.SOL_IP, socket.IP_MULTICAST_LOOP, 1)

        # send multicast over this interface
        if self.general_conf.multicast_if != "":
            sock_multi.setsockopt(
                SOL_IP,
                IP_MULTICAST_IF,
                inet_aton(self.general_conf.multicast_if),
            )

    def set_required_mfu_state(self, cop_data: COPInitData) -> bool:
        """Set Required state according BMC"""
        if (
            self.mfu_type == ECOPType.MR
            and cop_data.required_state.locking_policy == ELockingPolicy.LOBL
        ):
            self.my_logger.error("Conflict MR MFU and LOBL is not allowed.")
            return False
        self.mfu_required_state = cop_data.required_state
        local_bmc = self.mfu_required_state.fire_source == EInitFireSource.LOCAL
        remote_bmc = self.mfu_required_state.fire_source == EInitFireSource.REMOTE
        local_mfu = self.safety.is_local_fire_source()
        remote_mfu = self.safety.is_remote_fire_source()
        if (local_bmc and local_mfu) or (remote_bmc and remote_mfu):
            self.mcu_not_ready_status.set_fire_source_conflict(ENoYesBoolean.NO)
        else:
            self.mcu_not_ready_status.set_fire_source_conflict(ENoYesBoolean.YES)
        self.mfu_required_state_is_valid = True
        self.cop_logical_id = cop_data.cop_logical_id
        self.mfu_required_state_update = monotonic()
        return True

    def start_bmc_unicast_comm(self) -> None:
        """Start BMC unicast communication"""
        self.my_logger.info("Start BMC_COP communication.")
        self.status_thread_server = asyncio.create_task(self.status_sender())
        asyncio.create_task(self.unicast_receive_data())
        self.init_status = True

    @staticmethod
    def _check_flightradar_registration(
        prep_req: PreparedRequest, session: Session
    ) -> bool:
        """Check if MFU is registered by flightRadar

        Returns:
            bool: True if MFU is registered, False Otherwise
        """
        try:
            response: ReqResponse = session.send(prep_req, timeout=1)
            if response.status_code == 200:
                content = response.content
                return content == b"OK"
            return False
        except (TimeoutError, ConnectionRefusedError, exceptions.ConnectionError):
            return False

    def _flightradar_registration(self) -> bool:
        """Register MFU to FlightRadar dynamically"""
        register_param = {
            "cop_id": self.mcu_serial_id,
            "ip_address": self.cop_conf.cop_web_ip,
            "port": self.cop_conf.cop_web_port,
            "lat": self.cop_conf.mfu_lat,
            "lon": self.cop_conf.mfu_lon,
            "radius": self.cop_conf.mfu_radius,
        }

        try:
            resp: ReqResponse = post(
                self.general_conf.flightradar_register_url,
                data=register_param,
                timeout=1,
            )
            if resp.content == b"OK":
                return True
        except TimeoutError:
            pass
        except ConnectionRefusedError:
            pass
        except exceptions.ConnectionError:
            pass
        return False

    async def flightradar_register_loop(self) -> None:
        """FlightRadar MFU registration"""
        session = Session()
        check_params = {"cop_id": self.mcu_serial_id}
        check_req = Request(
            "GET", self.general_conf.flightradar_check_url, params=check_params
        )
        prep_check_req = check_req.prepare()
        while True:
            try:
                if not self._check_flightradar_registration(prep_check_req, session):
                    self._flightradar_registration()
            except Exception:
                self.my_logger.error("FlightRadar MFU registration error")
            await asyncio.sleep(self.general_conf.timeout_flightradar_registration)

    async def monitor_engagements(self) -> None:
        """Monitor Engagement Orders"""

        def immediate_mode_test() -> bool:
            """Mode test"""
            immediate_test = (
                self.cop_state_status.is_cop_immediate
                or self.cop_state_status.is_cop_autonomous
            )
            if not immediate_test:
                self.my_logger.info("Mode test failed")
                self.mcu_not_ready_status.set_no_immediate_state(ENoYesBoolean.YES)
            else:
                self.mcu_not_ready_status.set_no_immediate_state(ENoYesBoolean.NO)
            return immediate_test

        def target_exists_test(eng: EngagementOrder) -> bool:
            """Target exists test"""
            if eng.is_autonomous:
                return True
            eng_target = self.eng_tracks.get_target(eng.track_id)
            if not eng_target:
                self.my_logger.info("Target does not exist")
            return eng_target is not None

        def magic_word_test(eng: EngagementOrder) -> bool:
            """test Magic Word"""
            if self.cop_state_status.mode in [
                ECOPMode.TRAINING_IMMEDIATE,
                ECOPMode.AUTONOMOUS,
            ]:
                magic_word = True
            else:
                magic_word = eng.is_magic_word_valid()
                if not magic_word:
                    self.my_logger.info("Magic word test failed")
            return magic_word

        def can_start_engagement(eng: EngagementOrder) -> bool:
            """Can process engagement test"""
            common_test = (
                not eng.is_missile_started
                and self.lm_readiness_status.is_cop_armed
                and not self.cop_state_status.is_cop_faulty
                and immediate_mode_test()
                and not self.lm_readiness_status.is_cop_not_ready_to_engage
            )
            if eng.is_autonomous and not self.safety.is_autonomous():
                # Not in Autonomous state clean Auto Engagements
                self.my_logger.error(
                    "eng in Auto mode, but COP not in autonomous mode. Delete EnG"
                )
                eng.prepare_to_delete(True)
                return False
            if self.safety.is_autonomous() and eng.is_autonomous:
                return common_test and self.safety.can_fire()
            if self.safety.is_local_fire_source():
                # self.log_fac.error("can_start_engagement Common test: %s",
                #   common_test)
                # self.log_fac.error(
                #     "can_start_engagement self.safety.can_fire(): %s",
                #     self.safety.can_fire(),
                # )
                return (
                    common_test
                    and eng.fire_control2 == EEngagementFireControl2.READY
                    and self.safety.can_fire()
                    and target_exists_test(eng)
                )
            return (
                common_test
                and eng.fire_control2 == EEngagementFireControl2.ENABLE
                and magic_word_test(eng)
                and target_exists_test(eng)
            )

        while True:
            if self.cop_is_running():
                if self.safety.is_abort():
                    self.do_abort()
                contain_autonomous_eng = self.engagements.contain_autonomous_eng()
                for eng_key, eng_order in list(self.engagements.engagements.items()):
                    if eng_order.done:
                        await self.try_to_finish_engagement(eng_key, eng_order)
                    else:
                        success_launch = await self.try_to_start_engagement(
                            target_exists_test(eng_order),
                            can_start_engagement(eng_order),
                            eng_order,
                            contain_autonomous_eng,
                        )
                        if success_launch and self.mfu_type == ECOPType.SR:
                            mfu_pos = self.current_cop_status.launcher_position
                            target_pos = eng_order.target.airplane_pos
                            bearing = GeoUtil.bearing_rad(
                                mfu_pos.x_pos,
                                mfu_pos.y_pos,
                                target_pos.x_pos,
                                target_pos.y_pos,
                            )
                            self.current_cop_status.turret_azimuth = (
                                self.calculate_turret_azimuth(
                                    self.current_cop_status.cabin_azimuth, bearing
                                )
                            )

            await asyncio.sleep(COPServer.TIMEOUT_MANAGEMENT_MONITOR)

    @staticmethod
    def calculate_turret_azimuth(
        cabin_azimuth: float, bearing_to_north: float
    ) -> float:
        """
        Calculate the turret azimuth in radians.

        Parameters
        ----------
        cabin_azimuth : float
            The cabin azimuth in radians.
        bearing_to_north : float
            The bearing to the target in radians.

        Returns
        -------
        float
            The turret azimuth in radians.
        """
        turret_azimuth = (bearing_to_north - cabin_azimuth + RAD_360) % RAD_360
        if turret_azimuth > pi:
            turret_azimuth -= RAD_360

        return turret_azimuth

    async def process_engagements_queue(self) -> None:
        """Process engagements from Queue"""
        while True:
            while not self.engagements_queue.empty():
                data = await self.engagements_queue.get()
                self._process_c2_cop_eng_order(data)

            await asyncio.sleep(COPServer.TIMEOUT_ENGAGEMENT_QUEUE)

    async def process_and_send_downlink_data(self) -> None:
        """Process and send downlink data"""
        while True:
            await self.send_downlink_msg()
            await asyncio.sleep(self.general_conf.timeout_downlink)

    async def update_engagement_missile_position(self) -> None:
        """Update Missile Position"""
        while True:
            for eng_order in list(self.engagements.engagements.values()):
                await self.update_missile_position(eng_order)
            await asyncio.sleep(COPServer.TIMEOUT_MISSILE_POSITION_UPDATE)

    async def send_downlink_msg(self) -> None:
        """Send downlink message"""
        if msg := self.downlink.prepare_dwn_message():
            msg_data = msg.pack()
            data = self.header_gen.get_header_msg(
                COPC2MslDL.get_msg_type(),
                msg_data,
                self.cop_physical_id,
                self.safety.is_oper(),
            )
            if (
                self.stop_sending.get_state(EMsgType.COP_DOWNLINK)
                and self.loop is not None
            ):
                await self.loop.sock_sendto(
                    self.cop_send_sock,
                    data,
                    (self.cop_conf.bmc_ip, self.cop_conf.bmc_port),
                )
                self.statistics.incr("DOWNLINK MSL")
            else:
                self.my_logger.error("Sending of downlink data is disabled...")

    def _update_autonomous_target(self, eng: EngagementOrder) -> None:
        """In case eng is Autonomous, update target position"""
        if eng.is_autonomous:
            track = self.asp_tracks.get_track(eng.track_id)
            if track:
                current_pos = track.pos
                eng.target.pos = current_pos
                eng.target.airplane_pos = EngagedTracks.convert_ecef2gps(current_pos)

    async def handle_abort(
        self, eng_order: EngagementOrder, log_error: str, stat_name: str
    ) -> None:
        """Handle errors during launch"""
        self.my_logger.error(log_error)
        eng_order.do_abort(True)
        await self.send_multiple_status(
            eng_order,
            ECOPEngagementHandling.CANT_COMPLY,
            status=ECOPMCUEngagementStatus.CLOSED,
            icp_stage=EICPStage.ON_GROUND,
            stat_name=stat_name,
        )

    async def try_to_launch_missile(self, eng_order: EngagementOrder) -> bool:
        """Try to start missile"""
        self.my_logger.info("Launch missile")
        if eng_order.missile.misfire_indication:
            await self.handle_abort(
                eng_order, "Missile misfire", "COP_C2_Eng_Status_cant"
            )
            return False

        if self.update_inventory(eng_order):
            await self.launch_missile_eng_status(
                eng_order, int(TimeUtil.get_ts_from_mid())
            )
            return True
        else:
            await self.handle_abort(
                eng_order, "Not enough missiles to fire", "COP_C2_Eng_Status_cant"
            )
            return False

    @staticmethod
    def get_utc_midnight_milliseconds() -> int:
        """Get number of milliseconds from UTC midnight"""
        return int(
            (
                datetime.now(timezone.utc)
                - datetime.now(timezone.utc).replace(
                    hour=0, minute=0, second=0, microsecond=0
                )
            ).total_seconds()
            * 1000
        )

    async def send_toplite_data(self, eng_order: EngagementOrder) -> None:
        """Send toplite tracking data"""
        target = eng_order.target.airplane_pos
        rel_north, rel_east, rel_down = navpy.lla2ned(
            target.x_pos,
            target.y_pos,
            target.z_pos,
            self.cop_conf.mfu_lat,
            self.cop_conf.mfu_lon,
            self.cop_conf.mfu_alt,
        )
        msg = COPC2TopliteTrack(
            target_update_time=self.get_utc_midnight_milliseconds(),
            target_position_north=int(rel_north * 100),
            target_position_east=int(rel_east * 100),
            target_position_down=int(rel_down * 100),
            target_velocity_north=10,
            target_velocity_down=11,
            target_velocity_east=12,
            autonomous_target_id=eng_order.track_id,
            autonomous_engagement_id=eng_order.cop_eng_id,
            toplite_target_type=EASPTargetType.FIGHTER,
            validity=ENoYesBoolean.YES,
        )
        msg_data = msg.pack()
        data = self.header_gen.get_header_msg(
            COPC2TopliteTrack.get_msg_type(),
            msg_data,
            self.cop_physical_id,
            self.safety.is_oper(),
        )
        if self.stop_sending.get_state(EMsgType.COP_TOPLITE) and self.loop is not None:
            await self.loop.sock_sendto(
                self.cop_send_sock, data, (self.cop_conf.bmc_ip, self.cop_conf.bmc_port)
            )
            self.statistics.incr("TOPLITE_TRACK")
        else:
            self.my_logger.error("Sending of Toplite data is disabled...")

    async def try_to_start_engagement(
        self,
        target_exists_test: bool,
        can_start_missile: bool,
        eng_order: EngagementOrder,
        contain_autonomous_eng: bool,
    ) -> bool:
        """Try to start engagement
        @param can_start_missile:
        @param target_exists_test:
        @type eng_order: EngagementOrder
        """
        self.my_logger.debug(eng_order)
        if not target_exists_test:
            await self.handle_abort(
                eng_order, "Target does not exist", "COP_C2_Eng_Status_cant"
            )
            return False

        self._update_autonomous_target(eng_order)

        if not contain_autonomous_eng:
            self.current_cop_status.cop_toplite_engagement_indication = (
                ECOPTopliteEngagementIndication.NO_ENGAGEMENT
            )

        if can_start_missile:
            launch_success = await self.try_to_launch_missile(eng_order)
            if not launch_success:
                return False
            self.check_inventory()
            return True

        elif eng_order.missile.in_the_air:
            await self.move_missile_eng_status(eng_order)
            if eng_order.is_autonomous:
                self.current_cop_status.cop_toplite_engagement_indication = (
                    ECOPTopliteEngagementIndication.AIR_ENGAGEMENT
                )
                await self.send_toplite_data(eng_order)

        if not eng_order.missile.in_the_air:
            mcu_error = self.mcu_not_ready_status.is_mcu_in_any_error()
            icp_error = not self.inventory.has_missile_type(eng_order.type_icp)
            if mcu_error or icp_error:
                if mcu_error:
                    self.my_logger.error("CANT_COMPLY - MCU not ready")
                if icp_error:
                    self.my_logger.error(
                        "CANT_COMPLY - missile type: %s", eng_order.type_icp
                    )
                await self.sent_eng_status(
                    eng_order,
                    ECOPEngagementHandling.CANT_COMPLY,
                    status=ECOPMCUEngagementStatus.CLOSED,
                    icp_stage=EICPStage.ON_GROUND,
                    stat_name="COP_C2_Eng_Status_cant",
                )
            else:
                if eng_order.is_autonomous:
                    self.current_cop_status.cop_toplite_engagement_indication = (
                        ECOPTopliteEngagementIndication.GROUND_ENGAGEMENT
                    )
                await self.sent_eng_status(
                    eng_order,
                    ECOPEngagementHandling.WILL_COMPLY,
                    status=ECOPMCUEngagementStatus.OPENED,
                    icp_stage=EICPStage.ON_GROUND,
                    stat_name="COP_C2_Eng_Status_will",
                )
        return False

    def check_inventory(self) -> None:
        """Check if there are enough missiles"""
        if self.inventory.total_missiles() <= 0:
            self.mcu_not_ready_status.set_no_missile_available(ENoYesBoolean.YES)
            self.my_logger.error("No enough missiles")
        else:
            self.mcu_not_ready_status.set_no_missile_available(ENoYesBoolean.NO)

    async def send_multiple_status(
        self,
        eng_order: EngagementOrder,
        handling: ECOPEngagementHandling,
        status: ECOPMCUEngagementStatus,
        icp_stage: EICPStage,
        stat_name: str,
    ) -> None:
        """Send 3 time status"""
        for i in range(3):
            await self.sent_eng_status(
                eng_order, handling, status, icp_stage, stat_name
            )
            if i >= 2:
                break
            await asyncio.sleep(0.25)

    async def try_to_finish_engagement(
        self, eng_key: EngagementKey, eng_order: EngagementOrder
    ) -> None:
        """Try to finish engagement"""
        if eng_order.aborted:
            await self.send_multiple_status(
                eng_order,
                ECOPEngagementHandling.WILL_PRO,
                status=ECOPMCUEngagementStatus.CLOSED,
                icp_stage=EICPStage.ON_GROUND,
                stat_name="COP_C2_Eng_Status_abort_done",
            )
            self.engagements.delete(eng_order, True)
        else:
            if eng_order.ready_to_delete and not eng_order.closed_sent:
                await self.send_multiple_status(
                    eng_order,
                    ECOPEngagementHandling.WILL_PRO,
                    status=ECOPMCUEngagementStatus.CLOSED,
                    icp_stage=EICPStage.ON_GROUND,
                    stat_name="COP_C2_Eng_Status_abort_done",
                )
                eng_order.closed_sent = True
            else:
                await self.send_multiple_status(
                    eng_order,
                    ECOPEngagementHandling.WILL_PRO,
                    status=ECOPMCUEngagementStatus.ABORT_PENDING,
                    icp_stage=EICPStage.ON_GROUND,
                    stat_name="COP_C2_Eng_Status_done",
                )
            self.engagements.delete(eng_order, False)
            target = eng_order.missile.target
            if target.asp_track is not None:
                fcr_id = target.asp_track.fcr_track_id
            else:
                fcr_id = 0
            if not target.activated:
                if self.destroyed_planes.get(fcr_id) is None:
                    self.destroyed_planes[fcr_id] = {"active": False}

        if eng_order.ready_to_delete and eng_order.closed_sent:
            self.engagements.real_delete(eng_key)

    def update_inventory(self, eng_order: EngagementOrder) -> bool:
        """Update number of missiles in inventory

        Args:
            eng_order (EngagementOrder): _description_
        """
        return self.inventory.fire_missile(eng_order.type_icp)

    async def launch_missile_eng_status(
        self, eng_order: EngagementOrder, mll_time: int = 0
    ) -> None:
        """Process regarding missile launching"""
        eng_order.start_eng(
            self.cop_conf.mfu_lat,
            self.cop_conf.mfu_lon,
            self.cop_conf.mfu_alt,
            mll_time,
        )
        await self.send_multiple_status(
            eng_order,
            ECOPEngagementHandling.WILL_PRO,
            status=ECOPMCUEngagementStatus.OPENED,
            icp_stage=EICPStage.ON_AIR,
            stat_name="COP_C2_Eng_Status_MissileStart",
        )

    async def update_missile_position(self, eng_order: EngagementOrder) -> None:
        """Update missile position"""
        if eng_order.done:
            return
        eng_order.update_missile_current_state()
        self.downlink.update_missile_position(eng_order.cop_eng_id, eng_order.missile)

    async def move_missile_eng_status(self, eng_order: EngagementOrder) -> None:
        """Process regarding missile launching"""
        await self.sent_eng_status(
            eng_order,
            ECOPEngagementHandling.WILL_PRO,
            status=ECOPMCUEngagementStatus.OPENED,
            icp_stage=EICPStage.ON_AIR,
            stat_name="COP_C2_Eng_Status_MissileMove",
        )

    async def sent_eng_status(
        self,
        eng: EngagementOrder,
        handling: ECOPEngagementHandling,
        status: ECOPMCUEngagementStatus,
        icp_stage: EICPStage = EICPStage.ON_GROUND,
        stat_name: str = "COP_C2_Eng_Status",
    ) -> None:
        """sending Engagement Status"""
        msg = COPC2EngagementStatus()
        stat = COPEngagementStatus(eng.track_id, eng.ccu_id, eng.cop_eng_id)
        stat.engagement_launch_time = eng.mll_time
        abort_source = (
            ECOPEngagementAbortSource.COP
            if eng.aborted
            else ECOPEngagementAbortSource.NO_ABORT
        )
        stat.mcu_engagement = COPMCUEngagement(handling, status, abort_source)

        derby_lock = EICPLockStatus.NOT_LOCKED
        python_lock = EICPLockStatus.NOT_LOCKED
        derby_er_lock = EICPLockStatus.NOT_LOCKED
        derby_lr_lock = EICPLockStatus.NOT_LOCKED
        if eng.lock_policy == ELockingPolicy.LOBL and eng.type_icp == ETypeOfICP.OBJP:
            python_lock = EICPLockStatus.LOCKED

        mis_reason = (
            ECOPMisfireReason.MISFIRE
            if eng.missile.misfire_indication
            else ECOPMisfireReason.EMPTY
        )
        mis_yes_no = (
            ENoYesBoolean.YES if eng.missile.misfire_indication else ENoYesBoolean.NO
        )
        stat.allocated_missile_status = COPAllocatedMissileStatus(
            eng.type_icp,
            python_lock,
            derby_lock,
            derby_er_lock,
            derby_lr_lock,
            icp_stage,
            eng.lock_policy,
            mis_yes_no,
            mis_reason,
        )
        msg.add_eng_status(stat)
        msg_data = msg.pack()
        data = self.header_gen.get_header_msg(
            COPC2EngagementStatus.get_msg_type(),
            msg_data,
            self.cop_physical_id,
            self.safety.is_oper(),
        )
        if (
            self.stop_sending.get_state(EMsgType.COP_ENG_STATUS)
            and self.loop is not None
        ):
            await self.loop.sock_sendto(
                self.cop_send_sock, data, (self.cop_conf.bmc_ip, self.cop_conf.bmc_port)
            )
            self.statistics.incr(stat_name)
        else:
            self.my_logger.error("Sending of COP Eng Status is disabled.")

    def set_simulation_abort(self, abort: bool | None) -> None:
        if abort:
            self.do_abort()

    def set_simulation_cop_not_ready(
        self, cop_not_ready: SimCOPNotReady | None
    ) -> None:
        if cop_not_ready is not None:
            self.cop_not_ready_status.convert_sim_cop_not_ready(cop_not_ready)

    def set_simulation_mcu_not_ready(
        self, mcu_not_ready: SimMCUNotReady | None
    ) -> None:
        if mcu_not_ready is not None:
            self.mcu_not_ready_status.convert_sim_mcu_not_ready(mcu_not_ready)

    def randomize_cabin_azimuth(self) -> None:
        """Randomize cabin azimuth"""
        self.current_cop_status.cabin_azimuth = uniform(0, 2 * pi)

    def randomize_turret_azimuth(self) -> None:
        """Randomize turret azimuth"""
        if self.mfu_type == ECOPType.SR:
            self.current_cop_status.turret_azimuth = uniform(0, 2 * pi)
            self.current_cop_status.toplite_azimuth = uniform(0, 2 * pi)
            self.current_cop_status.toplite_elevation = uniform(0, 2 * pi)
            self.current_cop_status.toplite_target_range = randint(0, 10000)

    def generate_sr_sectors(self) -> None:
        if self.mfu_type == ECOPType.SR:
            self.current_cop_status.clear_no_launch_sectors()
            for _ in range(randint(0, 3)):
                start_azimuth = uniform(0, 360)
                sector_size = uniform(30, 150)
                self.current_cop_status.add_no_launch_sector(
                    COPSectorElement(int(start_azimuth), int(sector_size))
                )

            start_azimuth = uniform(0, 360)
            sector_size = uniform(30, 240)
            self.current_cop_status.mechanical_limit = COPSectorElement(
                int(start_azimuth), int(sector_size)
            )
