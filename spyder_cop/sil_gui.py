#!/usr/bin/env python3
"""
Author:   --<>
Purpose:
Created: 03/15/2023
"""

import asyncio
import os
from enum import Enum, auto
from logging import Logger
from math import radians
from typing import Callable

from spyder_cop.messages.util import ECOPType, ETypeOfICP

from .cop_data.GuiStatus import Gui<PERSON>tat<PERSON>
from .icopserver import ICOPServer


class MenuItem(Enum):
    """menu items"""

    MCU_NOT_READY_MENU = auto()
    COP_NOT_READY_MENU = auto()
    COMPONENT_MENU = auto()
    LM_READINESS_MENU = auto()
    COP_STATE_MENU = auto()
    SAFETY_MENU = auto()


class GuiMenuItem:
    """Gui Menu"""

    def __init__(self, menu_item: MenuItem) -> None:
        self.menu_item = menu_item
        self._current_menu = ""

    @property
    def current_menu(self) -> str:
        """current menu"""
        return self._current_menu

    @current_menu.setter
    def current_menu(self, value: str) -> None:
        """current menu"""
        self._current_menu = value


class Phases(Enum):
    """GUI Phases"""

    PHASE0_START = 0

    PHASE1_COP_STATE = 1

    PHASE2_LM_READINESS = 2

    PHASE3_MCU_NOT_READY = 3

    PHASE4_COP_NOT_READY = 4

    PHASE5_COMPONENT_STATUS = 5

    PHASE6_INTERNAL_DATA = 6
    PHASE7_ASP_TRACKS = 7
    PHASE8_ENGAGED_TRACKS = 8
    PHASE9_BMC_STATUS = 9
    PHASE10_ENGAGEMENTS = 10
    PHASE11_SAFETY = 11
    PHASE12_STOP_SENDING = 12
    PHASE13_SIM_CONFIG = 13
    PHASE100_STATISTICS = 100


class Gui:
    """Gui"""

    @staticmethod
    def _display_phase(status_obj: GuiStatus, sub_menu: GuiMenuItem) -> str:
        """Display Phase"""
        if not sub_menu.current_menu:
            return status_obj.get_gui_menu()
        return status_obj.get_options(int(sub_menu.current_menu))

    @staticmethod
    def _input_phase(
        status_obj: GuiStatus, sub_menu: GuiMenuItem, user_input: str, phase: Phases
    ) -> tuple[bool, Phases, None]:
        """Phase Inputs"""
        change_menu = False
        if not sub_menu.current_menu and user_input.isdigit():
            sub_menu.current_menu = user_input
            change_menu = True
        if user_input == "q":
            if sub_menu.current_menu:
                sub_menu.current_menu = ""
                return False, phase, None
            else:
                return True, Phases.PHASE0_START, None

        if user_input.isdigit() and (not change_menu):
            try:
                status_obj.set_options(int(sub_menu.current_menu), int(user_input))
                sub_menu.current_menu = ""
            except IndexError:
                sub_menu.current_menu = ""
        return False, phase, None

    @staticmethod
    def _input_phase13(user_input: str) -> tuple[bool, Phases, None]:
        """Safety inputs"""
        if user_input == "q":
            return True, Phases.PHASE0_START, None

        return False, Phases.PHASE13_SIM_CONFIG, None

    def _display_phase13(self, *_params: list[str]) -> str:
        """Current Sim config the GUI"""
        return self.cop_server.get_sim_config()

    def _input_phase12(self, user_input: str) -> tuple[bool, Phases, None]:
        """Safety inputs"""
        if user_input == "q":
            return True, Phases.PHASE0_START, None
        self.cop_server.get_stop_send().toggle_msg_type(user_input)
        return False, Phases.PHASE12_STOP_SENDING, None

    def _display_phase12(self, *_params: list[str]) -> str:
        """safety switches in the GUI"""
        stop_send = self.cop_server.get_stop_send()
        return stop_send.display_all

    def _input_phase11(self, user_input: str) -> tuple[bool, Phases, None]:
        """input_phase3"""
        return self._input_phase(
            self.cop_server.get_cop_safety(),
            self.safety_menu,
            user_input,
            Phases.PHASE11_SAFETY,
        )

    def _display_phase11(self, *_params: list[str]) -> str:
        """display_phase3"""
        return self._display_phase(self.cop_server.get_cop_safety(), self.safety_menu)

    def _input_phase10(self, user_input: str) -> tuple[bool, Phases, None]:
        """input_phase10"""
        if user_input == "q":
            return True, Phases.PHASE0_START, None
        if user_input == "a":
            self.cop_server.close_all_missiles_in_air()
            return False, Phases.PHASE10_ENGAGEMENTS, None
        else:
            try:
                eng_id = int(user_input)
                if eng_id > 0:
                    self.cop_server.toggle_misfire_indicator(eng_id)
            except ValueError:
                pass
        return False, Phases.PHASE10_ENGAGEMENTS, None

    def _display_phase10(self, *_params: list[str]) -> str:
        """_display_phase10"""
        ret = f"Engagements ({self.cop_server.get_engagements_cnt()}):\n"
        engagements = self.cop_server.get_engagements()
        ret += engagements.get_report()
        ret += "[a] finish all missiles in the air \n\n"
        ret += "[q] Quit\n\n"
        return ret

    @staticmethod
    def _input_phase8(user_input: str) -> tuple[bool, Phases, None]:
        """input_phase8"""
        if user_input == "q":
            return True, Phases.PHASE0_START, None
        return False, Phases.PHASE8_ENGAGED_TRACKS, None

    def _display_phase8(self, *_params: list[str]) -> str:
        """display_phase8"""
        ret = f"Engaged tracks ({self.cop_server.get_eng_tracks().get_tracks_cnt()}):\n"
        eng_track = self.cop_server.get_eng_tracks()
        ret += eng_track.get_report()
        ret += "q. Quit\n\n"
        return ret

    def _input_phase100(self, user_input: str) -> tuple[bool, Phases, None]:
        """input_phase100"""
        if user_input == "0":
            self.cop_server.reset_statistics()
        if user_input == "q":
            return True, Phases.PHASE0_START, None
        return False, Phases.PHASE100_STATISTICS, None

    def _display_phase100(self, *_params: list[str]) -> str:
        """display_phase8"""
        ret = "Statistics: 0 ... Reset\n\n"
        ret += self.cop_server.get_statistics_report()
        ret += "q. Quit\n\n"
        return ret

    def _input_phase7(self, user_input: str) -> tuple[bool, Phases, None]:
        """input_phase7"""
        id_plane = 0
        try:
            id_plane = int(user_input)
        except ValueError:
            pass
        if user_input == "q":
            return True, Phases.PHASE0_START, None
        elif id_plane > 0:
            self.cop_server.try_create_autonomous_engagement(id_plane)
        return False, Phases.PHASE7_ASP_TRACKS, None

    def _display_phase7(self, *_params: list[str]) -> str:
        """display_phase7"""
        ret = f"ASP tracks ({self.cop_server.get_asp_tracks().get_tracks_cnt()}):\n"
        asp_track = self.cop_server.get_asp_tracks()
        ret += asp_track.get_report()
        ret += "q. Quit\n\n"
        return ret

    def _input_phase6(self, user_input: str) -> tuple[bool, Phases, None]:
        """input_phase6"""
        if user_input == "0":
            self.cop_server.reset()
        if user_input == "1":
            self.cop_server.reload_missiles()
        if user_input == "2":
            self.cop_server.deplete_missiles()
        if user_input == "3":
            self.cop_server.increment_missiles(ETypeOfICP.OBJP)
        if user_input == "4":
            self.cop_server.increment_missiles(ETypeOfICP.DERBY)
        if user_input == "5":
            self.cop_server.increment_missiles(ETypeOfICP.ER_DERBY)
        if user_input == "6":
            self.cop_server.increment_missiles(ETypeOfICP.LR_DERBY)
        if user_input == "7":
            self.cop_server.randomize_cabin_azimuth()
        if user_input == "8":
            self.cop_server.randomize_turret_azimuth()
        if user_input == "9":
            self.cop_server.generate_sr_sectors()
        if user_input == "q":
            return True, Phases.PHASE0_START, None
        return False, Phases.PHASE6_INTERNAL_DATA, None

    def _display_phase6(self, *_params: list[str]) -> str:
        """display_phase6"""
        mfu_req_state = self.cop_server.get_mfu_required_state()
        if mfu_req_state is None:
            mfu_req_state_str = "Not set yet."
        else:
            mfu_req_state_str = str(mfu_req_state)

        inventory = self.cop_server.get_inventory()
        cabin_az = self.cop_server.get_cop_status().cabin_azimuth
        turret_azimuth = self.cop_server.get_cop_status().turret_azimuth
        mfu_type = self.cop_server.get_mfu_type()
        mechanical_limit = self.cop_server.get_cop_status().mechanical_limit
        no_launch_sectors = self.cop_server.get_cop_status().no_launch_sectors

        # Build the base output string
        ret = f"""
    INTERNAL DATA:
    {'=' * 50}
    [0] ... Reset
        
    INVENTORY:
    {'-' * 20}
        Python:     {inventory.python}
        Derby:      {inventory.derby}
        Derby ER:   {inventory.derby_er}
        Derby LR:   {inventory.derby_lr}
        
        [1] ... Reload Inventory
        [2] ... Deplete Inventory
        
        [3] ... Increment Python
        [4] ... Increment Derby
        [5] ... Increment Derby ER
        [6] ... Increment Derby LR
        
    MFU CONFIGURATION:
    {'-' * 20}
        MFU Required state from BMC:
        {mfu_req_state_str}
        
        Logical ID:     {self.cop_server.get_mfu_logical_id()}
        Physical ID:    {self.cop_server.get_mfu_physical_id()}
        MCU Serial ID:  {self.cop_server.get_mfu_mcu_serial_id()}
        Location:       {self.cop_server.get_mfu_location()}
        MFU type:       {mfu_type}
        
        [7] ... Cabin Azimuth:   {cabin_az:.2f} [deg] ({radians(cabin_az):.2f} rad)"""

        # Add turret azimuth and sector information only for SR type
        if mfu_type == ECOPType.SR:
            ret += f"""
        [8] ... Turret Azimuth:   {turret_azimuth:.2f} [deg] ({radians(turret_azimuth):.2f} rad)
        [9] ... Random no_launch_sectors and mechanical_limit

        Mechanical Limit:  {(f"Start: {mechanical_limit.start_azimuth}° ({radians(mechanical_limit.start_azimuth):.2f} rad), "
                             f"Size: {mechanical_limit.sector_size}° ({radians(mechanical_limit.sector_size):.2f} rad)")
            if mechanical_limit else "Not set"}
        No Launch Sectors: {"None" if not no_launch_sectors else ""}"""

            if no_launch_sectors:
                for idx, sector in enumerate(no_launch_sectors, 1):
                    ret += (
                        f"\n            {idx}. Start: {sector.start_azimuth}° ({radians(sector.start_azimuth):.2f} rad), "
                        f"Size: {sector.sector_size}° ({radians(sector.sector_size):.2f} rad)"
                    )

        # Add status information
        ret += f"""

    STATUS INFORMATION:
    {'-' * 20}
        ASP Tracks:         {self.cop_server.get_asp_tracks().get_tracks_cnt()}
        Engaged Targets:    {self.cop_server.get_eng_tracks().get_tracks_cnt()}
        Engagements:        {self.cop_server.get_engagements_cnt()}
        
        Simulation state:   {self.cop_server.get_current_sim_command()}
        
    [q] Quit
    """
        return ret

    @staticmethod
    def _input_phase9(user_input: str) -> tuple[bool, Phases, None]:
        """input_phase9"""
        if user_input == "q":
            return True, Phases.PHASE0_START, None
        return False, Phases.PHASE9_BMC_STATUS, None

    def _display_phase9(self, *_params: list[str]) -> str:
        """display_phase9"""
        ret = "BMC STATUS:\n"
        if self.cop_server.get_bmc_status() is not None:
            ret += (
                f"C2_Mode\t\t\t\t{self.cop_server.get_bmc_status().cc_mode}\n"
                f"C2_Substate\t\t\t{self.cop_server.get_bmc_status().cc_substate}\n"
                f"C2_Safety_State\t\t\t{self.cop_server.get_bmc_status().cc_safety_state}\n"
                "C2_Fire_Readiness_Status\t"
                f"{self.cop_server.get_bmc_status().cc_fire_readiness_status}\n"
                f"C2_Status\t\t\t{self.cop_server.get_bmc_status().cc_status}\n"
            )
        else:
            ret += "BMC not connected...\n"
        ret += "q. Quit\n\n"
        return ret

    def _input_phase1(self, user_input: str) -> tuple[bool, Phases, None]:
        """input_phase3"""
        return self._input_phase(
            self.cop_server.get_cop_state(),
            self.cop_state_menu,
            user_input,
            Phases.PHASE1_COP_STATE,
        )

    def _display_phase1(self, *_params: list[str]) -> str:
        """display_phase3"""
        return self._display_phase(self.cop_server.get_cop_state(), self.cop_state_menu)

    def _input_phase2(self, user_input: str) -> tuple[bool, Phases, None]:
        """input_phase3"""
        return self._input_phase(
            self.cop_server.get_cop_lm_readiness(),
            self.lm_readiness_menu,
            user_input,
            Phases.PHASE2_LM_READINESS,
        )

    def _display_phase2(self, *_params: list[str]) -> str:
        """display_phase3"""
        return self._display_phase(
            self.cop_server.get_cop_lm_readiness(), self.lm_readiness_menu
        )

    def _input_phase3(self, user_input: str) -> tuple[bool, Phases, None]:
        """input_phase3"""
        return self._input_phase(
            self.cop_server.get_mcu_not_ready_reason(),
            self.mcu_not_ready_menu,
            user_input,
            Phases.PHASE3_MCU_NOT_READY,
        )

    def _display_phase3(self, *_params: list[str]) -> str:
        """display_phase3"""
        return self._display_phase(
            self.cop_server.get_mcu_not_ready_reason(), self.mcu_not_ready_menu
        )

    def _input_phase4(self, user_input: str) -> tuple[bool, Phases, None]:
        """input_phase4"""
        return self._input_phase(
            self.cop_server.get_cop_not_ready_reason(),
            self.cop_not_ready_menu,
            user_input,
            Phases.PHASE4_COP_NOT_READY,
        )

    def _display_phase4(self, *_params: list[str]) -> str:
        """display_phase3"""
        return self._display_phase(
            self.cop_server.get_cop_not_ready_reason(), self.cop_not_ready_menu
        )

    def _input_phase5(self, user_input: str) -> tuple[bool, Phases, None]:
        """input_phase4"""
        return self._input_phase(
            self.cop_server.get_cop_component_status(),
            self.component_menu,
            user_input,
            Phases.PHASE5_COMPONENT_STATUS,
        )

    def _display_phase5(self, *_params: list[str]) -> str:
        """display_phase3"""
        return self._display_phase(
            self.cop_server.get_cop_component_status(), self.component_menu
        )

    @staticmethod
    def _input_phase_start(user_input: str) -> tuple[bool, Phases, None]:
        """input_phase0"""

        phase = Phases.PHASE0_START
        try:
            input_phase = int(user_input)
            if input_phase in (
                Phases.PHASE0_START.value,
                Phases.PHASE1_COP_STATE.value,
                Phases.PHASE2_LM_READINESS.value,
                Phases.PHASE3_MCU_NOT_READY.value,
                Phases.PHASE4_COP_NOT_READY.value,
                Phases.PHASE5_COMPONENT_STATUS.value,
                Phases.PHASE6_INTERNAL_DATA.value,
                Phases.PHASE7_ASP_TRACKS.value,
                Phases.PHASE8_ENGAGED_TRACKS.value,
                Phases.PHASE9_BMC_STATUS.value,
                Phases.PHASE10_ENGAGEMENTS.value,
                Phases.PHASE11_SAFETY.value,
                Phases.PHASE12_STOP_SENDING.value,
                Phases.PHASE13_SIM_CONFIG.value,
                Phases.PHASE100_STATISTICS.value,
            ):
                phase = Phases(input_phase)
        except ValueError:
            if user_input == "q":
                return True, phase, None

        return False, phase, None

    @staticmethod
    def _display_phase_start(*_params) -> str:
        """Phase 0"""
        return (
            f"Choose a phase:\n"
            f"[{Phases.PHASE1_COP_STATE.value}]\tSet/Display COP_State\n"
            f"[{Phases.PHASE2_LM_READINESS.value}]\tSet/Display LM_Readiness\n"
            f"[{Phases.PHASE3_MCU_NOT_READY.value}]\tSet/Display MCU_Not_Ready_Reason\n"
            f"[{Phases.PHASE4_COP_NOT_READY.value}]\tSet/Display COP_Not_Ready_Reason\n"
            f"[{Phases.PHASE5_COMPONENT_STATUS.value}]\tSet/Display Component Status\n"
            f"[{Phases.PHASE6_INTERNAL_DATA.value}]\tInternal data\n"
            f"[{Phases.PHASE7_ASP_TRACKS.value}]\tASP tracks\n"
            f"[{Phases.PHASE8_ENGAGED_TRACKS.value}]\tDisplay Engaged Tracks\n"
            f"[{Phases.PHASE9_BMC_STATUS.value}]\tDisplay BMC Status\n"
            f"[{Phases.PHASE10_ENGAGEMENTS.value}]\tEngagements\n"
            f"[{Phases.PHASE11_SAFETY.value}]\tSafety\n"
            f"[{Phases.PHASE12_STOP_SENDING.value}]\tStop sending msg_type\n"
            f"[{Phases.PHASE13_SIM_CONFIG.value}]\tSIM config\n"
            f"[{Phases.PHASE100_STATISTICS.value}]\tStatistics\n"
            "\n[q]\tQuit\n"
        )

    def __init__(
        self, cop_server: ICOPServer, gui_address: tuple[str, int], log_fac: Logger
    ) -> None:
        """Constructor"""
        self.cop_server = cop_server
        self.gui_address = gui_address
        self.current_phase = Phases.PHASE0_START
        self.logger = log_fac

        self.mcu_not_ready_menu = GuiMenuItem(MenuItem.MCU_NOT_READY_MENU)
        self.cop_not_ready_menu = GuiMenuItem(MenuItem.COP_NOT_READY_MENU)
        self.component_menu = GuiMenuItem(MenuItem.COMPONENT_MENU)
        self.lm_readiness_menu = GuiMenuItem(MenuItem.LM_READINESS_MENU)
        self.cop_state_menu = GuiMenuItem(MenuItem.COP_STATE_MENU)
        self.safety_menu = GuiMenuItem(MenuItem.SAFETY_MENU)

        self.stop_event_safety = asyncio.Event()

        self.phases: dict[Phases, dict[str, Callable]] = {
            Phases.PHASE0_START: {
                "display": self._display_phase_start,
                "input": self._input_phase_start,
            },
            Phases.PHASE1_COP_STATE: {
                "display": self._display_phase1,
                "input": self._input_phase1,
            },
            Phases.PHASE2_LM_READINESS: {
                "display": self._display_phase2,
                "input": self._input_phase2,
            },
            Phases.PHASE3_MCU_NOT_READY: {
                "display": self._display_phase3,
                "input": self._input_phase3,
            },
            Phases.PHASE4_COP_NOT_READY: {
                "display": self._display_phase4,
                "input": self._input_phase4,
            },
            Phases.PHASE5_COMPONENT_STATUS: {
                "display": self._display_phase5,
                "input": self._input_phase5,
            },
            Phases.PHASE6_INTERNAL_DATA: {
                "display": self._display_phase6,
                "input": self._input_phase6,
            },
            Phases.PHASE7_ASP_TRACKS: {
                "display": self._display_phase7,
                "input": self._input_phase7,
            },
            Phases.PHASE8_ENGAGED_TRACKS: {
                "display": self._display_phase8,
                "input": self._input_phase8,
            },
            Phases.PHASE9_BMC_STATUS: {
                "display": self._display_phase9,
                "input": self._input_phase9,
            },
            Phases.PHASE10_ENGAGEMENTS: {
                "display": self._display_phase10,
                "input": self._input_phase10,
            },
            Phases.PHASE11_SAFETY: {
                "display": self._display_phase11,
                "input": self._input_phase11,
            },
            Phases.PHASE12_STOP_SENDING: {
                "display": self._display_phase12,
                "input": self._input_phase12,
            },
            Phases.PHASE13_SIM_CONFIG: {
                "display": self._display_phase13,
                "input": self._input_phase13,
            },
            Phases.PHASE100_STATISTICS: {
                "display": self._display_phase100,
                "input": self._input_phase100,
            },
        }

    def is_gui_stopped(self) -> bool:
        """Check if gui is stopped"""
        return self.stop_event_safety.is_set()

    async def _handle_client(
        self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter
    ) -> None:
        """handle gui"""
        welcome_message = (
            "Welcome to the COP server ver "
            + self.cop_server.get_version()
            + os.linesep
        )
        client_address = writer.get_extra_info("peername")
        self.logger.info(f"Connected client:  {client_address}")
        writer.write(welcome_message.encode())
        params = None
        while True:
            display_method: str = self.phases[self.current_phase]["display"](params)
            writer.write(display_method.encode())
            data: bytes = await reader.readline()

            try:
                command: str = data.decode(encoding="utf-8").strip()
                if command is not None:
                    response = f"You entered: {command} {os.linesep}"
                    prev_phase = self.current_phase

                    (finnish, self.current_phase, params) = self.phases[
                        self.current_phase
                    ]["input"](command)

                    writer.write(response.encode())
                    if finnish and prev_phase == Phases.PHASE0_START:
                        self.close_remote_connection(writer)
                        return None
                else:
                    self.logger.error("_handle_client - Client disconnect")
                    self.close_remote_connection(writer)
                    return None
            except Exception as ex:
                self.logger.error("_handle_client - Exception %s", ex)
                break

    async def start_server(self) -> None:
        """Start socket"""
        server = await asyncio.start_server(
            self._handle_client, host=self.gui_address[0], port=self.gui_address[1]
        )

        print(f"Server started on {self.gui_address}")
        self.logger.info(f"Server started on {self.gui_address}")

        async with server:
            await server.serve_forever()

    @staticmethod
    def close_remote_connection(writer: asyncio.StreamWriter) -> None:
        """Close remote connection

        Args:
            writer (asyncio.StreamWriter): _description_
        """
        writer.close()
