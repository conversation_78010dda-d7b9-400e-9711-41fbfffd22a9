#!/usr/bin/env python3
# coding:utf-8
"""
Author:   --<>
Purpose:
Created: 03/10/2023
"""
import asyncio
import datetime
import math
from enum import Enum
from logging import Logger
from random import uniform
from time import monotonic, time
from typing import Any, Callable, List, Type, TypeVar, cast

from spyder_bmc.geoutil import GeoUtil

from spyder_cop.messages.class_C2_COP_ASP_Track import ASPTrack
from spyder_cop.messages.class_C2_COP_Engaged_Track import C2COPEngagedTrack
from spyder_cop.messages.util import ECEFPosition, ECOPType, ETypeOfICP, LLLNPosition

T = TypeVar("T")
EnumT = TypeVar("EnumT", bound=Enum)


def from_float(val: Any) -> float:
    """From float"""
    if isinstance(val, (float, int)):
        return float(val)
    if isinstance(val, str):
        try:
            return float(val)
        except ValueError:
            return 0.0
    if val is None:
        raise ValueError("Value is None")

    return 0.0


def from_bool(val: Any) -> bool:
    """From bool"""
    if isinstance(val, bool):
        return val
    if val is None:
        raise ValueError("Value is None")
    return False


def to_float(val: Any) -> float:
    """To float"""
    if isinstance(val, float):
        return val
    if val is None:
        raise ValueError("Value is None")
    try:
        return float(val)
    except ValueError:
        return 0.0


def from_list(ret_list: Callable[[Any], T], fr_list: Any) -> List[T]:
    """From list"""
    #     assert isinstance(x, list)
    return [ret_list(y) for y in fr_list]


def from_str(val: Any) -> str:
    """From str"""
    if isinstance(val, str):
        return val
    if val is None:
        raise ValueError("Value is None")
    return ""


def from_int(val: Any) -> int:
    """From int"""
    if isinstance(val, int):
        return val
    if val is None:
        raise ValueError("Value is None")
    return 0


def from_none(val: Any) -> Any:
    """From ANY"""
    return val


def from_union(src_union: Any, val: Any) -> Any | None:
    """From union"""
    if val is None:
        return None
    for union_item in src_union:
        try:
            ret = union_item(val)
            if ret is not None:
                return ret
        except ValueError:
            pass
    return None


def to_class(c: Type[T], class_val: Any) -> dict:
    """To class"""
    #     assert isinstance(x, c)
    return cast(Any, class_val).to_dict()


class COPPlane:
    """COP_Plane"""

    def __init__(self, track_id: int, asp_track: ASPTrack | None) -> None:
        """Constructor"""
        self.track_id = track_id
        self.asp_track = asp_track
        self.activated = True
        self.pos = ECEFPosition(0, 0, 0)
        self.airplane_pos = LLLNPosition(0, 0, 0)
        self.tra_upd_time = 0.0
        self._receive_abs_time = monotonic()

    @property
    def receive_abs_time(self) -> float:
        """getter"""
        return self._receive_abs_time

    @receive_abs_time.setter
    def receive_abs_time(self, upd_time: float) -> None:
        """setter"""
        self._receive_abs_time = upd_time

    def __str__(self) -> str:
        return (
            f"Target: ID: {self.track_id}, \n"
            f"ECEF_POS: {self.pos},\n"
            f"Pos: {self.airplane_pos}\n"
            f"upd_time: {self.tra_upd_time}, Alive: {self.activated}"
        )


class MissileLocation:
    """Missile Location"""

    def __init__(
        self,
        miss_id: int,
        miss_lat: float,
        miss_lon: float,
        miss_alt: float,
        bearing: float,
        miss_type: str,
        target_id: int,
        detonation: bool,
        cop_id: int,
    ) -> None:
        """Constructor"""
        self.miss_id = miss_id
        self.miss_lat = miss_lat
        self.miss_lon = miss_lon
        self.miss_alt = miss_alt
        self.bearing = bearing
        self.miss_type = miss_type
        self.target_id = target_id
        self.detonation = detonation
        self.cop_id = cop_id

    def to_dict(self) -> dict[str, Any]:
        """Serialize to dict"""
        result: dict[str, Any] = {
            "miss_id": from_int(self.miss_id),
            "miss_lat": from_float(self.miss_lat),
            "miss_lon": from_float(self.miss_lon),
            "miss_alt": from_float(self.miss_alt),
            "bearing": from_float(self.bearing),
            "miss_type": from_str(self.miss_type),
            "target_id": from_int(self.target_id),
            "detonation": from_bool(self.detonation),
            "cop_id": from_int(self.cop_id),
        }
        return result

    @staticmethod
    def from_dict(obj: Any) -> "MissileLocation":
        """Read from dict"""
        miss_id = from_int(obj.get("miss_id"))
        miss_lat = from_float(obj.get("miss_lat"))
        miss_lon = from_float(obj.get("miss_lon"))
        miss_alt = from_float(obj.get("miss_alt"))
        bearing = from_float(obj.get("bearing"))
        miss_type = from_str(obj.get("miss_type"))
        target_id = from_int(obj.get("target_id"))
        detonation = from_bool(obj.get("detonation"))
        cop_id = from_int(obj.get("cop_id"))
        return MissileLocation(
            miss_id,
            miss_lat,
            miss_lon,
            miss_alt,
            bearing,
            miss_type,
            target_id,
            detonation,
            cop_id,
        )


def missile_location_from_dict(s: Any) -> MissileLocation:
    """Read from dict"""
    return MissileLocation.from_dict(s)


def missile_location_to_dict(x: MissileLocation) -> Any:
    """Store in dict"""
    return to_class(MissileLocation, x)


class Missile:
    """Missile"""

    # vzdalenost s ohledem na rychlost, kdy se predpoklada stret s letadlem
    MISSILE_MIN_DISTANCE_KOEF = 0.5

    LATITUDE_METERS_PER_DEGREE = 111319.9
    LONGITUDE_METERS_PER_DEGREE = 111319.9

    # for testing
    # MISSILE_FLIGHT_TIME = {"SR": 5, "MR": 10}
    MISSILE_FLIGHT_TIME = {ECOPType.SR: 65, ECOPType.MR: 130}

    DETONATION_SUCCESSFUL_RATE = 0.95

    def __init__(
        self,
        mis_id: int,
        missile_speed: float,
        target: COPPlane,
        mfu_type: ECOPType,
        type_of_icp: ETypeOfICP,
    ) -> None:
        """Constructor"""
        self.missile_id = mis_id
        self.type_of_icp = type_of_icp
        self.target_id = target.track_id
        self.target = target
        self.in_the_air = False
        self.missile_pos = LLLNPosition(0, 0, 0)
        self.detonation = False
        self.missile_speed = missile_speed
        self.bearing = 0.0
        self.mfu_type = mfu_type
        self.start_time = 0.0
        self.misfire_indication = False
        self.aborted = False
        self.last_update = 0.0
        self.velocity = LLLNPosition(0.0, 0.0, 0.0)

    def set_misfire(self, state: bool) -> None:
        """Set MisFire - Missile onGround error"""
        if not self.in_the_air:
            self.misfire_indication = state

    def start_missile(self) -> None:
        """Start missile"""
        self.start_time = time()
        self.last_update = self.start_time
        self.in_the_air = True

    def detonation_missile(self, success: bool) -> None:
        """Missile detonation"""
        self.detonation = True
        self.in_the_air = False
        if success:
            self.target.activated = False
        else:
            self.target.activated = True

    def abort_missile(self) -> None:
        """Abort missile"""
        self.detonation = True
        self.in_the_air = False
        self.aborted = True

    def is_aborted(self) -> bool:
        """Check if the missile aborted"""
        return self.aborted

    def max_missile_flight_check(self) -> bool:
        """Check if the missile flight enough"""
        max_time = Missile.MISSILE_FLIGHT_TIME.get(self.mfu_type, 100)
        now = time()
        print(f"Missile flight time: {(now - self.start_time)}")
        return (now - self.start_time) > max_time

    def _calculate_missile_detonation_distance(self) -> float:
        """Distance in [m] if it's less than is missile reach the target"""
        return self.missile_speed * Missile.MISSILE_MIN_DISTANCE_KOEF

    def update_missile_position(self) -> None:
        """update_missile_position"""
        if not self.in_the_air:
            return None

        if self.max_missile_flight_check():
            self.detonation_missile(False)
            return None

        # print(f"Plane: {self.target.airplane_pos}")
        # print(f"Missile: {self.missile_pos}")

        longitude_meters_per_degree = Missile.LONGITUDE_METERS_PER_DEGREE * math.cos(
            math.radians(self.missile_pos.x_pos)
        )
        self.bearing = GeoUtil.bearing_rad(
            self.missile_pos.x_pos,
            self.missile_pos.y_pos,
            self.target.airplane_pos.x_pos,
            self.target.airplane_pos.y_pos,
        )
        now = time()
        time_diff = now - self.last_update
        self.last_update = now
        # print(f"Bearing: {math.degrees(bearing)}")
        velocity_lat = (
            self.missile_speed
            * math.cos(self.bearing)
            / Missile.LATITUDE_METERS_PER_DEGREE
        ) * time_diff
        velocity_lon = (
            self.missile_speed * math.sin(self.bearing) / longitude_meters_per_degree
        ) * time_diff
        self.velocity.x_pos = velocity_lat
        self.velocity.y_pos = velocity_lon

        # print(f"velocity_lat: {velocity_lat}, {velocity_lon}")
        self.missile_pos = LLLNPosition(
            self.missile_pos.x_pos + velocity_lat,
            self.missile_pos.y_pos + velocity_lon,
            self.target.airplane_pos.z_pos,
        )
        # print(f"Distance: {self.calc_distance()}")
        distance = (
            GeoUtil.distance(
                self.missile_pos.x_pos,
                self.missile_pos.y_pos,
                self.target.airplane_pos.x_pos,
                self.target.airplane_pos.y_pos,
            )
            * 1000
        )
        if distance <= self._calculate_missile_detonation_distance():
            self.try_to_detonate()

    def try_to_detonate(self) -> None:
        """Detonation successful rate"""
        self.detonation_missile(
            uniform(0, 1) >= (1 - Missile.DETONATION_SUCCESSFUL_RATE)
        )


class TimeUtil:
    """Time Util general methods"""

    @classmethod
    def get_ts_from_mid(cls) -> float:
        """Get [msec] from midnight"""
        utc_tz = datetime.timezone.utc
        current_time = datetime.datetime.now(utc_tz)
        midnight_utc = current_time.replace(
            hour=0, minute=0, second=0, microsecond=0, tzinfo=utc_tz
        )
        time_elapsed = current_time - midnight_utc

        seconds_from_midnight = time_elapsed.total_seconds()
        milliseconds_from_midnight = seconds_from_midnight * 1000
        return float(milliseconds_from_midnight)


class EngagedTracks:
    """EngagedTracks"""

    TIMEOUT_CHECK = 5.0

    def __init__(self, log_fac: Logger) -> None:
        """Constructor"""
        self.engaged_tracks: dict[int, COPPlane] = {}
        self._timeout = 10.0  # 10 seconds
        self.log_fac = log_fac

    async def _timeout_handler(self) -> None:
        """TimeOut handler"""
        while True:
            try:
                for track_id, track in list(self.engaged_tracks.items()):
                    elapsed_time = monotonic() - track.receive_abs_time

                    if abs(elapsed_time) >= self._timeout:
                        self.log_fac.info(
                            f"Timeout EngagedTracks of track_id: {track_id}, "
                            f" upd_time: {track.tra_upd_time}, "
                            f" elapsed: {elapsed_time} sec"
                        )
                        del self.engaged_tracks[track_id]

                await asyncio.sleep(EngagedTracks.TIMEOUT_CHECK)
            except Exception as exc:
                self.log_fac.error(f"EngagedTracks timeout handler error: {exc}")
                continue

    async def start(self) -> None:
        """Start Timeout garbage collector"""
        await self._timeout_handler()

    def reset(self) -> None:
        """Reset"""
        self.engaged_tracks.clear()

    def get_target(self, idt: int) -> COPPlane | None:
        """get_target"""
        return self.engaged_tracks.get(idt, None)

    def add_eng_track(
        self, track_data: C2COPEngagedTrack, asp_track: ASPTrack | None
    ) -> None:
        """add_eng_track"""
        update_diff = monotonic() - track_data.receive_abs_time
        if abs(update_diff) > self._timeout:
            self.log_fac.info(
                f"Ignore EngagedTrack - Timeout: {update_diff} > {self._timeout}"
            )
            return
        idt = track_data.track_id
        eng_track = self.engaged_tracks.get(idt, None)
        if eng_track:
            if eng_track.tra_upd_time < track_data.tra_upd_time:
                eng_track.tra_upd_time = track_data.tra_upd_time
                eng_track.receive_abs_time = track_data.receive_abs_time
                if asp_track is not None:
                    eng_track.pos = asp_track.pos
                    eng_track.airplane_pos = EngagedTracks.convert_ecef2gps(
                        asp_track.pos
                    )
            elif eng_track.tra_upd_time == track_data.tra_upd_time:
                self.log_fac.info("Engaged track - Same upd time")

            else:
                self.log_fac.info("Engaged track - Older upd time")
        else:
            target = COPPlane(idt, asp_track)
            target.tra_upd_time = track_data.tra_upd_time
            if asp_track is not None:
                target.airplane_pos = EngagedTracks.convert_ecef2gps(asp_track.pos)
            self.engaged_tracks[idt] = target

    def add_eng_track_from_asp(self, asp_track: ASPTrack | None) -> COPPlane:
        """add_eng_track_from_asp"""
        if asp_track is None:
            return COPPlane(0, None)

        idt = asp_track.track_id
        target_plane = self.engaged_tracks.get(idt, None)
        if target_plane is None:
            target = COPPlane(idt, asp_track)
            target.tra_upd_time = asp_track.tra_upd_time
            target.airplane_pos = EngagedTracks.convert_ecef2gps(asp_track.pos)
            self.engaged_tracks[idt] = target
            return target
        else:
            return target_plane

    @staticmethod
    def convert_ecef2gps(asp_pos: ECEFPosition) -> LLLNPosition:
        """Convert ASP track position to LLN

        Args:
            asp_pos (ECEFPosition): _description_

        Returns:
            LLLNPosition: _description_
        """
        (plane_lat, plane_lon, plane_alt) = GeoUtil.ecef_to_gps(
            asp_pos.x_pos, asp_pos.y_pos, asp_pos.z_pos, in_rad=False
        )
        return LLLNPosition(plane_lat, plane_lon, plane_alt)

    def get_report(self) -> str:
        """get report for GUI"""
        report = ""
        for track in self.engaged_tracks.values():
            report += f"{track}\n"
            report += "-" * 40
            report += "\n"
        return report

    def get_tracks_cnt(self) -> int:
        """get_tracks_cnt"""
        return len(self.engaged_tracks)

    def __str__(self) -> str:
        """Description"""
        cnt_active = 0
        for track in self.engaged_tracks.values():
            if track.activated:
                cnt_active += 1
        return f"Eng track count = {len(self.engaged_tracks)}," f"active={cnt_active}"


class ASPTracks:
    """GlobalTracks"""

    TIMEOUT_CHECK = 5.0

    def __init__(self, log_fac: Logger) -> None:
        """Constructor"""
        self.global_tracks: dict[int, ASPTrack] = {}
        self.logger = log_fac
        self._timeout = 10.0  # 10 seconds

    async def _timeout_handler(self) -> None:
        """TimeOut handler"""
        while True:
            try:
                for track_id, track in list(self.global_tracks.items()):
                    elapsed_time = monotonic() - track.receive_abs_time

                    if abs(elapsed_time) >= self._timeout:
                        self.logger.info(
                            f"Timeout ASPTracks of track_id: {track_id}, "
                            f" upd_time: {track.tra_upd_time}, "
                            f" elapsed: {elapsed_time} sec"
                        )
                        del self.global_tracks[track_id]

                await asyncio.sleep(ASPTracks.TIMEOUT_CHECK)
            except Exception as ex:
                self.logger.error(f"ASPTracks timeout handler error: {ex}")
                continue

    async def start(self) -> None:
        """Start Timeout garbage collector"""
        await self._timeout_handler()

    def reset(self) -> None:
        """REset"""
        self.global_tracks.clear()

    def add_track(self, track_data: ASPTrack) -> None:
        """add_track"""
        idt = track_data.track_id
        if track := self.global_tracks.get(idt):
            # print(f"{track.tra_upd_time} < {trackData.tra_upd_time}")
            if track.tra_upd_time < track_data.tra_upd_time:
                track.tra_upd_time = track_data.tra_upd_time
                track.receive_abs_time = track_data.receive_abs_time
                track.pos = track_data.pos
                track.iff_special_data = track_data.iff_special_data
                track.target_identity = track_data.target_identity
                track.sif_1_code = track_data.sif_1_code
                track.sif_2_code = track_data.sif_2_code
                track.sif_3_code = track_data.sif_3_code
                track.velocity = track_data.velocity
                track.thread_level = track_data.thread_level
                track.track_status = track_data.track_status
                if track.fcr_track_id != track_data.fcr_track_id:
                    self.logger.error(
                        "FCR track id ERROR: %s -> %s",
                        track.fcr_track_id,
                        track_data.fcr_track_id,
                    )
                if track.tn_str != track_data.tn_str:
                    self.logger.error(
                        "TN ERROR: %s -> %s", track.tn_str, track_data.tn_str
                    )
                track.target_type = track_data.target_type
                track.thread_level = track_data.thread_level
            elif track.tra_upd_time == track_data.tra_upd_time:
                self.logger.info("ASP track - Same Update Time Track!!!!")
            else:
                if track.receive_abs_time < monotonic():
                    track.tra_upd_time = 0
                self.logger.info(
                    "ASP track - Error Update Time older data than I have Track!!!!"
                )
        else:
            self.global_tracks[idt] = track_data

    def get_track(self, track_id: int) -> ASPTrack | None:
        """get_track"""
        if self.global_tracks.get(track_id) is not None:
            return self.global_tracks[track_id]
        return None

    def get_report(self) -> str:
        """get report for GUI"""
        report = ""
        for track in self.global_tracks.values():
            report += f"{track}\n"
            report += "-" * 20
            report += f"\nAutonomous engagement: [{track.track_id}]\n"
            report += "-" * 40
            report += "\n"
        return report

    def get_tracks_cnt(self) -> int:
        """get_tracks_cnt"""
        return len(self.global_tracks)

    def __str__(self) -> str:
        """Description"""
        return f"Tracks count = {len(self.global_tracks)}"
