from enum import Enum

from spyder_cop.config import Config


class EMsgType(Enum):
    """Msg Type to not send"""

    COP_STATUS = "1"
    COP_ENG_STATUS = "2"
    COP_TOPLITE = "3"
    COP_DOWNLINK = "4"


class StopSending:
    """Send message type toggling"""

    def __init__(self, general_conf: Config) -> None:
        """Constructor"""
        self.msg_types: dict[EMsgType, bool] = {
            EMsgType.COP_STATUS: general_conf.enable_sending_cop_status,
            EMsgType.COP_ENG_STATUS: general_conf.enable_sending_cop_eng_status,
            EMsgType.COP_TOPLITE: general_conf.enable_sending_cop_toplite,
            EMsgType.COP_DOWNLINK: general_conf.enable_sending_cop_downlink,
        }

    def toggle_msg_type(self, user_input: str) -> None:
        """Toggle msg type"""
        try:
            msg_type_enum = EMsgType(user_input)
            current_value = self.msg_types[msg_type_enum]
            self.msg_types[msg_type_enum] = not current_value
        except ValueError:
            pass

    def get_state(self, msg_type: EMsgType) -> bool:
        """Get state of msg type"""
        return self.msg_types.get(msg_type, False)

    @property
    def display_all(self) -> str:
        """Display all msg types"""
        state_status = self.get_state(EMsgType.COP_STATUS)
        state_eng_status = self.get_state(EMsgType.COP_ENG_STATUS)
        state_toplite = self.get_state(EMsgType.COP_TOPLITE)
        state_down = self.get_state(EMsgType.COP_DOWNLINK)
        return f"""
        
         Msg Types:
         
        [{EMsgType.COP_STATUS.value}] - COP_STATUS: {state_status}
        [{EMsgType.COP_ENG_STATUS.value}] - COP_ENG_STATUS: {state_eng_status}
        [{EMsgType.COP_TOPLITE.value}] - COP_TOPLITE: {state_toplite}
        [{EMsgType.COP_DOWNLINK.value}] - COP_DOWNLINK: {state_down}
        
        q.Quit
"""
