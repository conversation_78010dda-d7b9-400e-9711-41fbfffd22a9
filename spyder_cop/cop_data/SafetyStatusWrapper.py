#!/usr/bin/env python3
# coding:utf-8
"""
Author:   --<>
Purpose:
Created: 03/10/2023
"""
from enum import Enum

from spyder_simul.simulation import SimFireSource

from spyder_cop.cop_data.GuiStatus import GuiStatus
from spyder_cop.messages.util import EHeaderSimFlag, EInitFireSource, ENoYesBoolean

_SafetyStatus = EInitFireSource | EHeaderSimFlag | ENoYesBoolean

_OPTION_FIRE_SOURCE: dict[int, EInitFireSource] = {
    1: EInitFireSource.REMOTE,
    2: EInitFireSource.LOCAL,
}

_OPTION_HEADER_SIM_FLAG: dict[int, EHeaderSimFlag] = {
    1: EHeaderSimFlag.OPERATIONA_MODE,
    2: EHeaderSimFlag.SIMULATION_MODE,
}

_OPTION_TRUE_FALSE: dict[int, ENoYesBoolean] = {
    1: ENoYesBoolean.NO,
    2: ENoYesBoolean.YES,
}


class SwitchEnum(Enum):
    """Enum for McuNotReadyStatus"""

    SWITCH_MASTER = ("switch_master", 1, _OPTION_TRUE_FALSE)
    SWITCH_OPER = ("switch_oper", 2, _OPTION_HEADER_SIM_FLAG)
    SWITCH_FIRE_KEY = ("switch_fire_key", 3, _OPTION_TRUE_FALSE)
    SWITCH_FIRE = ("switch_fire", 4, _OPTION_TRUE_FALSE)
    SWITCH_AUTONOMOUS = ("switch_autonomous", 5, _OPTION_TRUE_FALSE)
    SWITCH_FIRE_SOURCE = ("switch_fire_source", 6, _OPTION_FIRE_SOURCE)
    SWITCH_MASTER_ABORT = ("switch_master_abort", 7, _OPTION_TRUE_FALSE)

    def __init__(
        self,
        enum_name: str,
        index_value: int,
        dict_option: dict[int, _SafetyStatus],
    ):
        self.enum_name = enum_name
        self.index_value = index_value
        self.dict_option = dict_option

    def __str__(self) -> str:
        return self.enum_name

    @classmethod
    def get_index(cls, enum_name: str) -> int:
        """gets index from enum name"""
        for enum in cls:
            if enum.enum_name == enum_name:
                return enum.index_value
        raise ValueError(f"enum name {enum_name} not found in {cls.__name__}")

    @classmethod
    def get_enum_by_index(cls, index_value: int) -> "SwitchEnum":
        """gets enum from index"""
        for enum in cls:
            if enum.index_value == index_value:
                return enum
        raise ValueError(f"index {index_value} not found in {cls.__name__}")


class Safety(GuiStatus):
    """Safety in COP system."""

    def __init__(self) -> None:
        """
        Initializes the safety switches with default values.
        """
        self._state_changed: bool = False
        self.switches: dict[SwitchEnum, _SafetyStatus] = {
            SwitchEnum.SWITCH_MASTER: ENoYesBoolean.YES,
            SwitchEnum.SWITCH_OPER: EHeaderSimFlag.OPERATIONA_MODE,
            SwitchEnum.SWITCH_FIRE_KEY: ENoYesBoolean.YES,
            SwitchEnum.SWITCH_FIRE: ENoYesBoolean.NO,
            SwitchEnum.SWITCH_AUTONOMOUS: ENoYesBoolean.NO,
            SwitchEnum.SWITCH_FIRE_SOURCE: EInitFireSource.REMOTE,
            SwitchEnum.SWITCH_MASTER_ABORT: ENoYesBoolean.NO,
        }

    def is_abort(self) -> bool:
        """Is Master Abort Pressed"""
        return self.switches[SwitchEnum.SWITCH_MASTER_ABORT] == ENoYesBoolean.YES

    def is_autonomous(self) -> bool:
        """
        Checks if the system is in autonomous mode.

        Returns:
            True if the system is in autonomous mode, False otherwise.
        """
        return self.switches[SwitchEnum.SWITCH_AUTONOMOUS] == ENoYesBoolean.YES

    def is_oper(self) -> EHeaderSimFlag:
        """Is Operational or Simulation"""
        return self.switches[SwitchEnum.SWITCH_OPER]  # type: ignore

    def is_local_fire_source(self) -> bool:
        """Is local fire source"""
        return self.switches[SwitchEnum.SWITCH_FIRE_SOURCE] == EInitFireSource.LOCAL

    def is_remote_fire_source(self) -> bool:
        """Is remote fire source"""
        return self.switches[SwitchEnum.SWITCH_FIRE_SOURCE] == EInitFireSource.REMOTE

    def can_fire(self) -> bool:
        """
        Checks if the system can fire.

        Returns:
            True if the system can fire, False otherwise.
        """
        return (
            self.switches[SwitchEnum.SWITCH_MASTER] == ENoYesBoolean.YES
            and self.switches[SwitchEnum.SWITCH_FIRE] == ENoYesBoolean.YES
            and self.switches[SwitchEnum.SWITCH_FIRE_KEY] == ENoYesBoolean.YES
            and self.switches[SwitchEnum.SWITCH_MASTER_ABORT] == ENoYesBoolean.NO
        )

    def get_gui_menu(self) -> str:
        """description"""
        status_strings = [
            f"[{status.index_value}] ... {status.enum_name.ljust(30)}: \t\t{state.name}"
            for status, state in self.switches.items()
        ]
        return "\n".join(status_strings) + "\n" + "q ... Quit" + "\n" + "Choose: "

    def get_options(self, option: int) -> str:
        """description"""
        try:
            sel_option: SwitchEnum = SwitchEnum.get_enum_by_index(option)
            sel_item: _SafetyStatus | None = self.switches.get(sel_option)
            if sel_item is None:
                return "Unknown choice\nq ... Quit\nChoose: "
            return (
                sel_option.enum_name
                + ": "
                + sel_item.name
                + "\n"
                + "\n".join(
                    f"[{i}]: {state.name}"
                    for i, state in sel_option.dict_option.items()
                )
                + "\nq ... Quit"
                + "\n"
                + "Choose: "
            )
        except ValueError:
            return "Unknown choice\nq ... Quit\nChoose: "

    def set_options(self, option: int, value: int) -> bool:
        """description"""
        try:
            sel_option: SwitchEnum = SwitchEnum.get_enum_by_index(option)
            sel_value = sel_option.dict_option.get(value, None)
            if sel_value is None:
                return False
            self.switches[sel_option] = sel_value  # type: ignore
        except ValueError:
            return False
        return self._state_changed

    def set_fire_source(self, param: SimFireSource | None) -> None:
        if param is None:
            return
        if param == SimFireSource.LOCAL:
            self.switches[SwitchEnum.SWITCH_FIRE_SOURCE] = EInitFireSource.LOCAL
        else:
            self.switches[SwitchEnum.SWITCH_FIRE_SOURCE] = EInitFireSource.REMOTE

    def set_autonomous(self, param: bool) -> None:
        if param:
            self.switches[SwitchEnum.SWITCH_AUTONOMOUS] = ENoYesBoolean.YES
            self.switches[SwitchEnum.SWITCH_FIRE_SOURCE] = EInitFireSource.LOCAL
        else:
            self.switches[SwitchEnum.SWITCH_AUTONOMOUS] = ENoYesBoolean.NO

    def set_sim_flag(self, is_not_sim: bool) -> None:
        if is_not_sim:
            self.switches[SwitchEnum.SWITCH_OPER] = EHeaderSimFlag.OPERATIONA_MODE
        else:
            self.switches[SwitchEnum.SWITCH_OPER] = EHeaderSimFlag.SIMULATION_MODE

    def set_abort(self, abort: bool) -> None:
        if abort:
            self.switches[SwitchEnum.SWITCH_MASTER_ABORT] = ENoYesBoolean.YES
        else:
            self.switches[SwitchEnum.SWITCH_MASTER_ABORT] = ENoYesBoolean.NO

    def is_master_on(self) -> bool:
        return self.switches[SwitchEnum.SWITCH_MASTER] == ENoYesBoolean.YES
