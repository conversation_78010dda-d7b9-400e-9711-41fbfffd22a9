#!/usr/bin/env python3
# coding:utf-8
"""
Author:   --<>
Purpose:
Created: 03/10/2023
"""
from logging import Logger

from spyder_cop.messages.class_C2_COP_ASP_Track import ASPTrack
from spyder_cop.messages.class_C2_COP_Engagement import C2COPEngagement
from spyder_cop.messages.util import (
    EC2EngagementRequest,
    ECOPType,
    EEngagementFireControl2,
    ELockingPolicy,
    ETypeOfICP,
    LLLNPosition,
)

from .ASPTrack import COPPlane, EngagedTracks
from .inventory import Inventory
from .missile import Missile


# import traceback


class EngagementKey:
    """EngagementKey"""

    def __init__(self, track_id: int, ccu_id: int) -> None:
        """Constructor"""
        self.track_id = track_id
        self.ccu_id = ccu_id

    def __eq__(self, other: object) -> bool:
        """EQ"""
        if isinstance(other, type(self)):
            return self.track_id == other.track_id and self.ccu_id == other.ccu_id
        return False

    def __hash__(self) -> int:
        """Hash"""
        return hash((self.track_id, self.ccu_id))

    def __str__(self) -> str:
        return f"[TrackID: {self.track_id}, CCU_ID: {self.ccu_id}]"


class EngagementOrder:
    """EngagementOrder"""

    MAX_DELETE_TIMER = 5

    def __init__(
        self,
        track_id: int,
        ccu_id: int,
        cop_eng_id: int,
        type_of_icp: ETypeOfICP,
        lock_policy: ELockingPolicy,
        target: COPPlane,
        missile_speed: float,
        mfu_type: ECOPType,
        inventory: Inventory,
        is_autonomous: bool = False,
    ) -> None:
        """Constructor"""
        self.track_id: int = track_id
        self.ccu_id: int = ccu_id
        self.cop_eng_id: int = cop_eng_id
        self.mfu_type: ECOPType = mfu_type
        self.type_icp: ETypeOfICP = type_of_icp
        self._lock_policy = lock_policy
        self.missile = Missile(
            cop_eng_id, missile_speed, target, mfu_type, type_of_icp, inventory
        )

        self.is_missile_started: bool = False
        # self.upd_time = 0
        self.done: bool = False
        self.magic_word: int = 0
        self.magic_word_time: int = 0
        self.mll_time: int = 0x0000FFFF  # according ICD 1.3
        self.aborted: bool = False
        self.target: COPPlane = target

        self.ready_to_delete: bool = False
        self.closed_sent: bool = False
        self.delete_time: int = EngagementOrder.MAX_DELETE_TIMER
        self.is_autonomous: bool = is_autonomous
        self.fire_control2: EEngagementFireControl2 = EEngagementFireControl2.HOLD
        self._enable_turret_aim: bool = False

    @property
    def enable_turret_aim(self) -> bool:
        """Getter"""
        if self.mfu_type == ECOPType.SR:
            return self._enable_turret_aim
        return False

    @enable_turret_aim.setter
    def enable_turret_aim(self, value: bool) -> None:
        """Setter"""
        if self.mfu_type != ECOPType.SR:
            return
        self._enable_turret_aim = value

    @property
    def lock_policy(self) -> ELockingPolicy:
        return self._lock_policy

    @lock_policy.setter
    def lock_policy(self, value: ELockingPolicy) -> None:
        if self.mfu_type == ECOPType.MR:
            self._lock_policy = ELockingPolicy.LOAL
        else:
            self._lock_policy = value

    def prepare_to_delete(self, immediate: bool) -> None:
        """Prepare to delete"""
        if immediate:
            self.ready_to_delete = True
            self.closed_sent = True
            return
        else:
            self.delete_time -= 1
            if self.delete_time <= 0:
                self.ready_to_delete = True
                self.finish_engagement()

    def do_abort(self, is_aborted: bool) -> None:
        """Do abort"""
        if is_aborted and not self.aborted:
            self.aborted = True
            self.abort_missile()

    def start_eng(self, lat: float, lon: float, alt: float, mll_time: int = 0) -> None:
        """start_eng"""
        if not self.is_missile_started:
            self.missile.start_missile()
            self.mll_time = mll_time
            self.missile.missile_pos = LLLNPosition(lat, lon, alt)
        self.is_missile_started = True

    def is_magic_word_valid(self) -> bool:
        """is magic word correct to launch missile

        Returns:
            bool: _description_
        """
        return self.magic_word > 0

    def update_missile_current_state(self) -> None:
        """update_missile_current_state"""
        if self.done:
            return
        self.missile.update_missile_position()
        if self.missile.detonation:
            self.finish_engagement()

    def abort_missile(self) -> None:
        """Abort missile"""
        if self.is_missile_started:
            self.missile.abort_missile()
        self.finish_engagement()

    def finish_engagement(self) -> None:
        """Finish engagement"""
        self.is_missile_started = False
        # self.upd_time = 0
        self.done = True

    def __str__(self) -> str:
        """description"""
        return (
            f"Magic_Word: {self.magic_word}\n"
            f"MissileID: {self.missile.missile_id}, "
            f"TargetID: {self.missile.target_id}\n"
            f"Missile start: {self.is_missile_started}, "
            f"Missile done: {self.done}\n"
            f"Misfire Indication: {self.missile.misfire_indication}\n"
        )


class Engagements:
    """Engagements"""

    AUTONOMOUS_ENG_ID_START = 65500
    AUTONOMOUS_ENG_ID_END = 65535

    ENG_ID_START = 1

    def __init__(
        self,
        eng_tracks: EngagedTracks,
        missile_speed: float,
        log_fac: Logger,
        inventory: Inventory,
    ) -> None:
        """Constructor"""
        self.inventory = inventory
        self.engagements: dict[EngagementKey, EngagementOrder] = {}
        self.cop_eng_id = Engagements.ENG_ID_START

        self.cop_autonomous_eng_id = Engagements.AUTONOMOUS_ENG_ID_START
        self.eng_tracks = eng_tracks
        self.missile_speed = missile_speed
        self.logger = log_fac

    def is_any_engagement_enabled_turret_aim(self) -> bool:
        """Check if any engagement is in Turret Aim mode"""
        for eng in self.engagements.values():
            if eng.enable_turret_aim:
                return True
        return False

    def close_all_missiles_in_air(self) -> None:
        for eng in self.engagements.values():
            if eng.missile.in_the_air:
                eng.abort_missile()

    def contain_autonomous_eng(self) -> bool:
        for eng in self.engagements.values():
            if eng.is_autonomous:
                return True
        return False

    def abort_all_engagements(self) -> None:
        """Abort all engagements because Master abort was pressed"""
        for eng in list(self.engagements.values()):
            eng.do_abort(True)
            eng.prepare_to_delete(False)

    def get_next_cop_eng_id(self) -> int:
        """get next cop_engagement id"""
        self.cop_eng_id += 1
        if self.cop_eng_id >= Engagements.AUTONOMOUS_ENG_ID_START:
            self.cop_eng_id = Engagements.ENG_ID_START
        return self.cop_eng_id

    def get_next_cop_autonomous_eng_id(self) -> int:
        """get next cop autonomous engagement id"""
        self.cop_autonomous_eng_id += 1
        if self.cop_autonomous_eng_id >= Engagements.AUTONOMOUS_ENG_ID_END:
            self.cop_autonomous_eng_id = Engagements.AUTONOMOUS_ENG_ID_START
        return self.cop_autonomous_eng_id

    def reset(self) -> None:
        """Reset"""
        self.engagements.clear()

    def real_delete(self, key: EngagementKey) -> None:
        """real delete"""
        del self.engagements[key]

    @staticmethod
    def delete(engagement: EngagementOrder, immediate: bool) -> None:
        """Delete from dict"""
        engagement.prepare_to_delete(immediate)

    def delete_engagement(
        self, engagement: EngagementOrder, delete_req: EC2EngagementRequest
    ) -> None:
        """delete_engagement"""
        # self.logger.error("delete_engagement: %s", eng)

        if not engagement.missile.in_the_air:
            if delete_req == EC2EngagementRequest.DELETE_GROUND_ENGAGEMENT:
                self.logger.debug("Delete not in the air engagement: %s", engagement)
                self.delete(engagement, False)
            else:
                self.logger.error("Missile not in the air - Delete Air request")
        else:
            if delete_req == EC2EngagementRequest.DELETE_AIR_ENGAGEMENT:
                self.logger.debug("Delete in the air engagement: %s", engagement)
                if engagement.aborted:
                    self.delete(engagement, False)
                else:
                    self.logger.error("Missile in the air need to be aborted.")
            else:
                self.logger.error("Missile in the air - Delete Ground request")

    def add_engagement(
        self, eng: C2COPEngagement, target: COPPlane, mfu_type: ECOPType
    ) -> None:
        """add_engagement"""
        self.logger.info("add_engagement: %s - %s", eng, target)
        track_id = eng.track_id
        ccu_id = eng.ccu_engagement_id
        key = EngagementKey(track_id, ccu_id)
        eng_order = self.engagements.get(key, None)
        if eng_order is not None:
            if (
                eng.engagement_characteristics.engagement_request
                == EC2EngagementRequest.UPDATE
            ):
                if not self.is_any_engagement_enabled_turret_aim():
                    eng_order.enable_turret_aim = True
                eng_order.type_icp = eng.engagement_characteristics.type_of_icp
                eng_order.missile.update_type_of_icp(
                    eng.engagement_characteristics.type_of_icp
                )
                eng_order.lock_policy = eng.engagement_characteristics.locking_policy
                if (
                    eng_order.lock_policy
                    != eng.engagement_characteristics.locking_policy
                ):
                    self.logger.error("Unable to set locking policy")
                eng_order.fire_control2 = eng.fire_control2
                if eng.fire_control2 == EEngagementFireControl2.ENABLE:
                    eng_order.magic_word = eng.magic_word
                    eng_order.magic_word_time = eng.ccu_time
                self.logger.info(f"Update engagement: {eng_order}")
                eng_order.do_abort(eng.abort_engagement)
            elif (
                eng.engagement_characteristics.engagement_request
                == EC2EngagementRequest.NEW
            ):
                # duplikovany pozadavek
                self.logger.info(f"Duplicated NEW engagement - IGNORE: {eng_order}")
            else:
                # delete
                eng_order.enable_turret_aim = False
                eng_order.do_abort(eng.abort_engagement)
                self.delete_engagement(
                    eng_order, eng.engagement_characteristics.engagement_request
                )
                # self.logger.error(f"Delete engagement: {eng_order}")
                # self.delete(key)
        else:
            if (
                eng.engagement_characteristics.engagement_request
                == EC2EngagementRequest.NEW
            ):
                new_order = EngagementOrder(
                    track_id,
                    ccu_id,
                    self.get_next_cop_eng_id(),
                    eng.engagement_characteristics.type_of_icp,
                    eng.engagement_characteristics.locking_policy,
                    target,
                    self.missile_speed,
                    mfu_type,
                    self.inventory,
                    False,
                )
                new_order.fire_control2 = eng.fire_control2
                if not self.is_any_engagement_enabled_turret_aim():
                    new_order.enable_turret_aim = True
                self.engagements[key] = new_order
            else:
                self.logger.error(
                    f"Engagement doesn't exists, need NEW command !!!: {eng}"
                )

    def add_autonomous_engagement(
        self, track: ASPTrack, missile_type: ETypeOfICP, mfu_type: ECOPType
    ) -> bool:
        """Add autonomous engagement"""
        track_id = track.track_id
        ccu_id = self.get_next_cop_autonomous_eng_id()
        target = COPPlane(track_id, track)
        key = EngagementKey(track_id, ccu_id)
        self.engagements[key] = EngagementOrder(
            track_id,
            ccu_id,
            ccu_id,
            missile_type,
            ELockingPolicy.LOBL,
            target,
            self.missile_speed,
            mfu_type,
            self.inventory,
            True,
        )
        return True

    def get_report(self) -> str:
        """Description"""
        ret = ""
        for key, eng in self.engagements.items():
            ret += f"{eng.cop_eng_id}) {key} - {eng}\n"
            ret += "-" * 40
            ret += "\n"
            ret += f"Press: {eng.cop_eng_id} to Misfire this Interception.....\n"
        return ret

    def toggle_misfire_indication(self, eng_id: int) -> None:
        """Toggle misfire indication"""
        for eng in self.engagements.values():
            if eng.cop_eng_id == eng_id:
                eng.missile.misfire_indication = not eng.missile.misfire_indication
                break

    def get_engagements_cnt(self) -> int:
        """get_engagements_cnt"""
        return len(self.engagements)
