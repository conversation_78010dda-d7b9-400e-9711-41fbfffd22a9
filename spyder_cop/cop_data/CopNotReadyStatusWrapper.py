#!/usr/bin/env python3
"""
Mcu Not Ready Status
"""
from enum import Enum

from spyder_simul.simulation import SimCOPNotReady

from spyder_cop.messages.class_COP_Not_Ready_Reason import COPNotReadyReason
from spyder_cop.messages.util import ENoYesBoolean

from .GuiStatus import GuiStatus


class StatusEnum(Enum):
    """Enum for CopNotReadyStatus"""

    COP_CLIENT_DISCONNECTED = ("cop_client_disconnected", 1)
    COP_SERVER_DISCONNECTED = ("cop_server_disconnected", 2)
    CCU_SERVER_DISCONNECTED = ("ccu_server_disconnected", 3)
    COP_CCU_DESYNC = ("cop_ccu_desync", 4)
    COP_MCU_DESYNC = ("cop_mcu_desync", 5)
    DLQ_SMALLER_THAN_THRESHOLD = ("dlq_smaller_than_threshold", 6)
    COP_CLIENT_FAULTY = ("cop_client_faulty", 7)
    COP_SERVER_FAULTY = ("cop_server_faulty", 8)
    CCU_SERVER_FAULTY = ("ccu_server_faulty", 9)
    DTM_COMP = ("dtm_comp", 10)
    SICS_FAULTY = ("sics_faulty", 11)
    TOPLITE_FAULTY = ("toplite_faulty", 12)

    def __init__(self, enum_name: str, index_value: int):
        self.enum_name = enum_name
        self.index_value = index_value

    def __str__(self) -> str:
        return self.enum_name

    @classmethod
    def get_index(cls, enum_name: str) -> int:
        """gets index from enum name"""
        for enum in cls:
            if enum.enum_name == enum_name:
                return enum.index_value
        raise ValueError(f"enum name {enum_name} not found in {cls.__name__}")

    @classmethod
    def get_enum_by_index(cls, index_value: int) -> "StatusEnum":
        """gets enum from index"""
        for enum in cls:
            if enum.index_value == index_value:
                return enum
        raise ValueError(f"index {index_value} not found in {cls.__name__}")


class CopNotReadyStatusWrapper(GuiStatus):
    """MCU Not Ready Status for GUI"""

    def __init__(self, cop_ready: COPNotReadyReason) -> None:
        self._cop_not_ready = cop_ready

        self._states: dict[StatusEnum, ENoYesBoolean] = {
            StatusEnum.COP_CLIENT_DISCONNECTED: cop_ready.cop_client_disconnected,
            StatusEnum.COP_SERVER_DISCONNECTED: cop_ready.cop_server_disconnected,
            StatusEnum.CCU_SERVER_DISCONNECTED: cop_ready.ccu_server_disconnected,
            StatusEnum.COP_CCU_DESYNC: cop_ready.cop_ccu_desync,
            StatusEnum.COP_MCU_DESYNC: cop_ready.cop_mcu_desync,
            StatusEnum.DLQ_SMALLER_THAN_THRESHOLD: cop_ready.dlq_smaller_than_threshold,
            StatusEnum.COP_CLIENT_FAULTY: cop_ready.cop_client_faulty,
            StatusEnum.COP_SERVER_FAULTY: cop_ready.cop_server_faulty,
            StatusEnum.CCU_SERVER_FAULTY: cop_ready.ccu_server_faulty,
            StatusEnum.DTM_COMP: cop_ready.dtm_comp,
            StatusEnum.SICS_FAULTY: cop_ready.sics_faulty,
            StatusEnum.TOPLITE_FAULTY: cop_ready.toplite_faulty,
        }

        self._options: dict[int, ENoYesBoolean] = {
            1: ENoYesBoolean.NO,
            2: ENoYesBoolean.YES,
        }

        self._state_changed: bool = False

    def get_state(self, status: StatusEnum) -> ENoYesBoolean:
        """Getter for a specific state."""
        return self._states.get(status, ENoYesBoolean.NO)

    def set_state(self, status: StatusEnum, value: ENoYesBoolean | bool) -> None:
        """Setter for a specific state."""
        if isinstance(value, bool):
            value = ENoYesBoolean.YES if value else ENoYesBoolean.NO
        if self._states.get(status) != value:
            self._states[status] = value
            setattr(self._cop_not_ready, status.enum_name, value)
            self._state_changed = True

    def get_gui_menu(self) -> str:
        """description"""
        status_strings = [
            f"[{status.index_value}] ... {status.enum_name.ljust(30)}: \t\t{state.name}"
            for status, state in self._states.items()
        ]
        return "\n".join(status_strings) + "\n" + "q ... Quit" + "\n" + "Choose: "

    def get_options(self, option: int) -> str:
        """description"""
        try:
            sel_option: StatusEnum = StatusEnum.get_enum_by_index(option)
            sel_item: ENoYesBoolean | None = self._states.get(sel_option)
            if sel_item is None:
                return "Unknown choice\nq ... Quit\nChoose: "
            return (
                sel_option.enum_name
                + ": "
                + sel_item.name
                + "\n"
                + "\n".join(
                    f"[{i}]: {state.name}" for i, state in self._options.items()
                )
                + "\nq ... Quit"
                + "\n"
                + "Choose: "
            )
        except ValueError:
            return "Unknown choice\nq ... Quit\nChoose: "

    def set_options(self, option: int, value: int) -> bool:
        """description"""
        if option < 0 or option > len(StatusEnum):
            raise IndexError("Option index out of range")
        if value not in self._options:
            raise IndexError("Value index out of range")
        self.set_state(
            StatusEnum.get_enum_by_index(option),
            self._options.get(value, ENoYesBoolean.NO),
        )
        return self._state_changed

    def convert_sim_cop_not_ready(
        self, sim_cop_not_ready: SimCOPNotReady | None
    ) -> None:
        if sim_cop_not_ready is None:
            return

        if sim_cop_not_ready.ccu_server_disconnected is not None:
            self.set_state(
                StatusEnum.CCU_SERVER_DISCONNECTED,
                sim_cop_not_ready.ccu_server_disconnected,
            )

        if sim_cop_not_ready.ccu_server_faulty is not None:
            self.set_state(
                StatusEnum.CCU_SERVER_FAULTY,
                sim_cop_not_ready.ccu_server_faulty,
            )
        if sim_cop_not_ready.cop_ccu_desync is not None:
            self.set_state(StatusEnum.COP_CCU_DESYNC, sim_cop_not_ready.cop_ccu_desync)
        if sim_cop_not_ready.cop_client_disconnected is not None:
            self.set_state(
                StatusEnum.COP_CLIENT_DISCONNECTED,
                sim_cop_not_ready.cop_client_disconnected,
            )
        if sim_cop_not_ready.cop_client_faulty is not None:
            self.set_state(
                StatusEnum.COP_CLIENT_FAULTY,
                sim_cop_not_ready.cop_client_faulty,
            )
        if sim_cop_not_ready.cop_mcu_desync is not None:
            self.set_state(StatusEnum.COP_MCU_DESYNC, sim_cop_not_ready.cop_mcu_desync)
        if sim_cop_not_ready.cop_server_disconnected is not None:
            self.set_state(
                StatusEnum.COP_SERVER_DISCONNECTED,
                sim_cop_not_ready.cop_server_disconnected,
            )
        if sim_cop_not_ready.cop_server_faulty is not None:
            self.set_state(
                StatusEnum.COP_SERVER_FAULTY,
                sim_cop_not_ready.cop_server_faulty,
            )
        if sim_cop_not_ready.dlq_smaller_than_threshold is not None:
            self.set_state(
                StatusEnum.DLQ_SMALLER_THAN_THRESHOLD,
                sim_cop_not_ready.dlq_smaller_than_threshold,
            )
        if sim_cop_not_ready.dtm_comp is not None:
            self.set_state(StatusEnum.DTM_COMP, sim_cop_not_ready.dtm_comp)
        if sim_cop_not_ready.sics_faulty is not None:
            self.set_state(StatusEnum.SICS_FAULTY, sim_cop_not_ready.sics_faulty)
        if sim_cop_not_ready.toplite_faulty is not None:
            self.set_state(StatusEnum.TOPLITE_FAULTY, sim_cop_not_ready.toplite_faulty)
