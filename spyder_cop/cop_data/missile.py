import math
from random import uniform
from time import time
from typing import Any

from spyder_bmc.geoutil import GeoUtil

from spyder_cop.cop_data.ASPTrack import (
    COPPlane,
    from_bool,
    from_float,
    from_int,
    from_str,
    to_class,
)
from spyder_cop.cop_data.inventory import Inventory
from spyder_cop.messages.util import ECOPType, ETypeOfICP, LLLNPosition


class MissileLocation:
    """Missile Location"""

    def __init__(
        self,
        miss_id: int,
        miss_lat: float,
        miss_lon: float,
        miss_alt: float,
        bearing: float,
        miss_type: str,
        target_id: int,
        detonation: bool,
        cop_id: int,
    ) -> None:
        """Constructor"""
        self.miss_id = miss_id
        self.miss_lat = miss_lat
        self.miss_lon = miss_lon
        self.miss_alt = miss_alt
        self.bearing = bearing
        self.miss_type = miss_type
        self.target_id = target_id
        self.detonation = detonation
        self.cop_id = cop_id

    def to_dict(self) -> dict[str, Any]:
        """Serialize to dict"""
        result: dict[str, Any] = {
            "miss_id": from_int(self.miss_id),
            "miss_lat": from_float(self.miss_lat),
            "miss_lon": from_float(self.miss_lon),
            "miss_alt": from_float(self.miss_alt),
            "bearing": from_float(self.bearing),
            "miss_type": from_str(self.miss_type),
            "target_id": from_int(self.target_id),
            "detonation": from_bool(self.detonation),
            "cop_id": from_int(self.cop_id),
        }
        return result

    @staticmethod
    def from_dict(obj: Any) -> "MissileLocation":
        """Read from dict"""
        miss_id = from_int(obj.get("miss_id"))
        miss_lat = from_float(obj.get("miss_lat"))
        miss_lon = from_float(obj.get("miss_lon"))
        miss_alt = from_float(obj.get("miss_alt"))
        bearing = from_float(obj.get("bearing"))
        miss_type = from_str(obj.get("miss_type"))
        target_id = from_int(obj.get("target_id"))
        detonation = from_bool(obj.get("detonation"))
        cop_id = from_int(obj.get("cop_id"))
        return MissileLocation(
            miss_id,
            miss_lat,
            miss_lon,
            miss_alt,
            bearing,
            miss_type,
            target_id,
            detonation,
            cop_id,
        )


def missile_location_from_dict(s: Any) -> MissileLocation:
    """Read from dict"""
    return MissileLocation.from_dict(s)


def missile_location_to_dict(x: MissileLocation) -> Any:
    """Store in dict"""
    return to_class(MissileLocation, x)


class Missile:
    """Missile"""

    # vzdalenost s ohledem na rychlost, kdy se predpoklada stret s letadlem
    MISSILE_MIN_DISTANCE_KOEF = 0.5

    LATITUDE_METERS_PER_DEGREE = 111319.9
    LONGITUDE_METERS_PER_DEGREE = 111319.9

    # for testing
    # MISSILE_FLIGHT_TIME = {"SR": 5, "MR": 10}
    MISSILE_FLIGHT_TIME = {ECOPType.SR: 135.0, ECOPType.MR: 180.0}

    DETONATION_SUCCESSFUL_RATE = 0.95

    def __init__(
        self,
        mis_id: int,
        missile_speed: float,
        target: COPPlane,
        mfu_type: ECOPType,
        type_of_icp: ETypeOfICP,
        inventory: Inventory,
    ) -> None:
        """Constructor"""
        self.missile_id = mis_id
        self.type_of_icp = type_of_icp
        self.target_id = target.track_id
        self.target = target
        self.in_the_air = False
        self.missile_pos = LLLNPosition(0, 0, 0)
        self.detonation = False
        self.missile_speed = missile_speed
        self.bearing = 0.0
        self.mfu_type = mfu_type
        self.start_time = 0.0
        self.misfire_indication = False
        self.aborted = False
        self.last_update = 0.0
        self.velocity = LLLNPosition(0.0, 0.0, 0.0)
        self.station_id: int = inventory.get_missile_id(type_of_icp)
        self.inventory = inventory

    def update_type_of_icp(self, type_of_icp: ETypeOfICP) -> None:
        """Update type of ICP"""
        self.type_of_icp = type_of_icp
        if self.station_id == 0 and type_of_icp in [
            ETypeOfICP.ER_DERBY,
            ETypeOfICP.LR_DERBY,
        ]:
            self.station_id = self.inventory.get_missile_id(type_of_icp)
        if type_of_icp not in [ETypeOfICP.ER_DERBY, ETypeOfICP.LR_DERBY]:
            self.station_id = 0

    def set_misfire(self, state: bool) -> None:
        """Set MisFire - Missile onGround error"""
        if not self.in_the_air:
            self.misfire_indication = state

    def start_missile(self) -> None:
        """Start missile"""
        self.start_time = time()
        self.last_update = self.start_time
        self.in_the_air = True

    def detonation_missile(self, success: bool) -> None:
        """Missile detonation"""
        self.detonation = True
        self.in_the_air = False
        if success:
            self.target.activated = False
        else:
            self.target.activated = True

    def abort_missile(self) -> None:
        """Abort missile"""
        self.detonation = True
        self.in_the_air = False
        self.aborted = True

    def is_aborted(self) -> bool:
        """Check if the missile aborted"""
        return self.aborted

    def max_missile_flight_check(self) -> bool:
        """Check if the missile flight enough"""
        max_time = Missile.MISSILE_FLIGHT_TIME.get(self.mfu_type, 100)
        now = time()
        print(f"Missile flight time: {(now - self.start_time)}")
        return (now - self.start_time) > max_time

    def _calculate_missile_detonation_distance(self) -> float:
        """Distance in [m] if it's less than is missile reach the target"""
        return self.missile_speed * Missile.MISSILE_MIN_DISTANCE_KOEF

    def update_missile_position(self) -> None:
        """update_missile_position"""
        if not self.in_the_air:
            return None

        if self.max_missile_flight_check():
            self.detonation_missile(False)
            return None

        # print(f"Plane: {self.target.airplane_pos}")
        # print(f"Missile: {self.missile_pos}")

        longitude_meters_per_degree = Missile.LONGITUDE_METERS_PER_DEGREE * math.cos(
            math.radians(self.missile_pos.x_pos)
        )
        self.bearing = GeoUtil.bearing_rad(
            self.missile_pos.x_pos,
            self.missile_pos.y_pos,
            self.target.airplane_pos.x_pos,
            self.target.airplane_pos.y_pos,
        )
        now = time()
        time_diff = now - self.last_update
        self.last_update = now
        # print(f"Bearing: {math.degrees(bearing)}")
        velocity_lat = (
            self.missile_speed
            * math.cos(self.bearing)
            / Missile.LATITUDE_METERS_PER_DEGREE
        ) * time_diff
        velocity_lon = (
            self.missile_speed * math.sin(self.bearing) / longitude_meters_per_degree
        ) * time_diff
        self.velocity.x_pos = velocity_lat
        self.velocity.y_pos = velocity_lon

        # print(f"velocity_lat: {velocity_lat}, {velocity_lon}")
        self.missile_pos = LLLNPosition(
            self.missile_pos.x_pos + velocity_lat,
            self.missile_pos.y_pos + velocity_lon,
            self.target.airplane_pos.z_pos,
        )
        # print(f"Distance: {self.calc_distance()}")
        distance = (
            GeoUtil.distance(
                self.missile_pos.x_pos,
                self.missile_pos.y_pos,
                self.target.airplane_pos.x_pos,
                self.target.airplane_pos.y_pos,
            )
            * 1000
        )
        if distance <= self._calculate_missile_detonation_distance():
            self.try_to_detonate()
            return None
        return None

    def try_to_detonate(self) -> None:
        """Detonation successful rate"""
        self.detonation_missile(
            uniform(0, 1) >= (1 - Missile.DETONATION_SUCCESSFUL_RATE)
        )
