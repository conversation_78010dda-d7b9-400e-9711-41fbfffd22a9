#!/usr/bin/env python3
"""
LM Readiness Status
"""
from enum import Enum

from spyder_simul.simulation import SimCOPState, SimOperability, SimState

from spyder_cop.messages.class_COP_C2_Status import LMReadiness
from spyder_cop.messages.util import (
    ECOPLMArmed,
    ECOPMCUState,
    ECOPReadinessToEngage,
    ENoYesBoolean,
)

from .GuiStatus import GuiStatus

_LmReadinessStates = ECOPMCUState | ECOPReadinessToEngage | ECOPLMArmed | ENoYesBoolean

_OPTION_NO_YES: dict[int, ENoYesBoolean] = {
    1: ENoYesBoolean.NO,
    2: ENoYesBoolean.YES,
}

_OPTION_LM_ARMED: dict[int, ECOPLMArmed] = {
    1: ECOPLMArmed.NOT_ARMED,
    2: ECOPLMArmed.ARMED,
}
_OPTION_MCU_STATE: dict[int, ECOPMCUState] = {
    1: ECOPMCUState.STANDBY,
    2: ECOPMCUState.OPERATIONAL,
    3: ECOPMCUState.MAINTENANCE,
    4: ECOPMCUState.TRAINING,
    5: ECOPMCUState.DISCONNECTED,
}

_OPTION_READY_TO_ENGAGE: dict[int, ECOPReadinessToEngage] = {
    1: ECOPReadinessToEngage.READY_TO_ENGAGE,
    2: ECOPReadinessToEngage.DEGRADED_BUT_READY_TO_ENGAGE,
    3: ECOPReadinessToEngage.NOT_READY_TO_ENGAGE,
}


class StatusEnum(Enum):
    """Enum for McuNotReadyStatus"""

    MCU_STATE = ("mcu_state", 1, _OPTION_MCU_STATE)
    LM_READINESS_TO_ENGAGE = (
        "lm_readiness_to_engage",
        2,
        _OPTION_READY_TO_ENGAGE,
    )
    LM_ARMED = ("lm_armed", 3, _OPTION_LM_ARMED)
    MCU_ABORT_BUTTON_PRESSED = (
        "mcu_abort_button_pressed",
        4,
        _OPTION_NO_YES,
    )
    ALLOW_OVERRIDE = ("Override readiness_to_engage by GUI", 5, _OPTION_NO_YES)

    def __init__(
        self,
        enum_name: str,
        index_value: int,
        dict_option: dict[int, _LmReadinessStates],
    ):
        self.enum_name = enum_name
        self.index_value = index_value
        self.dict_option = dict_option

    def __str__(self) -> str:
        return self.enum_name

    @classmethod
    def get_index(cls, enum_name: str) -> int:
        """gets index from enum name"""
        for enum in cls:
            if enum.enum_name == enum_name:
                return enum.index_value
        raise ValueError(f"enum name {enum_name} not found in {cls.__name__}")

    @classmethod
    def get_enum_by_index(cls, index_value: int) -> "StatusEnum":
        """gets enum from index"""
        for enum in cls:
            if enum.index_value == index_value:
                return enum
        raise ValueError(f"index {index_value} not found in {cls.__name__}")


class LmStatusWrapper(GuiStatus):
    """MCU Not Ready Status for GUI"""

    def __init__(self, lm_readiness: LMReadiness) -> None:
        self._lm_readiness = lm_readiness

        self._states: dict[
            StatusEnum,
            _LmReadinessStates,
        ] = {
            StatusEnum.MCU_STATE: lm_readiness.mcu_state,
            StatusEnum.LM_READINESS_TO_ENGAGE: lm_readiness.lm_readiness_to_engage,
            StatusEnum.LM_ARMED: lm_readiness.lm_armed,
            StatusEnum.MCU_ABORT_BUTTON_PRESSED: lm_readiness.mcu_abort_button_pressed,
            StatusEnum.ALLOW_OVERRIDE: ENoYesBoolean.NO,
        }

        self._state_changed: bool = False

    @property
    def allow_override(self) -> ENoYesBoolean:
        """getter"""
        return self._states[StatusEnum.ALLOW_OVERRIDE]  # type: ignore

    @allow_override.setter
    def allow_override(self, value: ENoYesBoolean) -> None:
        """setter"""
        self._states[StatusEnum.ALLOW_OVERRIDE] = value
        self._state_changed = True

    @property
    def mcu_state(self) -> ECOPMCUState:
        """getter"""
        return self._states[StatusEnum.MCU_STATE]  # type: ignore

    @mcu_state.setter
    def mcu_state(self, value: ECOPMCUState) -> None:
        """setter"""
        self._states[StatusEnum.MCU_STATE] = value
        self._lm_readiness.mcu_state = value
        self._state_changed = True

    @property
    def lm_readiness_to_engage(self) -> ECOPReadinessToEngage:
        """getter"""
        return self._states[StatusEnum.LM_READINESS_TO_ENGAGE]  # type: ignore

    @lm_readiness_to_engage.setter
    def lm_readiness_to_engage(self, value: ECOPReadinessToEngage) -> None:
        """setter"""
        self._states[StatusEnum.LM_READINESS_TO_ENGAGE] = value
        self._lm_readiness.lm_readiness_to_engage = value
        self._state_changed = True

    @property
    def lm_armed(self) -> ECOPLMArmed:
        """getter"""
        return self._states[StatusEnum.LM_ARMED]  # type: ignore

    @lm_armed.setter
    def lm_armed(self, value: ECOPLMArmed) -> None:
        """setter"""
        self._states[StatusEnum.LM_ARMED] = value
        self._lm_readiness.lm_armed = value
        self._state_changed = True

    @property
    def mcu_abort_button_pressed(self) -> ENoYesBoolean:
        """getter"""
        return self._states[StatusEnum.MCU_ABORT_BUTTON_PRESSED]  # type: ignore

    @mcu_abort_button_pressed.setter
    def mcu_abort_button_pressed(self, value: ENoYesBoolean) -> None:
        """setter"""
        self._states[StatusEnum.MCU_ABORT_BUTTON_PRESSED] = value
        self._lm_readiness.mcu_abort_button_pressed = value
        self._state_changed = True

    def get_gui_menu(self) -> str:
        """description"""
        status_strings = [
            f"[{status.index_value}] ... {status.enum_name.ljust(30)}: \t\t{state.name}"
            for status, state in self._states.items()
        ]
        return "\n".join(status_strings) + "\n" + "q ... Quit" + "\n" + "Choose: "

    def get_options(self, option: int) -> str:
        """description"""
        try:
            sel_option: StatusEnum = StatusEnum.get_enum_by_index(option)
            sel_item: _LmReadinessStates | None = self._states.get(sel_option)
            if sel_item is None:
                return "Unknown choice\nq ... Quit\nChoose: "
            return (
                sel_option.enum_name
                + ": "
                + sel_item.name
                + "\n"
                + "\n".join(
                    f"[{i}]: {state.name}"
                    for i, state in sel_option.dict_option.items()
                )
                + "\nq ... Quit"
                + "\n"
                + "Choose: "
            )
        except ValueError:
            return "Unknown choice\nq ... Quit\nChoose: "

    def set_options(self, option: int, value: int) -> bool:
        """description"""
        try:
            sel_option: StatusEnum = StatusEnum.get_enum_by_index(option)
            sel_dict_option = sel_option.dict_option.get(value, None)
            if sel_dict_option is None:
                return False
            match sel_option:
                case StatusEnum.MCU_STATE:
                    self.mcu_state = sel_dict_option  # type: ignore
                case StatusEnum.LM_READINESS_TO_ENGAGE:
                    self.lm_readiness_to_engage = sel_dict_option  # type: ignore
                case StatusEnum.LM_ARMED:
                    self.lm_armed = sel_dict_option  # type: ignore
                case StatusEnum.MCU_ABORT_BUTTON_PRESSED:
                    self.mcu_abort_button_pressed = sel_dict_option  # type: ignore
                case StatusEnum.ALLOW_OVERRIDE:
                    self.allow_override = sel_dict_option  # type: ignore
        except ValueError:
            return False

        return self._state_changed

    def convert_sim_readiness(self, readiness: SimCOPState) -> None:
        if readiness is None:
            return None
        if readiness.state is None:
            return None
        operability: bool = False
        if readiness.operability is not None:
            operability = readiness.operability == SimOperability.DEGRADED
        match readiness.state:
            case SimState.OPERATIONAL:
                if operability:
                    self.lm_readiness_to_engage = (
                        ECOPReadinessToEngage.DEGRADED_BUT_READY_TO_ENGAGE
                    )
                else:
                    self.lm_readiness_to_engage = ECOPReadinessToEngage.READY_TO_ENGAGE
            case SimState.NON_OPERATIONAL:
                self.lm_readiness_to_engage = ECOPReadinessToEngage.NOT_READY_TO_ENGAGE

    @property
    def is_enabled_allow_override(self) -> bool:
        return self.allow_override == ENoYesBoolean.YES

    @property
    def is_mcu_in_training(self) -> bool:
        return self.mcu_state == ECOPMCUState.TRAINING

    @property
    def is_mcu_in_operational(self) -> bool:
        return self.mcu_state == ECOPMCUState.OPERATIONAL

    @property
    def is_mcu_ready(self) -> bool:
        return self.mcu_state not in [
            ECOPMCUState.MAINTENANCE,
            ECOPMCUState.DISCONNECTED,
            ECOPMCUState.STANDBY,
        ]

    @property
    def is_cop_armed(self) -> bool:
        return self.lm_armed == ECOPLMArmed.ARMED

    @property
    def is_cop_ready_to_engage(self) -> bool:
        return self.lm_readiness_to_engage == ECOPReadinessToEngage.READY_TO_ENGAGE

    @property
    def is_cop_degraded(self) -> bool:
        return (
            self.lm_readiness_to_engage
            == ECOPReadinessToEngage.DEGRADED_BUT_READY_TO_ENGAGE
        )

    @property
    def is_cop_not_ready_to_engage(self) -> bool:
        return self.lm_readiness_to_engage == ECOPReadinessToEngage.NOT_READY_TO_ENGAGE
