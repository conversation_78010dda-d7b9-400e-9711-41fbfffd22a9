from spyder_cop.messages.util import ECOPType, ETypeOfICP


class Inventory:
    """Inventory"""

    MAX_MISSILES_MR: int = 8  # ES expecting up to 8 missiles no more !!!
    MAX_MISSILES_SR: int = 4  # ES expecting up to 8 missiles no more !!!

    def __init__(
        self, cop_type: ECOPType, derby: int, python: int, derby_er: int, derby_lr: int
    ) -> None:
        """_summary_

        Args:
            derby (int): _description_
            python (int): _description_
        """
        self.derby: int = derby
        self.derby_max: int = derby
        self.python: int = python
        self.python_max: int = python
        self.cop_type: ECOPType = cop_type
        self.derby_er: int = derby_er
        self.derby_er_max: int = derby_er
        self.derby_lr: int = derby_lr
        self.derby_lr_max: int = derby_lr
        self.set_max_missiles()
        self._last_missile_id: int = 0

    def get_missile_id(self, missile_type: ETypeOfICP) -> int:
        if missile_type in [ETypeOfICP.ER_DERBY, ETypeOfICP.LR_DERBY]:
            max_id = self.get_max_missiles_by_type()
            self._last_missile_id = (self._last_missile_id + 1) % max_id
            return self._last_missile_id
        return 0

    def has_missile_type(self, missile_type: ETypeOfICP) -> bool:
        """has missile type"""
        match missile_type:
            case ETypeOfICP.DERBY:
                return self.derby > 0
            case ETypeOfICP.OBJP:
                return self.python > 0
            case ETypeOfICP.ER_DERBY:
                return self.derby_er > 0
            case ETypeOfICP.LR_DERBY:
                return self.derby_lr > 0
        return False

    def fire_missile(self, missile_type: ETypeOfICP) -> bool:
        """fire missile"""
        match missile_type:
            case ETypeOfICP.DERBY:
                if self.derby > 0:
                    self.derby -= 1
                    return True
                else:
                    return False
            case ETypeOfICP.ER_DERBY:
                if self.derby_er > 0:
                    self.derby_er -= 1
                    return True
                else:
                    return False
            case ETypeOfICP.OBJP:
                if self.python > 0:
                    self.python -= 1
                    return True
                else:
                    return False
            case ETypeOfICP.LR_DERBY:
                if self.derby_lr > 0:
                    self.derby_lr -= 1
                    return True
                else:
                    return False
        return False

    def get_max_missiles_by_type(self) -> int:
        """get max missiles by type"""
        if self.cop_type == ECOPType.MR:
            return Inventory.MAX_MISSILES_MR
        return Inventory.MAX_MISSILES_SR

    def set_max_missiles(self) -> None:
        """Set Max missiles to 8"""
        self.set_available_type_missile()
        total_missiles: int = self.total_missiles()
        max_missiles: int = self.get_max_missiles_by_type()
        while total_missiles > max_missiles:
            self._try_to_ratio_missiles(total_missiles, max_missiles)
            total_missiles = self.total_missiles()
            if total_missiles > max_missiles:
                max_missiles -= 1

    def set_available_type_missile(self) -> None:
        if self.cop_type == ECOPType.MR:
            self.derby_er = 0
            self.derby_er_max = 0
        else:
            self.derby_lr = 0
            self.derby_lr_max = 0

    def _try_to_ratio_missiles(self, total_missiles: int, max_missiles: int) -> None:
        ratio = max_missiles / total_missiles
        self.derby = int(round(self.derby * ratio))
        self.derby_max = self.derby
        self.python = int(round(self.python * ratio))
        self.python_max = self.python
        self.derby_er = int(round(self.derby_er * ratio))
        self.derby_er_max = self.derby_er
        self.derby_lr = int(round(self.derby_lr * ratio))
        self.derby_lr_max = self.derby_lr

    def get_autonomous_missile(self) -> ETypeOfICP | None:
        """Select missile for autonomous engagement"""
        if self.python > 0:
            return ETypeOfICP.OBJP
        if self.derby > 0:
            return ETypeOfICP.DERBY
        if self.derby_er > 0:
            return ETypeOfICP.ER_DERBY
        if self.derby_lr > 0:
            return ETypeOfICP.LR_DERBY
        return None

    def get_inventory(self) -> tuple[int, int, int, int]:
        """get_inventory"""
        return self.derby, self.python, self.derby_er, self.derby_lr

    def total_missiles(self) -> int:
        """Total number of missiles"""
        return self.derby + self.python + self.derby_er + self.derby_lr

    def reload(self) -> None:
        """Reloading of missiles"""

        self.derby = self.derby_max
        self.python = self.python_max
        self.derby_er = self.derby_er_max
        self.derby_lr = self.derby_lr_max
        return None

    def deplete(self) -> None:
        """Depleting of missiles"""
        self.derby = 0
        self.python = 0
        self.derby_er = 0
        self.derby_lr = 0

        return None

    def increment(self, missile_type: ETypeOfICP) -> None:
        """Increment the number of missiles of a given type"""
        match missile_type:
            case ETypeOfICP.DERBY:
                self.derby += 1
            case ETypeOfICP.OBJP:
                self.python += 1
            case ETypeOfICP.ER_DERBY:
                self.derby_er += 1
            case ETypeOfICP.LR_DERBY:
                self.derby_lr += 1
        self.set_max_missiles()

    def __str__(self) -> str:
        """Description"""
        return (
            f"Inventory: Derby: {self.derby}, Python: {self.python},"
            f"Derby ER: {self.derby_er},Derby LR: {self.derby_lr}\n"
        )
