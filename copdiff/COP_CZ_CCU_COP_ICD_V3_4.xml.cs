using System.ComponentModel;
using System.Linq;
using System.IO;
using System.Text;
using System;

namespace VTUL.Message.Rafael.Cop {

  public class COP_CZ_CCU_COP_ICD_V3_4 {
      public static RidaMessage Parse(byte[] data)
      {
        if (data == null || data.Length == 0) return null;
        
        var opcode = data[0];
        using (var ms = new System.IO.MemoryStream(data))
        using (var reader = new System.IO.BinaryReader(ms))
        {
          switch(opcode)
          {
            case 1:
              return new C2_COP_ASP_Track().Read(reader);
            case 2:
              return new C2_COP_Engaged_Track().Read(reader);
            case 4:
              return new C2_COP_Status().Read(reader);
            case 5:
              return new C2_COP_ACK().Read(reader);
            case 6:
              return new C2_COP_GSP().Read(reader);
            case 11:
              return new C2_COP_Engagement().Read(reader);
            case 10:
              return new C2_COP_Chat().Read(reader);
            case 12:
              return new C2_COP_Pointer().Read(reader);
            case 7:
              return new C2_COP_Msls_Location_From_Rdr().Read(reader);
            case 113:
              return new C2_COP_Training_Failures().Read(reader);
            case 114:
              return new C2_COP_Training_Settings().Read(reader);
            case 8:
              return new COP_C2_Status().Read(reader);
            case 129:
              return new COP_C2_Engagement_Status().Read(reader);
            case 133:
              return new COP_C2_ACK().Read(reader);
            case 140:
              return new COP_C2_Pointer().Read(reader);
            case 134:
              return new COP_C2_Msl_DL().Read(reader);
            case 135:
              return new COP_C2_Msl_Metry().Read(reader);
            case 136:
              return new COP_C2_Toplite_Track().Read(reader);
          }
        }
        return null;
      }

    public const ushort Num_Of_ASP_Tracks_In_Array = 32;

    public const ushort Num_Of_Chars_In_TN = 5;

    public const ushort Num_Of_Connected_MFUs = 6;

    public const ushort Num_Of_Chars_In_Callsign = 15;

    public const ushort Num_Of_GSP_In_Array = 8;

    public const ushort Num_Of_Chars_In_Description = 20;

    public const ushort Num_Of_GSP_Vertices = 10;

    public const ushort Num_Of_Points_In_Trajectory = 60;

    public const ushort Num_Of_Chars_In_Chat = 160;

    public const ushort Num_Of_Chars_In_Pointer = 31;

    public const ushort MaxNumOfMslsInAir = 8;

    public const ushort Num_Of_No_Launch_Sector = 3;

    public const ushort Num_Of_Chars_In_Small_Text = 51;

    public const ushort Num_Of_Engagements_From_MCU = 8;

    public const ushort ARRAY_SIZE_1000 = 1000;

    public enum header_sim_flag {
      Operationa_Mode = 0,
      Simulation_Mode = 1,
    }

    public enum eSafetyBoolean {
      False = 17,
      True = 85,
    }

    public enum ASP_target_type {
      Fighter = 0,
      Helicopter = 1,
      UAV = 2,
      Cruise_Missile = 3,
      Spare = 4,
      Commercial_Jet = 5,
      POS = 6,
      Propelled_POS = 7,
      Large_UAV = 8,
      ICP = 100,
      Unknown = 255,
    }

    public enum ASP_Target_Identity {
      Pending = 0,
      Hostile = 1,
      Suspect = 2,
      Unknown = 3,
      Neutral = 4,
      Assumed_Friend = 5,
      Friend = 6,
      Faker = 7,
      Joker = 8,
      Exercise_Pending = 9,
      Exercise_Unknown = 10,
      Exercise_Assumed_Friend = 11,
      Exercise_Friend = 12,
      Exercise_Neutral = 13,
    }

    public enum ASP_Threat_Level {
      No_Threat = 0,
      One = 1,
      Two = 2,
      Three = 3,
      Four = 4,
      Five = 5,
      Six = 6,
      Seven = 7,
      Eight = 8,
      Nine = 9,
      Ten = 10,
    }

    public enum ASP_tracking_status {
      Not_tracked = 0,
      Lost = 1,
      Tracked = 2,
    }

    public enum IFF_Militry_Emergency {
      No_Emergency = 0,
      Emergency = 1,
    }

    public enum IFF_Special_Identification {
      No_Identification = 0,
      Special_Identification = 1,
    }

    public enum IFF_Foe_Friend {
      NO_NSM_INTERROGATION = 0,
      FRIENDLY_TARGET = 1,
      UNKNOWN_TARGET = 2,
      NO_NSM_REPLY = 3,
    }

    public enum IFF_Jammer {
      No_Jammer = 0,
      Jammer = 1,
    }

    public enum Sys_Radar_type {
      ELM2106 = 0,
      A_MMR = 1,
      B_Giraf = 2,
      C_Tirion3 = 3,
      D_Tirion4 = 4,
      E_Tirion5 = 5,
      Toplite = 6,
      ManualTargetNoRadarOper = 7,
      ELM2138 = 8,
    }

    public enum C2_Mode {
      Not_Relevent = 0,
      Operational = 1,
      Simulation = 2,
    }

    public enum C2_Substate {
      Unknown = 0,
      Setup = 1,
      Surveillance = 2,
      Immediate = 3,
    }

    public enum C2_Safety_State {
      Unknown = 0,
      Disarmed = 1,
      Armed = 2,
    }

    public enum C2_Fire_Readiness_Status {
      Unknown = 0,
      Ready = 1,
      Not_Ready = 2,
    }

    public enum Component_Status {
      Disconnected = 0,
      OK = 1,
      Degraded = 2,
      Faulty = 3,
    }

    public enum Radar_Type {
      MMR = 0,
      ATAR_FCR = 1,
      ATAR_SSR = 2,
      ELM2138 = 3,
    }

    public enum No_Yes_Boolean {
      No = 0,
      Yes = 1,
    }

    public enum IBIT_Action {
      Stop = 0,
      Start = 1,
    }

    public enum Init_Link_Type {
      Wired = 0,
      Wireless = 1,
    }

    public enum Locking_Policy {
      LOBL = 0,
      LOAL = 1,
    }

    public enum Init_Fire_Source {
      Local = 0,
      Remote = 1,
    }

    public enum GSP_Area_Type {
      SKOZ_No_Fly = 0,
      SKOZ_No_Interception = 1,
      Deffended_Asset = 2,
      GSP_Polygon = 3,
      GSP_Line = 4,
      WCO_Polygon = 5,
      WCO_Sector = 6,
      WCO_Ellipse = 7,
      BDZ = 8,
      Sector = 9,
      Point = 10,
      Ellipse = 11,
    }

    public enum GSP_Area_Status {
      Not_Active = 0,
      Active = 1,
    }

    public enum GSP_Area_Hostility {
      Friendly = 0,
      Hostile = 1,
      Neutral = 2,
    }

    public enum GSP_Object_Type {
      No_Statement = 0,
      Search = 1,
      Restricted = 2,
      Exercise = 3,
      Submarine_patrol = 4,
      Fighter_AOR = 5,
      Area_of_Responsibility = 6,
      Defended_Area = 7,
      Flight_Corridor = 8,
      Air_Defencse_Identification = 9,
      Danger = 10,
      Contaminated = 11,
      Allied_Engagement = 12,
      Hostile_Msl_Engagement = 13,
      Hostile_Weapon = 14,
      Hostile_Tactical = 15,
      SHORAD = 16,
      Kill_Zone = 17,
      Target_Area_Of_Interest = 18,
      Interest_Area = 19,
      Crossover_Point = 20,
      Air_Defense_Engagement = 21,
      Line_No_Statement = 22,
      Battle_Area_Front = 23,
      Gun_Target = 24,
      Corridor = 25,
      Hostile_Boundary = 26,
      Buffer_Zone_Boundary = 27,
      Low_Level_Transit_Route = 28,
      Tactical_Action_Line = 29,
      FSCL = 30,
      FLOT = 31,
      Approach_Line = 32,
      EW_Site = 33,
      Radar_Site = 34,
      C2_Site = 35,
      Launcher_Site = 36,
      Emergency_Point = 37,
      Aux_Point = 38,
      Rendezvous_Point = 39,
      Marshel_Point = 40,
      Tanker_Station = 41,
      Combat_Air_Patrol_Station = 42,
      Beacon_Site = 43,
      General_Point_Representaion = 44,
    }

    public enum Engagement_Priority {
      High = 0,
      Low = 1,
    }

    public enum Type_Of_ICP {
      objP = 0,
      Derby = 1,
      ER_Derby = 2,
      LR_Derby = 3,
    }

    public enum CCU_Engagement_Request {
      New = 0,
      Update = 1,
      Delete_Ground_Engagement = 2,
      Delete_Air_Engagement = 3,
    }

    public enum Plan_Status {
      Plan_Exists = 0,
      No_Valid_Plan = 1,
      Not_In_Overall_Solution = 2,
      Not_Calculated_Yet = 3,
    }

    public enum Engagement_Status {
      Plan_Only = 0,
      Prelaunch = 1,
      Sent_To_MFU = 2,
      On_Air = 3,
      After_Flyby = 4,
      After_Abort = 5,
      Closed = 6,
    }

    public enum Engagement_Fire_Control_1 {
      Normal = 0,
      Salvo = 1,
    }

    public enum Engagement_Fire_Control_2 {
      Hold = 0,
      Ready = 1,
      Enable = 2,
    }

    public enum COP_Operability {
      OK = 0,
      Degraded = 1,
      Faulty = 2,
    }

    public enum COP_Mode {
      Maintenance = 0,
      Training_Setup = 1,
      Training_Surveilance = 2,
      Training_Immediate = 3,
      Training_Conflict = 4,
      Operational_Setup = 5,
      Operational_Surveilance = 6,
      Operational_Immediate = 7,
      Operational_Conflict = 8,
      Navigation = 9,
      Autonomous = 10,
    }

    public enum COP_Type {
      SR = 0,
      MR = 1,
    }

    public enum COP_MCU_State {
      StandBy = 0,
      Operational = 1,
      Maintenance = 2,
      Training = 3,
      Disconnected = 4,
    }

    public enum COP_Readiness_To_Engage {
      Ready_To_Engage = 0,
      Degraded_But_Ready_To_Engage = 1,
      Not_Ready_To_Engage = 2,
    }

    public enum COP_LM_Armed {
      Not_Armed = 0,
      Armed = 1,
    }

    public enum COP_Component_Status {
      OK = 0,
      Fail = 1,
    }

    public enum DLQ_Data_Source {
      CBIT = 0,
      IBIT_Request = 1,
    }

    public enum COPTopliteEngagementIndication {
      NoEngagement = 0,
      GroundEngagement = 1,
      AirEngagement = 2,
    }

    public enum COP_Engagement_Handling {
      Will_Pro = 0,
      Cant_Pro = 1,
      Will_Comply = 2,
      Cant_Comply = 3,
      Engagement_Cannot_Be_Created = 4,
    }

    public enum COP_MCU_Engagement_Status {
      Closed = 0,
      Opened = 1,
      Abort_Pending = 2,
    }

    public enum COP_Engagement_Abort_Source {
      No_Abort = 0,
      CM = 1,
      COP = 2,
      CP = 3,
    }

    public enum ICP_Lock_Status {
      Not_Locked = 0,
      Locked = 1,
    }

    public enum ICP_Stage {
      On_Ground = 0,
      On_Air = 1,
    }

    public enum COP_Misfire_Reason {
      Empty = 0,
      Launch_Failed = 1,
      Misfire = 2,
      No_Fire = 3,
    }

    public enum COP_Seeker_mode {
      Free = 0,
      Follows_Designation = 1,
    }

    public enum TopliteTargetType {
      Plane = 0,
      Helicopter = 1,
      UAV = 2,
      Cruise_Missile = 3,
      NA = 4,
      Commercial_Jet = 5,
      PSO = 6,
      Propelled_PSO = 7,
      Large_UAV = 8,
      ICP = 100,
      Invalid_Type = 255,
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class CCU_COP_Header {

      [DisplayName("(01) - msg_type")]
      [Description("Range: 0 ... 255\u000D\u000AMessage OpCode")]
      public byte msg_type { get; set; }

      [DisplayName("(02) - msg_length")]
      [Description("Range: 0 ... 65535\u000D\u000AMessage length in bytes, including the header")]
      public ushort msg_length { get; set; }

      [DisplayName("(03) - msg_number")]
      [Description("Range: 0 ... 65535\u000D\u000AMessage sequence number (cyclic)")]
      public ushort msg_number { get; set; }

      [DisplayName("(04) - sender")]
      [Description("Range: 0 ... 65535\u000D\u000A0x1 – 0xFF for BMC\u000D\u000A0x100  - 0xFFFF for COP")]
      public ushort sender { get; set; }

      [DisplayName("(05) - sim_flag")]
      [Description("Range: 0 ... 255\u000D\u000A1 – Operational Mode\u000D\u000A2- Simulation Mode ")]
      public header_sim_flag sim_flag { get; set; }

      [DisplayName("(06) - msg_sent_time")]
      [Description("Range: 946684800 ... 4102444800\u000D\u000AMessage send time from 1970 (UTC) in seconds and milliseconds after decimal point")]
      public double msg_sent_time { get; set; }

      [DisplayName("(07) - msg_checksum")]
      [Description("Range: 0 ... 65535\u000D\u000ACRC16 checksum, calculated over all data, excluding header")]
      public ushort msg_checksum { get; set; }

      public override string ToString()
      {
        return "CCU_COP_Header";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class Threat_Level_And_Tracking_Status {

      [DisplayName("(01) - Threat_Level")]
      [Description("Range: 0 ... 15\u000D\u000A")]
      public ASP_Threat_Level Threat_Level { get; set; }

      [DisplayName("(02) - Tracking_Status")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public ASP_tracking_status Tracking_Status { get; set; }

      [DisplayName("(03) - Spare")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public byte Spare { get; set; }

      public override string ToString()
      {
        return "Threat_Level_And_Tracking_Status";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class IFF_special_data {

      [DisplayName("(01) - Militry_emergency")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public IFF_Militry_Emergency Militry_emergency { get; set; }

      [DisplayName("(02) - Special_Identification")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public IFF_Special_Identification Special_Identification { get; set; }

      [DisplayName("(03) - Foe_Friend")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public IFF_Foe_Friend Foe_Friend { get; set; }

      [DisplayName("(04) - Jammer")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public IFF_Jammer Jammer { get; set; }

      [DisplayName("(05) - Spare1")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare1 { get; set; }

      [DisplayName("(06) - Spare2")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare2 { get; set; }

      [DisplayName("(07) - Spare3")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare3 { get; set; }

      public override string ToString()
      {
        return "IFF_special_data";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class ECEF_Position {

      [DisplayName("(01) - X_Pos")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000AX Axis Current Position (m)")]
      public int X_Pos { get; set; }

      [DisplayName("(02) - Y_Pos")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000AY Axis Current Position (m)")]
      public int Y_Pos { get; set; }

      [DisplayName("(03) - Z_Pos")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000AZ Axis Current Position (m)")]
      public int Z_Pos { get; set; }

      public override string ToString()
      {
        return "ECEF_Position";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class ECEF_Velocity {

      [DisplayName("(01) - Vx")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000AScaleFactor: 0.01\u000D\u000AX Component of Velocity LSB=0.01 m/s")]
      public double Vx {
        get =>  _Vx * 0.01;
        set => _Vx = (int)Math.Round(value / 0.01);
      }
      internal int _Vx;

      [DisplayName("(02) - Vy")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000AScaleFactor: 0.01\u000D\u000AY Component of Velocity LSB=0.01 m/s")]
      public double Vy {
        get =>  _Vy * 0.01;
        set => _Vy = (int)Math.Round(value / 0.01);
      }
      internal int _Vy;

      [DisplayName("(03) - Vz")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000AScaleFactor: 0.01\u000D\u000AZ Component of Velocity LSB=0.01 m/s")]
      public double Vz {
        get =>  _Vz * 0.01;
        set => _Vz = (int)Math.Round(value / 0.01);
      }
      internal int _Vz;

      public override string ToString()
      {
        return "ECEF_Velocity";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class ASP_Track {

      [DisplayName("(01) - Track_ID")]
      [Description("Range: 0 ... 65535\u000D\u000AUnique Track ID ")]
      public ushort Track_ID { get; set; }

      [DisplayName("(02) - FCR_track_ID")]
      [Description("Range: 0 ... 12000\u000D\u000A")]
      public ushort FCR_track_ID { get; set; }

      [DisplayName("(03) - TN_str")]
      [Description("Range: -128 ... 127\u000D\u000A")]
      public byte[] TN_str { get; } = new byte[Num_Of_Chars_In_TN];

      [DisplayName("(04) - track_update_time")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000ACurrent state vector time, Unit = 1 sec TOD")]
      public float track_update_time { get; set; }

      [DisplayName("(05) - target_type")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public ASP_target_type target_type { get; set; }

      [DisplayName("(06) - target_identity")]
      [Description("Range: 0 ... 2\u000D\u000AThe identity of the target")]
      public ASP_Target_Identity target_identity { get; set; }

      [DisplayName("(07) - Threat_Level_And_Tracking_Status")]
      public Threat_Level_And_Tracking_Status Threat_Level_And_Tracking_Status { get; } = new Threat_Level_And_Tracking_Status();

      [DisplayName("(08) - special_data")]
      public IFF_special_data special_data { get; } = new IFF_special_data();

      [DisplayName("(09) - SIF_1_Code")]
      [Description("Range: 0 ... 255\u000D\u000A2 Octal digits, MAX_Byte– Invalid Code")]
      public byte SIF_1_Code { get; set; }

      [DisplayName("(10) - SIF_2_Code")]
      [Description("Range: 0 ... 65535\u000D\u000A4 Octal digits, MAX_SHORT – Invalid Code")]
      public ushort SIF_2_Code { get; set; }

      [DisplayName("(11) - SIF_3_Code")]
      [Description("Range: 0 ... 65535\u000D\u000A4 Octal digits, MAX_SHORT – Invalid Code")]
      public ushort SIF_3_Code { get; set; }

      [DisplayName("(12) - Position")]
      public ECEF_Position Position { get; } = new ECEF_Position();

      [DisplayName("(13) - Velocity")]
      public ECEF_Velocity Velocity { get; } = new ECEF_Velocity();

      [DisplayName("(14) - Spare")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte Spare { get; set; }

      public override string ToString()
      {
        return "ASP_Track";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class LLLN_Position {

      [DisplayName("(01) - X")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float X { get; set; }

      [DisplayName("(02) - Y")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Y { get; set; }

      [DisplayName("(03) - Z")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Z { get; set; }

      public override string ToString()
      {
        return "LLLN_Position";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class ECEF_uncertainty {

      [DisplayName("(01) - Var_XX")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Var_XX { get; set; }

      [DisplayName("(02) - Cov_XY")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Cov_XY { get; set; }

      [DisplayName("(03) - Cov_XZ")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Cov_XZ { get; set; }

      [DisplayName("(04) - Var_YY")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Var_YY { get; set; }

      [DisplayName("(05) - Cov_YZ")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Cov_YZ { get; set; }

      [DisplayName("(06) - Var_ZZ")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Var_ZZ { get; set; }

      public override string ToString()
      {
        return "ECEF_uncertainty";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class Rdr_Position {

      [DisplayName("(01) - Longitude")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Longitude { get; set; }

      [DisplayName("(02) - Latitude")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Latitude { get; set; }

      [DisplayName("(03) - Altitude")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Altitude { get; set; }

      public override string ToString()
      {
        return "Rdr_Position";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class Engaged_Track {

      [DisplayName("(01) - Track_ID")]
      [Description("Range: 0 ... 65535\u000D\u000AUnique Track ID ")]
      public ushort Track_ID { get; set; }

      [DisplayName("(02) - target_type")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public ASP_target_type target_type { get; set; }

      [DisplayName("(03) - track_update_time")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000ACurrent state vector time, Unit = 1 sec TOD\u000D\u000A")]
      public float track_update_time { get; set; }

      [DisplayName("(04) - Position")]
      [Description("Position as received from the sensor without any conversion")]
      public LLLN_Position Position { get; } = new LLLN_Position();

      [DisplayName("(05) - Velocity")]
      public ECEF_Velocity Velocity { get; } = new ECEF_Velocity();

      [DisplayName("(06) - Position_Uncertainty")]
      public ECEF_uncertainty Position_Uncertainty { get; } = new ECEF_uncertainty();

      [DisplayName("(07) - Velocity_Uncertainty")]
      public ECEF_uncertainty Velocity_Uncertainty { get; } = new ECEF_uncertainty();

      [DisplayName("(08) - FCR_Curr_Position")]
      [Description("Position as received from the sensor without any conversion")]
      public Rdr_Position FCR_Curr_Position { get; } = new Rdr_Position();

      [DisplayName("(09) - Sensor_To_Target_Azimuth")]
      [Description("Range: 0 ... 6.28318530717959\u000D\u000A")]
      public float Sensor_To_Target_Azimuth { get; set; }

      [DisplayName("(10) - Sensor_To_Target_Elevation")]
      [Description("Range: -1.5707963267949 ... 1.5707963267949\u000D\u000A")]
      public float Sensor_To_Target_Elevation { get; set; }

      [DisplayName("(11) - Target_Source_Radar_type")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public Sys_Radar_type Target_Source_Radar_type { get; set; }

      [DisplayName("(12) - Spare")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort Spare { get; set; }

      public override string ToString()
      {
        return "Engaged_Track";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class Radar_Info {

      [DisplayName("(01) - Radar_Position")]
      public ECEF_Position Radar_Position { get; } = new ECEF_Position();

      [DisplayName("(02) - Radar_Type")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public Radar_Type Radar_Type { get; set; }

      [DisplayName("(03) - Is_Location_Valid")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean Is_Location_Valid { get; set; }

      [DisplayName("(04) - Spare_7")]
      [Description("Range: 0 ... 127\u000D\u000A")]
      public byte Spare_7 { get; set; }

      public override string ToString()
      {
        return "Radar_Info";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class Relevant_COP {

      [DisplayName("(01) - COP_Logical_ID1")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte COP_Logical_ID1 { get; set; }

      [DisplayName("(02) - COP_Logical_ID2")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte COP_Logical_ID2 { get; set; }

      [DisplayName("(03) - COP_Logical_ID3")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte COP_Logical_ID3 { get; set; }

      [DisplayName("(04) - COP_Logical_ID4")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte COP_Logical_ID4 { get; set; }

      [DisplayName("(05) - COP_Logical_ID5")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte COP_Logical_ID5 { get; set; }

      [DisplayName("(06) - COP_Logical_ID6")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte COP_Logical_ID6 { get; set; }

      [DisplayName("(07) - bSpare1")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte bSpare1 { get; set; }

      [DisplayName("(08) - bSpare2")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte bSpare2 { get; set; }

      public override string ToString()
      {
        return "Relevant_COP";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class DLQ_IBIT_Request {

      [DisplayName("(01) - Relevant_COP")]
      public Relevant_COP Relevant_COP { get; } = new Relevant_COP();

      [DisplayName("(02) - IBIT_Action")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public IBIT_Action IBIT_Action { get; set; }

      public override string ToString()
      {
        return "DLQ_IBIT_Request";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_Initialization_Request {

      [DisplayName("(01) - Link_Type")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public Init_Link_Type Link_Type { get; set; }

      [DisplayName("(02) - Locking_Policy")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public Locking_Policy Locking_Policy { get; set; }

      [DisplayName("(03) - Fire_Source")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public Init_Fire_Source Fire_Source { get; set; }

      [DisplayName("(04) - Spare1")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare1 { get; set; }

      [DisplayName("(05) - Spare2")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare2 { get; set; }

      [DisplayName("(06) - Spare3")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare3 { get; set; }

      [DisplayName("(07) - Spare4")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare4 { get; set; }

      [DisplayName("(08) - Spare5")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare5 { get; set; }

      public override string ToString()
      {
        return "COP_Initialization_Request";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_Init_Data {

      [DisplayName("(01) - COP_Logical_ID")]
      [Description("Range: 1 ... 6\u000D\u000A")]
      public byte COP_Logical_ID { get; set; }

      [DisplayName("(02) - MCU_Serial_ID")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte MCU_Serial_ID { get; set; }

      [DisplayName("(03) - Required_State")]
      public COP_Initialization_Request Required_State { get; } = new COP_Initialization_Request();

      [DisplayName("(04) - Site_Text_Length")]
      [Description("Range: 1 ... 10\u000D\u000A")]
      public byte Site_Text_Length { get; set; }

      [DisplayName("(05) - Site_Name")]
      [Description("Range: -128 ... 127\u000D\u000A")]
      public byte[] Site_Name { get; } = new byte[Num_Of_Chars_In_Callsign];

      [DisplayName("(05-A) - Site_Name")]
      public string Site_Name_Text
      {
        get
        {
          return UTF8Encoding.UTF8
            .GetString(Site_Name, 0, Site_Text_Length);
        }
        set
        {
          var data = UTF8Encoding.UTF8.GetBytes(value);
          var length = Math.Min(data.Length, Site_Name.Length);
          
          Array.Clear(Site_Name, 0, Site_Name.Length);
          Array.Copy(data, 0, Site_Name, 0, length);
          Site_Text_Length = (byte)length;
        }
      }

      public override string ToString()
      {
        return "COP_Init_Data";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_Initialization_Data_Array {

      [DisplayName("(01) - Init_Data")]
      public COP_Init_Data Init_Data { get; } = new COP_Init_Data();

      public override string ToString()
      {
        return "COP_Initialization_Data_Array";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class Area_Status_And_Hositility {

      [DisplayName("(01) - Area_Status")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public GSP_Area_Status Area_Status { get; set; }

      [DisplayName("(02) - Area_Hostility")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public GSP_Area_Hostility Area_Hostility { get; set; }

      [DisplayName("(03) - SeverityOrWCOType")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public byte SeverityOrWCOType { get; set; }

      [DisplayName("(04) - Spare")]
      [Description("Range: 0 ... 7\u000D\u000A")]
      public byte Spare { get; set; }

      public override string ToString()
      {
        return "Area_Status_And_Hositility";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class GSP_Element_General_Data {

      [DisplayName("(01) - Area_ID")]
      [Description("Range: 1 ... 65535\u000D\u000A")]
      public ushort Area_ID { get; set; }

      [DisplayName("(02) - Area_Status_And_Hositility")]
      public Area_Status_And_Hositility Area_Status_And_Hositility { get; } = new Area_Status_And_Hositility();

      [DisplayName("(03) - Name_Length")]
      [Description("Range: 0 ... 16\u000D\u000ANumber of Chars in description")]
      public byte Name_Length { get; set; }

      [DisplayName("(04) - Name")]
      [Description("Range: -128 ... 127\u000D\u000A")]
      public byte[] Name { get; } = new byte[Num_Of_Chars_In_Description];

      [DisplayName("(04-A) - Name")]
      public string Name_Text
      {
        get
        {
          return UTF8Encoding.UTF8
            .GetString(Name, 0, Name_Length);
        }
        set
        {
          var data = UTF8Encoding.UTF8.GetBytes(value);
          var length = Math.Min(data.Length, Name.Length);
          
          Array.Clear(Name, 0, Name.Length);
          Array.Copy(data, 0, Name, 0, length);
          Name_Length = (byte)length;
        }
      }

      [DisplayName("(05) - Object_Type")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public GSP_Object_Type Object_Type { get; set; }

      [DisplayName("(06) - Vertices_Num")]
      [Description("Range: 0 ... 10\u000D\u000A")]
      public byte Vertices_Num { get; set; }

      [DisplayName("(07) - Vertices_Element")]
      public ECEF_Position[] Vertices_Element { get; } = Enumerable.Range(0, COP_CZ_CCU_COP_ICD_V3_4.Num_Of_GSP_Vertices).Select(i => new ECEF_Position()).ToArray();

      public override string ToString()
      {
        return "GSP_Element_General_Data";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class GSP_Area_Element {

      [DisplayName("(01) - SKOZ_Min_Height")]
      [Description("Range: 0 ... 20000\u000D\u000ARelevant only for Area_Type 0 or 1 (SKOZs) else 0 is sent")]
      public ushort SKOZ_Min_Height { get; set; }

      [DisplayName("(02) - SKOZ_Max_Height")]
      [Description("Range: 0 ... 20000\u000D\u000ARelevant only for Area_Type 0 or 1 (SKOZs) else 0 is sent")]
      public ushort SKOZ_Max_Height { get; set; }

      public override string ToString()
      {
        return "GSP_Area_Element";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class GSP_Sector_Element {

      [DisplayName("(01) - Start_Azimuth")]
      [Description("Range: 0 ... 359\u000D\u000ALSB = 1o")]
      public ushort Start_Azimuth { get; set; }

      [DisplayName("(02) - End_Azimuth")]
      [Description("Range: 0 ... 359\u000D\u000A")]
      public ushort End_Azimuth { get; set; }

      [DisplayName("(03) - Radius")]
      [Description("Range: 0 ... 65535\u000D\u000ALSB = 1 m")]
      public ushort Radius { get; set; }

      public override string ToString()
      {
        return "GSP_Sector_Element";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class GSP_Ellipse_Element {

      [DisplayName("(01) - Azimuth")]
      [Description("Range: 0 ... 359\u000D\u000A")]
      public ushort Azimuth { get; set; }

      [DisplayName("(02) - Major_Radius")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort Major_Radius { get; set; }

      [DisplayName("(03) - Minor_Radius")]
      [Description("Range: 0 ... 65535\u000D\u000ALSB = 1 m")]
      public ushort Minor_Radius { get; set; }

      public override string ToString()
      {
        return "GSP_Ellipse_Element";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class GSP_Element {

      [DisplayName("(01) - Area_Type")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public GSP_Area_Type Area_Type { get; set; }

      [DisplayName("(02) - GSP_Element_General_Data")]
      public GSP_Element_General_Data GSP_Element_General_Data { get; } = new GSP_Element_General_Data();

      [DisplayName("(03) - Area_Element")]
      public GSP_Area_Element Area_Element { get; } = new GSP_Area_Element();

      [DisplayName("(04) - Sector_Element")]
      public GSP_Sector_Element Sector_Element { get; } = new GSP_Sector_Element();

      [DisplayName("(05) - Ellipse_Element")]
      public GSP_Ellipse_Element Ellipse_Element { get; } = new GSP_Ellipse_Element();

      public override string ToString()
      {
        return "GSP_Element";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class Engagement_Characteristics {

      [DisplayName("(01) - Engagement_Priority")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public Engagement_Priority Engagement_Priority { get; set; }

      [DisplayName("(02) - Type_Of_ICP")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public Type_Of_ICP Type_Of_ICP { get; set; }

      [DisplayName("(03) - Locking_Policy")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public Locking_Policy Locking_Policy { get; set; }

      [DisplayName("(04) - Engagement_Request")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public CCU_Engagement_Request Engagement_Request { get; set; }

      [DisplayName("(05) - Spare1")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare1 { get; set; }

      [DisplayName("(06) - Spare2")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare2 { get; set; }

      public override string ToString()
      {
        return "Engagement_Characteristics";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class CCU_Engagement_Status_From_ICS {

      [DisplayName("(01) - Plan_Status")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public Plan_Status Plan_Status { get; set; }

      [DisplayName("(02) - Engagement_Status")]
      [Description("Range: 0 ... 7\u000D\u000A")]
      public Engagement_Status Engagement_Status { get; set; }

      [DisplayName("(03) - Spare1")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare1 { get; set; }

      [DisplayName("(04) - Spare2")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare2 { get; set; }

      [DisplayName("(05) - Spare3")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare3 { get; set; }

      public override string ToString()
      {
        return "CCU_Engagement_Status_From_ICS";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class C2_Fire_Control1 {

      [DisplayName("(01) - Fire_Mode")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public Engagement_Fire_Control_1 Fire_Mode { get; set; }

      [DisplayName("(02) - wSpare")]
      [Description("Range: 0 ... 127\u000D\u000A")]
      public byte wSpare { get; set; }

      public override string ToString()
      {
        return "C2_Fire_Control1";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class ECEF_Position_And_Time {

      [DisplayName("(01) - X_Pos")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000AX Axis Current Position (m)")]
      public int X_Pos { get; set; }

      [DisplayName("(02) - Y_Pos")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000AY Axis Current Position (m)")]
      public int Y_Pos { get; set; }

      [DisplayName("(03) - Z_Pos")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000AZ Axis Current Position (m)")]
      public int Z_Pos { get; set; }

      [DisplayName("(04) - Rel_Time")]
      [Description("Range: 0 ... 4294967295\u000D\u000ARelative time to MLL as received from ICS")]
      public uint Rel_Time { get; set; }

      public override string ToString()
      {
        return "ECEF_Position_And_Time";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class ECEF_PIP {

      [DisplayName("(01) - PIP_Pos_X")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000ARelative X coordinate [m]")]
      public int PIP_Pos_X { get; set; }

      [DisplayName("(02) - PIP_Pos_Y")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000A")]
      public int PIP_Pos_Y { get; set; }

      [DisplayName("(03) - PIP_Pos_Z")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000ARelative Z coordinate [m]")]
      public int PIP_Pos_Z { get; set; }

      public override string ToString()
      {
        return "ECEF_PIP";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class Recipients {

      [DisplayName("(01) - MFU1")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte MFU1 { get; set; }

      [DisplayName("(02) - MFU2")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte MFU2 { get; set; }

      [DisplayName("(03) - MFU3")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte MFU3 { get; set; }

      [DisplayName("(04) - MFU4")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte MFU4 { get; set; }

      [DisplayName("(05) - MFU5")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte MFU5 { get; set; }

      [DisplayName("(06) - MFU6")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte MFU6 { get; set; }

      [DisplayName("(07) - Spare")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public byte Spare { get; set; }

      public override string ToString()
      {
        return "Recipients";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class SMslInAirType {

      [DisplayName("(01) - q_MslID")]
      [Description("Range: 0 ... 255\u000D\u000AAs defined by station number")]
      public byte q_MslID { get; set; }

      [DisplayName("(02) - q_Engagement1ID")]
      [Description("Range: 0 ... 65535\u000D\u000AUnique Identifier Engagement ID = 1 – 65500\u000D\u000ATOPLITE engagement ID = 65500 - 65535")]
      public ushort q_Engagement1ID { get; set; }

      [DisplayName("(03) - q_RDR_Index")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte q_RDR_Index { get; set; }

      [DisplayName("(04) - q_RdrTimeFromMidnight")]
      [Description("Range: 0 ... 4294967295\u000D\u000AScaleFactor: 25\u000D\u000ANote for scale: 25 = 0.025 * 1.e3 as scaled in missile icd")]
      public double q_RdrTimeFromMidnight {
        get =>  _q_RdrTimeFromMidnight * 25;
        set => _q_RdrTimeFromMidnight = (uint)Math.Round(value / 25);
      }
      internal uint _q_RdrTimeFromMidnight;

      [DisplayName("(05) - q_RdrTimeYear")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort q_RdrTimeYear { get; set; }

      [DisplayName("(06) - q_RdrTimeDayInAYear")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort q_RdrTimeDayInAYear { get; set; }

      [DisplayName("(07) - q_RdrAzimuthToMsl1")]
      [Description("Range: -32768 ... 32767\u000D\u000A")]
      public short q_RdrAzimuthToMsl1 { get; set; }

      [DisplayName("(08) - q_RdrElevationToMsl1")]
      [Description("Range: -32768 ... 32767\u000D\u000A")]
      public short q_RdrElevationToMsl1 { get; set; }

      [DisplayName("(09) - q_RdrRangeToMsl1")]
      [Description("Range: -32768 ... 32767\u000D\u000A")]
      public short q_RdrRangeToMsl1 { get; set; }

      [DisplayName("(10) - q_RdrMsl1Status")]
      [Description("Range: -32768 ... 32767\u000D\u000A")]
      public short q_RdrMsl1Status { get; set; }

      [DisplayName("(11) - q_RdrCovX")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte q_RdrCovX { get; set; }

      [DisplayName("(12) - q_RdrCovY")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte q_RdrCovY { get; set; }

      [DisplayName("(13) - q_RdrCovZ")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte q_RdrCovZ { get; set; }

      [DisplayName("(14) - q_spare")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte q_spare { get; set; }

      public override string ToString()
      {
        return "SMslInAirType";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class SMslsLocationFromRdrData {

      [DisplayName("(01) - q_NumOfMslInAir")]
      [Description("Range: 0 ... 8\u000D\u000ANumber of MSL in air from this MFU (LM)")]
      public byte q_NumOfMslInAir { get; set; }

      [DisplayName("(02) - q_MslInAir")]
      public SMslInAirType[] q_MslInAir { get; } = Enumerable.Range(0, COP_CZ_CCU_COP_ICD_V3_4.MaxNumOfMslsInAir).Select(i => new SMslInAirType()).ToArray();

      public override string ToString()
      {
        return "SMslsLocationFromRdrData";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class Training_Failures {

      [DisplayName("(01) - Sim_Python_BIT")]
      [Description("Range: 0 ... 8\u000D\u000A")]
      public byte Sim_Python_BIT { get; set; }

      [DisplayName("(02) - Sim_Derby_BIT")]
      [Description("Range: 0 ... 8\u000D\u000A")]
      public byte Sim_Derby_BIT { get; set; }

      [DisplayName("(03) - Sim_ER_LR_BIT")]
      [Description("Range: 0 ... 8\u000D\u000A")]
      public byte Sim_ER_LR_BIT { get; set; }

      [DisplayName("(04) - Dlu_Fail")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Dlu_Fail { get; set; }

      [DisplayName("(05) - Launch_Fail")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Launch_Fail { get; set; }

      [DisplayName("(06) - Mis_Fire")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Mis_Fire { get; set; }

      [DisplayName("(07) - Seeker_Does_Not_Lock")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Seeker_Does_Not_Lock { get; set; }

      public override string ToString()
      {
        return "Training_Failures";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class Training_Settings_Data {

      [DisplayName("(01) - Sim_Derby_To_Load")]
      [Description("Range: 0 ... 8\u000D\u000ANumber of Derby simulated missiles to be \"loaded\". The missiles will be loaded on top of existing ones up to the max possible number. ")]
      public byte Sim_Derby_To_Load { get; set; }

      [DisplayName("(02) - Sim_Python_To_Load")]
      [Description("Range: 0 ... 8\u000D\u000ANumber of OBJP simulated missiles to be \"loaded\". The missiles will be loaded on top of existing ones up to the max possible number.")]
      public byte Sim_Python_To_Load { get; set; }

      [DisplayName("(03) - Sim_ER_LR_To_Load")]
      [Description("Range: 0 ... 8\u000D\u000ANumber of ER or LR simulated missiles to be \"loaded\". The missiles will be loaded on top of existing ones up to the max possible number.")]
      public byte Sim_ER_LR_To_Load { get; set; }

      public override string ToString()
      {
        return "Training_Settings_Data";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_State {

      [DisplayName("(01) - Operability")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Operability Operability { get; set; }

      [DisplayName("(02) - Mode")]
      [Description("Range: 0 ... 15\u000D\u000A")]
      public COP_Mode Mode { get; set; }

      [DisplayName("(03) - Type")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public COP_Type Type { get; set; }

      [DisplayName("(04) - Spare")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare { get; set; }

      public override string ToString()
      {
        return "COP_State";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class LM_Readiness {

      [DisplayName("(01) - MCU_State")]
      [Description("Range: 0 ... 7\u000D\u000A")]
      public COP_MCU_State MCU_State { get; set; }

      [DisplayName("(02) - LM_Readiness_To_Engage")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Readiness_To_Engage LM_Readiness_To_Engage { get; set; }

      [DisplayName("(03) - LM_Armed")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public COP_LM_Armed LM_Armed { get; set; }

      [DisplayName("(04) - MCU_Abort_Button_Pressed")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean MCU_Abort_Button_Pressed { get; set; }

      [DisplayName("(05) - Spare2")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare2 { get; set; }

      public override string ToString()
      {
        return "LM_Readiness";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class MCU_Not_Ready_Reason {

      [DisplayName("(01) - Not_In_Immediate_Status")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean Not_In_Immediate_Status { get; set; }

      [DisplayName("(02) - Conflict_In_The_Fire_Source")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean Conflict_In_The_Fire_Source { get; set; }

      [DisplayName("(03) - No_Missile_Available")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean No_Missile_Available { get; set; }

      [DisplayName("(04) - Uplink_Activated")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean Uplink_Activated { get; set; }

      [DisplayName("(05) - Launch_Fail")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean Launch_Fail { get; set; }

      [DisplayName("(06) - MCU_Failure")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean MCU_Failure { get; set; }

      [DisplayName("(07) - LCU_Failure")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean LCU_Failure { get; set; }

      [DisplayName("(08) - MDCU_Failure")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean MDCU_Failure { get; set; }

      [DisplayName("(09) - BNET_Failure_And_LOAL")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean BNET_Failure_And_LOAL { get; set; }

      [DisplayName("(10) - INS_Failure")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean INS_Failure { get; set; }

      [DisplayName("(11) - GPS_Failure")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean GPS_Failure { get; set; }

      [DisplayName("(12) - Time_Validity_Failure")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean Time_Validity_Failure { get; set; }

      [DisplayName("(13) - Pwr_Supply_Failure")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean Pwr_Supply_Failure { get; set; }

      [DisplayName("(14) - MCU_disconnected")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean MCU_disconnected { get; set; }

      [DisplayName("(15) - Turret_In_No_Launch_Sector")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean Turret_In_No_Launch_Sector { get; set; }

      [DisplayName("(16) - Spare3")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public ushort Spare3 { get; set; }

      public override string ToString()
      {
        return "MCU_Not_Ready_Reason";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_Not_Ready_Reason {

      [DisplayName("(01) - COP_Client_Disconnected")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean COP_Client_Disconnected { get; set; }

      [DisplayName("(02) - COP_Server_Disconnected")]
      [Description("Range: 0 ... 1\u000D\u000AWill not be filled ever - Spare")]
      public No_Yes_Boolean COP_Server_Disconnected { get; set; }

      [DisplayName("(03) - CCU_Server_Disconnected")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean CCU_Server_Disconnected { get; set; }

      [DisplayName("(04) - COP_CCU_Desync")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean COP_CCU_Desync { get; set; }

      [DisplayName("(05) - COP_MCU_Desync")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean COP_MCU_Desync { get; set; }

      [DisplayName("(06) - DLQ_Smaller_Than_Threashold")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean DLQ_Smaller_Than_Threashold { get; set; }

      [DisplayName("(07) - COP_Client_Faulty")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean COP_Client_Faulty { get; set; }

      [DisplayName("(08) - COP_Server_Faulty")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean COP_Server_Faulty { get; set; }

      [DisplayName("(09) - CCU_Server_Faulty")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean CCU_Server_Faulty { get; set; }

      [DisplayName("(10) - DTM_Comp")]
      [Description("Range: 0 ... 1\u000D\u000Aare miniICS and COP server DTM version compatible")]
      public No_Yes_Boolean DTM_Comp { get; set; }

      [DisplayName("(11) - SICS_Faulty")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean SICS_Faulty { get; set; }

      [DisplayName("(12) - Toplite_Faulty")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean Toplite_Faulty { get; set; }

      [DisplayName("(13) - Spare4")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public ushort Spare4 { get; set; }

      [DisplayName("(14) - Spare5")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public ushort Spare5 { get; set; }

      [DisplayName("(15) - Spare6")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public ushort Spare6 { get; set; }

      [DisplayName("(16) - Spare7")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public ushort Spare7 { get; set; }

      public override string ToString()
      {
        return "COP_Not_Ready_Reason";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_Missiles_Available_For_Fire {

      [DisplayName("(01) - objP")]
      [Description("Range: 0 ... 8\u000D\u000A")]
      public byte objP { get; set; }

      [DisplayName("(02) - Derby")]
      [Description("Range: 0 ... 8\u000D\u000A")]
      public byte Derby { get; set; }

      [DisplayName("(03) - ER_Derby")]
      [Description("Range: 0 ... 8\u000D\u000A")]
      public byte ER_Derby { get; set; }

      [DisplayName("(04) - LR_Derby")]
      [Description("Range: 0 ... 8\u000D\u000A")]
      public byte LR_Derby { get; set; }

      public override string ToString()
      {
        return "COP_Missiles_Available_For_Fire";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_Total_Missiles {

      [DisplayName("(01) - objP")]
      [Description("Range: 0 ... 8\u000D\u000A")]
      public byte objP { get; set; }

      [DisplayName("(02) - Derby")]
      [Description("Range: 0 ... 7\u000D\u000A")]
      public byte Derby { get; set; }

      [DisplayName("(03) - ER_Derby")]
      [Description("Range: 0 ... 8\u000D\u000A")]
      public byte ER_Derby { get; set; }

      [DisplayName("(04) - LR_Derby")]
      [Description("Range: 0 ... 8\u000D\u000A")]
      public byte LR_Derby { get; set; }

      public override string ToString()
      {
        return "COP_Total_Missiles";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class LLA_Position {

      [DisplayName("(01) - Latitude")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Latitude { get; set; }

      [DisplayName("(02) - Longitude")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Longitude { get; set; }

      [DisplayName("(03) - Altitude")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Altitude { get; set; }

      public override string ToString()
      {
        return "LLA_Position";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_Sector_Element {

      [DisplayName("(01) - Start_Azimuth")]
      [Description("Range: 0 ... 359\u000D\u000AStart azimuth, relevant to north, LSB = 1")]
      public ushort Start_Azimuth { get; set; }

      [DisplayName("(02) - Sector_Size")]
      [Description("Range: 0 ... 359\u000D\u000ASize of sector in degrees, CW.")]
      public ushort Sector_Size { get; set; }

      public override string ToString()
      {
        return "COP_Sector_Element";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_Component_Status_1 {

      [DisplayName("(01) - MCU")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Component_Status MCU { get; set; }

      [DisplayName("(02) - INS")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Component_Status INS { get; set; }

      [DisplayName("(03) - GPS")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Component_Status GPS { get; set; }

      [DisplayName("(04) - MDCU")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Component_Status MDCU { get; set; }

      [DisplayName("(05) - LCU1")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Component_Status LCU1 { get; set; }

      [DisplayName("(06) - LCU2")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Component_Status LCU2 { get; set; }

      [DisplayName("(07) - BNET")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Component_Status BNET { get; set; }

      [DisplayName("(08) - Time_Validity")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Component_Status Time_Validity { get; set; }

      [DisplayName("(09) - mDRS")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Component_Status mDRS { get; set; }

      [DisplayName("(10) - TopLite")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Component_Status TopLite { get; set; }

      [DisplayName("(11) - Rubidium")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Component_Status Rubidium { get; set; }

      [DisplayName("(12) - Spare3")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public uint Spare3 { get; set; }

      [DisplayName("(13) - Spare4")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public uint Spare4 { get; set; }

      [DisplayName("(14) - Spare5")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public uint Spare5 { get; set; }

      [DisplayName("(15) - Spare6")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public uint Spare6 { get; set; }

      [DisplayName("(16) - Spare7")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public uint Spare7 { get; set; }

      public override string ToString()
      {
        return "COP_Component_Status_1";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class DLQ {

      [DisplayName("(01) - Data_Source")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public DLQ_Data_Source Data_Source { get; set; }

      [DisplayName("(02) - DLQ_Value")]
      [Description("Range: 0 ... 127\u000D\u000A")]
      public byte DLQ_Value { get; set; }

      public override string ToString()
      {
        return "DLQ";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_Version {

      [DisplayName("(01) - MCU")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort MCU { get; set; }

      [DisplayName("(02) - BNET")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort BNET { get; set; }

      [DisplayName("(03) - INS")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort INS { get; set; }

      [DisplayName("(04) - MDCU")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort MDCU { get; set; }

      [DisplayName("(05) - LCU")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort LCU { get; set; }

      [DisplayName("(06) - COP_Server_Version")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort COP_Server_Version { get; set; }

      [DisplayName("(07) - COP_Client_Version")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort COP_Client_Version { get; set; }

      [DisplayName("(08) - ICS_DTM_Version")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort ICS_DTM_Version { get; set; }

      public override string ToString()
      {
        return "COP_Version";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_MCU_Engagement {

      [DisplayName("(01) - MCU_Engagement_Handling")]
      [Description("Range: 0 ... 7\u000D\u000A")]
      public COP_Engagement_Handling MCU_Engagement_Handling { get; set; }

      [DisplayName("(02) - MCU_Engagement_Status")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_MCU_Engagement_Status MCU_Engagement_Status { get; set; }

      [DisplayName("(03) - Spare")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare { get; set; }

      [DisplayName("(04) - Engagement_Abort_Source")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Engagement_Abort_Source Engagement_Abort_Source { get; set; }

      public override string ToString()
      {
        return "COP_MCU_Engagement";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_Allocated_Missile_Status {

      [DisplayName("(01) - ICP_Allocated")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public Type_Of_ICP ICP_Allocated { get; set; }

      [DisplayName("(02) - objP_ICP_Lock")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public ICP_Lock_Status objP_ICP_Lock { get; set; }

      [DisplayName("(03) - Derby_ICP_Lock")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public ICP_Lock_Status Derby_ICP_Lock { get; set; }

      [DisplayName("(04) - ER_Derby_ICP_Lock")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public ICP_Lock_Status ER_Derby_ICP_Lock { get; set; }

      [DisplayName("(05) - LR_Derby_ICP_Lock")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public ICP_Lock_Status LR_Derby_ICP_Lock { get; set; }

      [DisplayName("(06) - ICP_Stage")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public ICP_Stage ICP_Stage { get; set; }

      [DisplayName("(07) - Lock_Type")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public Locking_Policy Lock_Type { get; set; }

      [DisplayName("(08) - Misfire_Indication")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean Misfire_Indication { get; set; }

      [DisplayName("(09) - Misfire_Reason")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COP_Misfire_Reason Misfire_Reason { get; set; }

      [DisplayName("(10) - spare")]
      [Description("Range: 0 ... 31\u000D\u000A")]
      public byte spare { get; set; }

      public override string ToString()
      {
        return "COP_Allocated_Missile_Status";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_MCU_Engagement_Spare {

      [DisplayName("(01) - Seeker_Mode")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public COP_Seeker_mode Seeker_Mode { get; set; }

      [DisplayName("(02) - SPare1")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte SPare1 { get; set; }

      [DisplayName("(03) - Spare2")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare2 { get; set; }

      [DisplayName("(04) - Spare3")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Spare3 { get; set; }

      [DisplayName("(05) - Spare4")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public byte Spare4 { get; set; }

      [DisplayName("(06) - Spare5")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public byte Spare5 { get; set; }

      public override string ToString()
      {
        return "COP_MCU_Engagement_Spare";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class COP_Engagement_Status {

      [DisplayName("(01) - Track_ID")]
      [Description("Range: 1 ... 65535\u000D\u000AUnique Track ID")]
      public ushort Track_ID { get; set; }

      [DisplayName("(02) - CCU_Engagement_ID")]
      [Description("Range: 1 ... 65535\u000D\u000ACCU-ICS unique engagement ID")]
      public ushort CCU_Engagement_ID { get; set; }

      [DisplayName("(03) - COP_Engagement_ID")]
      [Description("Range: 1 ... 65535\u000D\u000ACOP-MCU Unique engagement Identifier")]
      public ushort COP_Engagement_ID { get; set; }

      [DisplayName("(04) - MCU_Engagement")]
      public COP_MCU_Engagement MCU_Engagement { get; } = new COP_MCU_Engagement();

      [DisplayName("(05) - Allocated_Missile_Status")]
      public COP_Allocated_Missile_Status Allocated_Missile_Status { get; } = new COP_Allocated_Missile_Status();

      [DisplayName("(06) - Engagement_Launch_Time")]
      [Description("Range: 0 ... 4294967295\u000D\u000A")]
      public uint Engagement_Launch_Time { get; set; }

      [DisplayName("(07) - Turret_Launching_Azimuth")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort Turret_Launching_Azimuth { get; set; }

      [DisplayName("(08) - MCU_Engagement_Spare")]
      public COP_MCU_Engagement_Spare MCU_Engagement_Spare { get; } = new COP_MCU_Engagement_Spare();

      [DisplayName("(09) - Coupled_Station_Id")]
      [Description("Range: 0 ... 8\u000D\u000A")]
      public byte Coupled_Station_Id { get; set; }

      public override string ToString()
      {
        return "COP_Engagement_Status";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class SMslDlData {

      [DisplayName("(01) - q_MslID")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte q_MslID { get; set; }

      [DisplayName("(02) - q_TimeOfLastDlMsgRcv")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000A")]
      public int q_TimeOfLastDlMsgRcv { get; set; }

      [DisplayName("(03) - q_TimeFromMLL")]
      [Description("Range: 0 ... 4294967295\u000D\u000A0-200000 [miliSec]")]
      public uint q_TimeFromMLL { get; set; }

      [DisplayName("(04) - q_servoPressure")]
      [Description("Range: 0 ... 65535\u000D\u000A500 – 13000")]
      public ushort q_servoPressure { get; set; }

      [DisplayName("(05) - q_mslPosition_N_L0")]
      [Description("Range: -20000 ... 32767\u000D\u000AScaleFactor: 10\u000D\u000ANorth position in L0 (Origin at MFU location) -200000 to 200000 resulution 10")]
      public double q_mslPosition_N_L0 {
        get =>  _q_mslPosition_N_L0 * 10;
        set => _q_mslPosition_N_L0 = (short)Math.Round(value / 10);
      }
      internal short _q_mslPosition_N_L0;

      [DisplayName("(06) - q_mslPosition_E_L0")]
      [Description("Range: -20000 ... 32767\u000D\u000AScaleFactor: 10\u000D\u000A")]
      public double q_mslPosition_E_L0 {
        get =>  _q_mslPosition_E_L0 * 10;
        set => _q_mslPosition_E_L0 = (short)Math.Round(value / 10);
      }
      internal short _q_mslPosition_E_L0;

      [DisplayName("(07) - q_mslPosition_D_LO")]
      [Description("Range: -32768 ... 32767\u000D\u000AScaleFactor: 10\u000D\u000A")]
      public double q_mslPosition_D_LO {
        get =>  _q_mslPosition_D_LO * 10;
        set => _q_mslPosition_D_LO = (short)Math.Round(value / 10);
      }
      internal short _q_mslPosition_D_LO;

      [DisplayName("(08) - q_mslVn_L0")]
      [Description("Range: -2000 ... 32767\u000D\u000AVn Velocity in L0 -2000 to 2000 [m/s]")]
      public short q_mslVn_L0 { get; set; }

      [DisplayName("(09) - q_mslVe_L0")]
      [Description("Range: -2000 ... 32767\u000D\u000A")]
      public short q_mslVe_L0 { get; set; }

      [DisplayName("(10) - q_mslVd_L0")]
      [Description("Range: -2000 ... 32767\u000D\u000A")]
      public short q_mslVd_L0 { get; set; }

      [DisplayName("(11) - q_MslNavPos_X_Uncertainty")]
      [Description("Range: 0 ... 1000\u000D\u000AUncertainty of Msl position from Navigation\u000D\u000AX 0-1000 [m]")]
      public ushort q_MslNavPos_X_Uncertainty { get; set; }

      [DisplayName("(12) - q_MslNavPos_Y_Uncertainty")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort q_MslNavPos_Y_Uncertainty { get; set; }

      [DisplayName("(13) - q_MslNavPos_Z_Uncertainty")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort q_MslNavPos_Z_Uncertainty { get; set; }

      [DisplayName("(14) - q_MslNavVel_X_Uncertainty")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort q_MslNavVel_X_Uncertainty { get; set; }

      [DisplayName("(15) - q_MslNavVel_Y_Uncertainty")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort q_MslNavVel_Y_Uncertainty { get; set; }

      [DisplayName("(16) - q_MslNavVel_Z_Uncertainty")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort q_MslNavVel_Z_Uncertainty { get; set; }

      [DisplayName("(17) - q_TimeToGo")]
      [Description("Range: 0 ... 32767\u000D\u000A")]
      public short q_TimeToGo { get; set; }

      [DisplayName("(18) - q_guidanceMode")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte q_guidanceMode { get; set; }

      [DisplayName("(19) - q_navPerformance")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte q_navPerformance { get; set; }

      [DisplayName("(20) - q_netState")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte q_netState { get; set; }

      [DisplayName("(21) - q_seekerState")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte q_seekerState { get; set; }

      [DisplayName("(22) - q_skrGimbal_Az")]
      [Description("Range: 0 ... 255\u000D\u000A0-60 [deg]")]
      public byte q_skrGimbal_Az { get; set; }

      [DisplayName("(23) - q_skrGimbal_El")]
      [Description("Range: 0 ... 255\u000D\u000A0-60 [deg]")]
      public byte q_skrGimbal_El { get; set; }

      [DisplayName("(24) - q_skrRange")]
      [Description("Range: -32768 ... 32767\u000D\u000ARange from Missile to Target. 0 – 15000 [m] ")]
      public short q_skrRange { get; set; }

      [DisplayName("(25) - q_skrRangeDot")]
      [Description("Range: -32768 ... 32767\u000D\u000A[m/s]")]
      public short q_skrRangeDot { get; set; }

      [DisplayName("(26) - q_skrSnr")]
      [Description("Range: 0 ... 255\u000D\u000A0-64 [db]")]
      public byte q_skrSnr { get; set; }

      [DisplayName("(27) - q_skrEcm")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte q_skrEcm { get; set; }

      [DisplayName("(28) - q_lamdaDotY")]
      [Description("Range: -32768 ... 32767\u000D\u000A0 - 64 [deg/sec]")]
      public short q_lamdaDotY { get; set; }

      [DisplayName("(29) - q_lamdaDotZ")]
      [Description("Range: -32768 ... 32767\u000D\u000A")]
      public short q_lamdaDotZ { get; set; }

      [DisplayName("(30) - q_mmcBit")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte q_mmcBit { get; set; }

      [DisplayName("(31) - q_mslStatus")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte q_mslStatus { get; set; }

      public override string ToString()
      {
        return "SMslDlData";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class SMslsInAirDlData {

      [DisplayName("(01) - q_NumOfDlReportsInMsg")]
      [Description("Range: 0 ... 8\u000D\u000A")]
      public ushort q_NumOfDlReportsInMsg { get; set; }

      [DisplayName("(02) - q_MslInAir")]
      public SMslDlData[] q_MslInAir { get; } = Enumerable.Range(0, COP_CZ_CCU_COP_ICD_V3_4.MaxNumOfMslsInAir).Select(i => new SMslDlData()).ToArray();

      public override string ToString()
      {
        return "SMslsInAirDlData";
      }
    }

    [TypeConverter(typeof(ExpandableObjectConverter))]
    [Description("")]
    public class Toplite_Track_Data {

      [DisplayName("(01) - TargetUpdateTime")]
      [Description("Range: 0 ... 4294967295\u000D\u000ATime measurements made in msec elapsed from midnight (UTC time)")]
      public uint TargetUpdateTime { get; set; }

      [DisplayName("(02) - TargetPositionNorth")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000ATarget position and velocity in LLLN based on LM position.\u000D\u000A(Axis directions: North, East, Down)\u000D\u000APosition: ±163830m\u000D\u000AVelocity: ±1270m/s")]
      public int TargetPositionNorth { get; set; }

      [DisplayName("(03) - TargetPositionEast")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000A")]
      public int TargetPositionEast { get; set; }

      [DisplayName("(04) - TargetPositionDown")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000A")]
      public int TargetPositionDown { get; set; }

      [DisplayName("(05) - TargetVelocityNorth")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000A")]
      public int TargetVelocityNorth { get; set; }

      [DisplayName("(06) - TargetVelocityEast")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000ATarget position and velocity in LLLN based on LM position.\u000D\u000A(Axis directions: North, East, Down)\u000D\u000APosition: ±163830m\u000D\u000AVelocity: ±1270m/s")]
      public int TargetVelocityEast { get; set; }

      [DisplayName("(07) - TargetVelocityDown")]
      [Description("Range: -2147483648 ... 2147483647\u000D\u000A")]
      public int TargetVelocityDown { get; set; }

      [DisplayName("(08) - AutonomousTargetID")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort AutonomousTargetID { get; set; }

      [DisplayName("(09) - AutonomousEngagementID")]
      [Description("Range: 0 ... 65535\u000D\u000A65500 – 65535")]
      public ushort AutonomousEngagementID { get; set; }

      [DisplayName("(10) - TopliteTargetType")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public TopliteTargetType TopliteTargetType { get; set; }

      [DisplayName("(11) - Validity")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean Validity { get; set; }

      [DisplayName("(12) - spare")]
      [Description("Range: 0 ... 127\u000D\u000A")]
      public byte spare { get; set; }

      public override string ToString()
      {
        return "Toplite_Track_Data";
      }
    }

    public class C2_COP_ASP_Track : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - live_tracks")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public eSafetyBoolean live_tracks { get; set; }

      [DisplayName("(03) - number_of_tracks")]
      [Description("Range: 0 ... 20\u000D\u000AThe number of tracks in this batch")]
      public byte number_of_tracks { get; set; }

      [DisplayName("(04) - Spare")]
      [Description("Range: 0 ... 65535\u000D\u000AFFU")]
      public ushort Spare { get; set; }

      [DisplayName("(05) - Track_data")]
      public ASP_Track[] Track_data { get; } = Enumerable.Range(0, COP_CZ_CCU_COP_ICD_V3_4.Num_Of_ASP_Tracks_In_Array).Select(i => new ASP_Track()).ToArray();

      public C2_COP_ASP_Track()
      {
        CCU_COP_Header.msg_type = 1;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write((byte)live_tracks);
        writter.Write(number_of_tracks);
        writter.Write(Spare);
        for (var i = 0; i < number_of_tracks; i++)
        {
          writter.Write(Track_data[i].Track_ID);
          writter.Write(Track_data[i].FCR_track_ID);
          for (var j = 0; j < Track_data[i].TN_str.Length; j++)
          {
            writter.Write(Track_data[i].TN_str[j]);
          }
          writter.Write(Track_data[i].track_update_time);
          writter.Write((byte)Track_data[i].target_type);
          writter.Write((byte)Track_data[i].target_identity);
          {
            byte help = 0;
            help |= (byte)(((byte)Track_data[i].Threat_Level_And_Tracking_Status.Threat_Level & 15) << 0);
            help |= (byte)(((byte)Track_data[i].Threat_Level_And_Tracking_Status.Tracking_Status & 3) << 4);
            help |= (byte)(((byte)Track_data[i].Threat_Level_And_Tracking_Status.Spare & 3) << 6);
            writter.Write(help);
          }
          {
            byte help = 0;
            help |= (byte)(((byte)Track_data[i].special_data.Militry_emergency & 1) << 0);
            help |= (byte)(((byte)Track_data[i].special_data.Special_Identification & 1) << 1);
            help |= (byte)(((byte)Track_data[i].special_data.Foe_Friend & 3) << 2);
            help |= (byte)(((byte)Track_data[i].special_data.Jammer & 1) << 4);
            help |= (byte)(((byte)Track_data[i].special_data.Spare1 & 1) << 5);
            help |= (byte)(((byte)Track_data[i].special_data.Spare2 & 1) << 6);
            help |= (byte)(((byte)Track_data[i].special_data.Spare3 & 1) << 7);
            writter.Write(help);
          }
          writter.Write(Track_data[i].SIF_1_Code);
          writter.Write(Track_data[i].SIF_2_Code);
          writter.Write(Track_data[i].SIF_3_Code);
          writter.Write(Track_data[i].Position.X_Pos);
          writter.Write(Track_data[i].Position.Y_Pos);
          writter.Write(Track_data[i].Position.Z_Pos);
          writter.Write(Track_data[i].Velocity._Vx);
          writter.Write(Track_data[i].Velocity._Vy);
          writter.Write(Track_data[i].Velocity._Vz);
          writter.Write(Track_data[i].Spare);
        }
      }

      public C2_COP_ASP_Track Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        live_tracks = (eSafetyBoolean) reader.ReadByte();
        number_of_tracks = reader.ReadByte();
        Spare = reader.ReadUInt16();
        for (var i = 0; i < number_of_tracks; i++)
        {
          Track_data[i].Track_ID = reader.ReadUInt16();
          Track_data[i].FCR_track_ID = reader.ReadUInt16();
          for (var j = 0; j < Track_data[i].TN_str.Length; j++)
          {
            Track_data[i].TN_str[j] = reader.ReadByte();
          }
          Track_data[i].track_update_time = reader.ReadSingle();
          Track_data[i].target_type = (ASP_target_type) reader.ReadByte();
          Track_data[i].target_identity = (ASP_Target_Identity) reader.ReadByte();
          {
            byte help = reader.ReadByte();
            Track_data[i].Threat_Level_And_Tracking_Status.Threat_Level = (ASP_Threat_Level)((help >> 0) & 15);
            Track_data[i].Threat_Level_And_Tracking_Status.Tracking_Status = (ASP_tracking_status)((help >> 4) & 3);
            Track_data[i].Threat_Level_And_Tracking_Status.Spare = (byte)((help >> 6) & 3);
          }
          {
            byte help = reader.ReadByte();
            Track_data[i].special_data.Militry_emergency = (IFF_Militry_Emergency)((help >> 0) & 1);
            Track_data[i].special_data.Special_Identification = (IFF_Special_Identification)((help >> 1) & 1);
            Track_data[i].special_data.Foe_Friend = (IFF_Foe_Friend)((help >> 2) & 3);
            Track_data[i].special_data.Jammer = (IFF_Jammer)((help >> 4) & 1);
            Track_data[i].special_data.Spare1 = (byte)((help >> 5) & 1);
            Track_data[i].special_data.Spare2 = (byte)((help >> 6) & 1);
            Track_data[i].special_data.Spare3 = (byte)((help >> 7) & 1);
          }
          Track_data[i].SIF_1_Code = reader.ReadByte();
          Track_data[i].SIF_2_Code = reader.ReadUInt16();
          Track_data[i].SIF_3_Code = reader.ReadUInt16();
          Track_data[i].Position.X_Pos = reader.ReadInt32();
          Track_data[i].Position.Y_Pos = reader.ReadInt32();
          Track_data[i].Position.Z_Pos = reader.ReadInt32();
          Track_data[i].Velocity._Vx = reader.ReadInt32();
          Track_data[i].Velocity._Vy = reader.ReadInt32();
          Track_data[i].Velocity._Vz = reader.ReadInt32();
          Track_data[i].Spare = reader.ReadByte();
        }
        return this;
      }

      public override string ToString()
      {
        return "C2_COP_ASP_Track";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_ASP_Track ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: live_tracks = {live_tracks}");
        if (!Enum.GetValues(typeof(eSafetyBoolean)).OfType<eSafetyBoolean>().Contains(live_tracks)) writter.AppendLine("!!      Value is not valid for enum eSafetyBoolean");
        pos += 1;
        writter.AppendLine($"{pos:X4}: number_of_tracks = {number_of_tracks}");
        if (number_of_tracks < 0) writter.AppendLine("!!      Value is less than 0");
        if (number_of_tracks > 20) writter.AppendLine("!!      Value is greather than 20");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Spare = {Spare}");
        if (Spare < 0) writter.AppendLine("!!      Value is less than 0");
        if (Spare > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        for (var i = 0; i < number_of_tracks; i++)
        {
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Track_ID = {Track_data[i].Track_ID}");
          if (Track_data[i].Track_ID < 0) writter.AppendLine("!!      Value is less than 0");
          if (Track_data[i].Track_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].FCR_track_ID = {Track_data[i].FCR_track_ID}");
          if (Track_data[i].FCR_track_ID < 0) writter.AppendLine("!!      Value is less than 0");
          if (Track_data[i].FCR_track_ID > 12000) writter.AppendLine("!!      Value is greather than 12000");
          pos += 2;
          for (var j = 0; j < Track_data[i].TN_str.Length; j++)
          {
            writter.AppendLine($"{pos:X4}: Track_data[{i}].TN_str[{j}] = {Track_data[i].TN_str[j]}");
            pos += 1;
          }
          writter.AppendLine($"{pos:X4}: Track_data[{i}].track_update_time = {Track_data[i].track_update_time}");
          if (Track_data[i].track_update_time < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
          if (Track_data[i].track_update_time > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].target_type = {Track_data[i].target_type}");
          if (!Enum.GetValues(typeof(ASP_target_type)).OfType<ASP_target_type>().Contains(Track_data[i].target_type)) writter.AppendLine("!!      Value is not valid for enum ASP_target_type");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].target_identity = {Track_data[i].target_identity}");
          if (!Enum.GetValues(typeof(ASP_Target_Identity)).OfType<ASP_Target_Identity>().Contains(Track_data[i].target_identity)) writter.AppendLine("!!      Value is not valid for enum ASP_Target_Identity");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Threat_Level_And_Tracking_Status.Threat_Level = {Track_data[i].Threat_Level_And_Tracking_Status.Threat_Level}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Threat_Level_And_Tracking_Status.Tracking_Status = {Track_data[i].Threat_Level_And_Tracking_Status.Tracking_Status}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Threat_Level_And_Tracking_Status.Spare = {Track_data[i].Threat_Level_And_Tracking_Status.Spare}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Militry_emergency = {Track_data[i].special_data.Militry_emergency}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Special_Identification = {Track_data[i].special_data.Special_Identification}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Foe_Friend = {Track_data[i].special_data.Foe_Friend}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Jammer = {Track_data[i].special_data.Jammer}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Spare1 = {Track_data[i].special_data.Spare1}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Spare2 = {Track_data[i].special_data.Spare2}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Spare3 = {Track_data[i].special_data.Spare3}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].SIF_1_Code = {Track_data[i].SIF_1_Code}");
          if (Track_data[i].SIF_1_Code < 0) writter.AppendLine("!!      Value is less than 0");
          if (Track_data[i].SIF_1_Code > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].SIF_2_Code = {Track_data[i].SIF_2_Code}");
          if (Track_data[i].SIF_2_Code < 0) writter.AppendLine("!!      Value is less than 0");
          if (Track_data[i].SIF_2_Code > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].SIF_3_Code = {Track_data[i].SIF_3_Code}");
          if (Track_data[i].SIF_3_Code < 0) writter.AppendLine("!!      Value is less than 0");
          if (Track_data[i].SIF_3_Code > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Position.X_Pos = {Track_data[i].Position.X_Pos}");
          if (Track_data[i].Position.X_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Track_data[i].Position.X_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Position.Y_Pos = {Track_data[i].Position.Y_Pos}");
          if (Track_data[i].Position.Y_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Track_data[i].Position.Y_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Position.Z_Pos = {Track_data[i].Position.Z_Pos}");
          if (Track_data[i].Position.Z_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Track_data[i].Position.Z_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Velocity.Vx = {Track_data[i].Velocity.Vx}");
          if (Track_data[i].Velocity._Vx < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Track_data[i].Velocity._Vx > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Velocity.Vy = {Track_data[i].Velocity.Vy}");
          if (Track_data[i].Velocity._Vy < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Track_data[i].Velocity._Vy > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Velocity.Vz = {Track_data[i].Velocity.Vz}");
          if (Track_data[i].Velocity._Vz < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Track_data[i].Velocity._Vz > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Spare = {Track_data[i].Spare}");
          if (Track_data[i].Spare < 0) writter.AppendLine("!!      Value is less than 0");
          if (Track_data[i].Spare > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
        }
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_ASP_Track ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: live_tracks = {live_tracks}");
        if (!Enum.GetValues(typeof(eSafetyBoolean)).OfType<eSafetyBoolean>().Contains(live_tracks)) writter.AppendLine("!!      Value is not valid for enum eSafetyBoolean");
        pos += 1;
        writter.AppendLine($"{pos:X4}: number_of_tracks = {number_of_tracks}");
        if (number_of_tracks < 0) writter.AppendLine("!!      Value is less than 0");
        if (number_of_tracks > 20) writter.AppendLine("!!      Value is greather than 20");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Spare = {Spare}");
        if (Spare < 0) writter.AppendLine("!!      Value is less than 0");
        if (Spare > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        for (var i = 0; i < number_of_tracks; i++)
        {
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Track_ID = {Track_data[i].Track_ID}");
          if (Track_data[i].Track_ID < 0) writter.AppendLine("!!      Value is less than 0");
          if (Track_data[i].Track_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].FCR_track_ID = {Track_data[i].FCR_track_ID}");
          if (Track_data[i].FCR_track_ID < 0) writter.AppendLine("!!      Value is less than 0");
          if (Track_data[i].FCR_track_ID > 12000) writter.AppendLine("!!      Value is greather than 12000");
          pos += 2;
          for (var j = 0; j < Track_data[i].TN_str.Length; j++)
          {
            writter.AppendLine($"{pos:X4}: Track_data[{i}].TN_str[{j}] = {Track_data[i].TN_str[j]}");
            pos += 1;
          }
          writter.AppendLine($"{pos:X4}: Track_data[{i}].track_update_time = {Track_data[i].track_update_time}");
          if (Track_data[i].track_update_time < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
          if (Track_data[i].track_update_time > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].target_type = {Track_data[i].target_type}");
          if (!Enum.GetValues(typeof(ASP_target_type)).OfType<ASP_target_type>().Contains(Track_data[i].target_type)) writter.AppendLine("!!      Value is not valid for enum ASP_target_type");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].target_identity = {Track_data[i].target_identity}");
          if (!Enum.GetValues(typeof(ASP_Target_Identity)).OfType<ASP_Target_Identity>().Contains(Track_data[i].target_identity)) writter.AppendLine("!!      Value is not valid for enum ASP_Target_Identity");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Threat_Level_And_Tracking_Status.Threat_Level = {Track_data[i].Threat_Level_And_Tracking_Status.Threat_Level}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Threat_Level_And_Tracking_Status.Tracking_Status = {Track_data[i].Threat_Level_And_Tracking_Status.Tracking_Status}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Threat_Level_And_Tracking_Status.Spare = {Track_data[i].Threat_Level_And_Tracking_Status.Spare}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Militry_emergency = {Track_data[i].special_data.Militry_emergency}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Special_Identification = {Track_data[i].special_data.Special_Identification}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Foe_Friend = {Track_data[i].special_data.Foe_Friend}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Jammer = {Track_data[i].special_data.Jammer}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Spare1 = {Track_data[i].special_data.Spare1}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Spare2 = {Track_data[i].special_data.Spare2}");
          writter.AppendLine($"{pos:X4}: Track_data[{i}].special_data.Spare3 = {Track_data[i].special_data.Spare3}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].SIF_1_Code = {Track_data[i].SIF_1_Code}");
          if (Track_data[i].SIF_1_Code < 0) writter.AppendLine("!!      Value is less than 0");
          if (Track_data[i].SIF_1_Code > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].SIF_2_Code = {Track_data[i].SIF_2_Code}");
          if (Track_data[i].SIF_2_Code < 0) writter.AppendLine("!!      Value is less than 0");
          if (Track_data[i].SIF_2_Code > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].SIF_3_Code = {Track_data[i].SIF_3_Code}");
          if (Track_data[i].SIF_3_Code < 0) writter.AppendLine("!!      Value is less than 0");
          if (Track_data[i].SIF_3_Code > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Position.X_Pos = {Track_data[i].Position.X_Pos}");
          if (Track_data[i].Position.X_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Track_data[i].Position.X_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Position.Y_Pos = {Track_data[i].Position.Y_Pos}");
          if (Track_data[i].Position.Y_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Track_data[i].Position.Y_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Position.Z_Pos = {Track_data[i].Position.Z_Pos}");
          if (Track_data[i].Position.Z_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Track_data[i].Position.Z_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Velocity.Vx = {Track_data[i].Velocity.Vx}");
          if (Track_data[i].Velocity._Vx < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Track_data[i].Velocity._Vx > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Velocity.Vy = {Track_data[i].Velocity.Vy}");
          if (Track_data[i].Velocity._Vy < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Track_data[i].Velocity._Vy > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Velocity.Vz = {Track_data[i].Velocity.Vz}");
          if (Track_data[i].Velocity._Vz < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Track_data[i].Velocity._Vz > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Track_data[{i}].Spare = {Track_data[i].Spare}");
          if (Track_data[i].Spare < 0) writter.AppendLine("!!      Value is less than 0");
          if (Track_data[i].Spare > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
        }
        return writter.ToString();
      }
    }

    public class C2_COP_Engaged_Track : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Live_Tracks")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public eSafetyBoolean Live_Tracks { get; set; }

      [DisplayName("(03) - Track_Data")]
      public Engaged_Track Track_Data { get; } = new Engaged_Track();

      public C2_COP_Engaged_Track()
      {
        CCU_COP_Header.msg_type = 2;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write((byte)Live_Tracks);
        writter.Write(Track_Data.Track_ID);
        writter.Write((byte)Track_Data.target_type);
        writter.Write(Track_Data.track_update_time);
        writter.Write(Track_Data.Position.X);
        writter.Write(Track_Data.Position.Y);
        writter.Write(Track_Data.Position.Z);
        writter.Write(Track_Data.Velocity._Vx);
        writter.Write(Track_Data.Velocity._Vy);
        writter.Write(Track_Data.Velocity._Vz);
        writter.Write(Track_Data.Position_Uncertainty.Var_XX);
        writter.Write(Track_Data.Position_Uncertainty.Cov_XY);
        writter.Write(Track_Data.Position_Uncertainty.Cov_XZ);
        writter.Write(Track_Data.Position_Uncertainty.Var_YY);
        writter.Write(Track_Data.Position_Uncertainty.Cov_YZ);
        writter.Write(Track_Data.Position_Uncertainty.Var_ZZ);
        writter.Write(Track_Data.Velocity_Uncertainty.Var_XX);
        writter.Write(Track_Data.Velocity_Uncertainty.Cov_XY);
        writter.Write(Track_Data.Velocity_Uncertainty.Cov_XZ);
        writter.Write(Track_Data.Velocity_Uncertainty.Var_YY);
        writter.Write(Track_Data.Velocity_Uncertainty.Cov_YZ);
        writter.Write(Track_Data.Velocity_Uncertainty.Var_ZZ);
        writter.Write(Track_Data.FCR_Curr_Position.Longitude);
        writter.Write(Track_Data.FCR_Curr_Position.Latitude);
        writter.Write(Track_Data.FCR_Curr_Position.Altitude);
        writter.Write(Track_Data.Sensor_To_Target_Azimuth);
        writter.Write(Track_Data.Sensor_To_Target_Elevation);
        writter.Write((byte)Track_Data.Target_Source_Radar_type);
        writter.Write(Track_Data.Spare);
      }

      public C2_COP_Engaged_Track Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        Live_Tracks = (eSafetyBoolean) reader.ReadByte();
        Track_Data.Track_ID = reader.ReadUInt16();
        Track_Data.target_type = (ASP_target_type) reader.ReadByte();
        Track_Data.track_update_time = reader.ReadSingle();
        Track_Data.Position.X = reader.ReadSingle();
        Track_Data.Position.Y = reader.ReadSingle();
        Track_Data.Position.Z = reader.ReadSingle();
        Track_Data.Velocity._Vx = reader.ReadInt32();
        Track_Data.Velocity._Vy = reader.ReadInt32();
        Track_Data.Velocity._Vz = reader.ReadInt32();
        Track_Data.Position_Uncertainty.Var_XX = reader.ReadSingle();
        Track_Data.Position_Uncertainty.Cov_XY = reader.ReadSingle();
        Track_Data.Position_Uncertainty.Cov_XZ = reader.ReadSingle();
        Track_Data.Position_Uncertainty.Var_YY = reader.ReadSingle();
        Track_Data.Position_Uncertainty.Cov_YZ = reader.ReadSingle();
        Track_Data.Position_Uncertainty.Var_ZZ = reader.ReadSingle();
        Track_Data.Velocity_Uncertainty.Var_XX = reader.ReadSingle();
        Track_Data.Velocity_Uncertainty.Cov_XY = reader.ReadSingle();
        Track_Data.Velocity_Uncertainty.Cov_XZ = reader.ReadSingle();
        Track_Data.Velocity_Uncertainty.Var_YY = reader.ReadSingle();
        Track_Data.Velocity_Uncertainty.Cov_YZ = reader.ReadSingle();
        Track_Data.Velocity_Uncertainty.Var_ZZ = reader.ReadSingle();
        Track_Data.FCR_Curr_Position.Longitude = reader.ReadSingle();
        Track_Data.FCR_Curr_Position.Latitude = reader.ReadSingle();
        Track_Data.FCR_Curr_Position.Altitude = reader.ReadSingle();
        Track_Data.Sensor_To_Target_Azimuth = reader.ReadSingle();
        Track_Data.Sensor_To_Target_Elevation = reader.ReadSingle();
        Track_Data.Target_Source_Radar_type = (Sys_Radar_type) reader.ReadByte();
        Track_Data.Spare = reader.ReadUInt16();
        return this;
      }

      public override string ToString()
      {
        return "C2_COP_Engaged_Track";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Engaged_Track ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Live_Tracks = {Live_Tracks}");
        if (!Enum.GetValues(typeof(eSafetyBoolean)).OfType<eSafetyBoolean>().Contains(Live_Tracks)) writter.AppendLine("!!      Value is not valid for enum eSafetyBoolean");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Track_Data.Track_ID = {Track_Data.Track_ID}");
        if (Track_Data.Track_ID < 0) writter.AppendLine("!!      Value is less than 0");
        if (Track_Data.Track_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Track_Data.target_type = {Track_Data.target_type}");
        if (!Enum.GetValues(typeof(ASP_target_type)).OfType<ASP_target_type>().Contains(Track_Data.target_type)) writter.AppendLine("!!      Value is not valid for enum ASP_target_type");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Track_Data.track_update_time = {Track_Data.track_update_time}");
        if (Track_Data.track_update_time < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.track_update_time > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position.X = {Track_Data.Position.X}");
        if (Track_Data.Position.X < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position.X > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position.Y = {Track_Data.Position.Y}");
        if (Track_Data.Position.Y < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position.Y > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position.Z = {Track_Data.Position.Z}");
        if (Track_Data.Position.Z < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position.Z > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity.Vx = {Track_Data.Velocity.Vx}");
        if (Track_Data.Velocity._Vx < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Track_Data.Velocity._Vx > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity.Vy = {Track_Data.Velocity.Vy}");
        if (Track_Data.Velocity._Vy < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Track_Data.Velocity._Vy > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity.Vz = {Track_Data.Velocity.Vz}");
        if (Track_Data.Velocity._Vz < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Track_Data.Velocity._Vz > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position_Uncertainty.Var_XX = {Track_Data.Position_Uncertainty.Var_XX}");
        if (Track_Data.Position_Uncertainty.Var_XX < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position_Uncertainty.Var_XX > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position_Uncertainty.Cov_XY = {Track_Data.Position_Uncertainty.Cov_XY}");
        if (Track_Data.Position_Uncertainty.Cov_XY < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position_Uncertainty.Cov_XY > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position_Uncertainty.Cov_XZ = {Track_Data.Position_Uncertainty.Cov_XZ}");
        if (Track_Data.Position_Uncertainty.Cov_XZ < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position_Uncertainty.Cov_XZ > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position_Uncertainty.Var_YY = {Track_Data.Position_Uncertainty.Var_YY}");
        if (Track_Data.Position_Uncertainty.Var_YY < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position_Uncertainty.Var_YY > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position_Uncertainty.Cov_YZ = {Track_Data.Position_Uncertainty.Cov_YZ}");
        if (Track_Data.Position_Uncertainty.Cov_YZ < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position_Uncertainty.Cov_YZ > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position_Uncertainty.Var_ZZ = {Track_Data.Position_Uncertainty.Var_ZZ}");
        if (Track_Data.Position_Uncertainty.Var_ZZ < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position_Uncertainty.Var_ZZ > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity_Uncertainty.Var_XX = {Track_Data.Velocity_Uncertainty.Var_XX}");
        if (Track_Data.Velocity_Uncertainty.Var_XX < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Velocity_Uncertainty.Var_XX > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity_Uncertainty.Cov_XY = {Track_Data.Velocity_Uncertainty.Cov_XY}");
        if (Track_Data.Velocity_Uncertainty.Cov_XY < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Velocity_Uncertainty.Cov_XY > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity_Uncertainty.Cov_XZ = {Track_Data.Velocity_Uncertainty.Cov_XZ}");
        if (Track_Data.Velocity_Uncertainty.Cov_XZ < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Velocity_Uncertainty.Cov_XZ > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity_Uncertainty.Var_YY = {Track_Data.Velocity_Uncertainty.Var_YY}");
        if (Track_Data.Velocity_Uncertainty.Var_YY < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Velocity_Uncertainty.Var_YY > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity_Uncertainty.Cov_YZ = {Track_Data.Velocity_Uncertainty.Cov_YZ}");
        if (Track_Data.Velocity_Uncertainty.Cov_YZ < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Velocity_Uncertainty.Cov_YZ > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity_Uncertainty.Var_ZZ = {Track_Data.Velocity_Uncertainty.Var_ZZ}");
        if (Track_Data.Velocity_Uncertainty.Var_ZZ < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Velocity_Uncertainty.Var_ZZ > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.FCR_Curr_Position.Longitude = {Track_Data.FCR_Curr_Position.Longitude}");
        if (Track_Data.FCR_Curr_Position.Longitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.FCR_Curr_Position.Longitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.FCR_Curr_Position.Latitude = {Track_Data.FCR_Curr_Position.Latitude}");
        if (Track_Data.FCR_Curr_Position.Latitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.FCR_Curr_Position.Latitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.FCR_Curr_Position.Altitude = {Track_Data.FCR_Curr_Position.Altitude}");
        if (Track_Data.FCR_Curr_Position.Altitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.FCR_Curr_Position.Altitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Sensor_To_Target_Azimuth = {Track_Data.Sensor_To_Target_Azimuth}");
        if (Track_Data.Sensor_To_Target_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
        if (Track_Data.Sensor_To_Target_Azimuth > 6.28318530717959) writter.AppendLine("!!      Value is greather than 6.28318530717959");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Sensor_To_Target_Elevation = {Track_Data.Sensor_To_Target_Elevation}");
        if (Track_Data.Sensor_To_Target_Elevation < -1.5707963267949) writter.AppendLine("!!      Value is less than -1.5707963267949");
        if (Track_Data.Sensor_To_Target_Elevation > 1.5707963267949) writter.AppendLine("!!      Value is greather than 1.5707963267949");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Target_Source_Radar_type = {Track_Data.Target_Source_Radar_type}");
        if (!Enum.GetValues(typeof(Sys_Radar_type)).OfType<Sys_Radar_type>().Contains(Track_Data.Target_Source_Radar_type)) writter.AppendLine("!!      Value is not valid for enum Sys_Radar_type");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Track_Data.Spare = {Track_Data.Spare}");
        if (Track_Data.Spare < 0) writter.AppendLine("!!      Value is less than 0");
        if (Track_Data.Spare > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Engaged_Track ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Live_Tracks = {Live_Tracks}");
        if (!Enum.GetValues(typeof(eSafetyBoolean)).OfType<eSafetyBoolean>().Contains(Live_Tracks)) writter.AppendLine("!!      Value is not valid for enum eSafetyBoolean");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Track_Data.Track_ID = {Track_Data.Track_ID}");
        if (Track_Data.Track_ID < 0) writter.AppendLine("!!      Value is less than 0");
        if (Track_Data.Track_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Track_Data.target_type = {Track_Data.target_type}");
        if (!Enum.GetValues(typeof(ASP_target_type)).OfType<ASP_target_type>().Contains(Track_Data.target_type)) writter.AppendLine("!!      Value is not valid for enum ASP_target_type");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Track_Data.track_update_time = {Track_Data.track_update_time}");
        if (Track_Data.track_update_time < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.track_update_time > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position.X = {Track_Data.Position.X}");
        if (Track_Data.Position.X < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position.X > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position.Y = {Track_Data.Position.Y}");
        if (Track_Data.Position.Y < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position.Y > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position.Z = {Track_Data.Position.Z}");
        if (Track_Data.Position.Z < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position.Z > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity.Vx = {Track_Data.Velocity.Vx}");
        if (Track_Data.Velocity._Vx < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Track_Data.Velocity._Vx > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity.Vy = {Track_Data.Velocity.Vy}");
        if (Track_Data.Velocity._Vy < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Track_Data.Velocity._Vy > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity.Vz = {Track_Data.Velocity.Vz}");
        if (Track_Data.Velocity._Vz < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Track_Data.Velocity._Vz > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position_Uncertainty.Var_XX = {Track_Data.Position_Uncertainty.Var_XX}");
        if (Track_Data.Position_Uncertainty.Var_XX < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position_Uncertainty.Var_XX > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position_Uncertainty.Cov_XY = {Track_Data.Position_Uncertainty.Cov_XY}");
        if (Track_Data.Position_Uncertainty.Cov_XY < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position_Uncertainty.Cov_XY > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position_Uncertainty.Cov_XZ = {Track_Data.Position_Uncertainty.Cov_XZ}");
        if (Track_Data.Position_Uncertainty.Cov_XZ < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position_Uncertainty.Cov_XZ > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position_Uncertainty.Var_YY = {Track_Data.Position_Uncertainty.Var_YY}");
        if (Track_Data.Position_Uncertainty.Var_YY < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position_Uncertainty.Var_YY > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position_Uncertainty.Cov_YZ = {Track_Data.Position_Uncertainty.Cov_YZ}");
        if (Track_Data.Position_Uncertainty.Cov_YZ < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position_Uncertainty.Cov_YZ > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Position_Uncertainty.Var_ZZ = {Track_Data.Position_Uncertainty.Var_ZZ}");
        if (Track_Data.Position_Uncertainty.Var_ZZ < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Position_Uncertainty.Var_ZZ > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity_Uncertainty.Var_XX = {Track_Data.Velocity_Uncertainty.Var_XX}");
        if (Track_Data.Velocity_Uncertainty.Var_XX < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Velocity_Uncertainty.Var_XX > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity_Uncertainty.Cov_XY = {Track_Data.Velocity_Uncertainty.Cov_XY}");
        if (Track_Data.Velocity_Uncertainty.Cov_XY < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Velocity_Uncertainty.Cov_XY > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity_Uncertainty.Cov_XZ = {Track_Data.Velocity_Uncertainty.Cov_XZ}");
        if (Track_Data.Velocity_Uncertainty.Cov_XZ < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Velocity_Uncertainty.Cov_XZ > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity_Uncertainty.Var_YY = {Track_Data.Velocity_Uncertainty.Var_YY}");
        if (Track_Data.Velocity_Uncertainty.Var_YY < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Velocity_Uncertainty.Var_YY > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity_Uncertainty.Cov_YZ = {Track_Data.Velocity_Uncertainty.Cov_YZ}");
        if (Track_Data.Velocity_Uncertainty.Cov_YZ < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Velocity_Uncertainty.Cov_YZ > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Velocity_Uncertainty.Var_ZZ = {Track_Data.Velocity_Uncertainty.Var_ZZ}");
        if (Track_Data.Velocity_Uncertainty.Var_ZZ < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.Velocity_Uncertainty.Var_ZZ > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.FCR_Curr_Position.Longitude = {Track_Data.FCR_Curr_Position.Longitude}");
        if (Track_Data.FCR_Curr_Position.Longitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.FCR_Curr_Position.Longitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.FCR_Curr_Position.Latitude = {Track_Data.FCR_Curr_Position.Latitude}");
        if (Track_Data.FCR_Curr_Position.Latitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.FCR_Curr_Position.Latitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.FCR_Curr_Position.Altitude = {Track_Data.FCR_Curr_Position.Altitude}");
        if (Track_Data.FCR_Curr_Position.Altitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Track_Data.FCR_Curr_Position.Altitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Sensor_To_Target_Azimuth = {Track_Data.Sensor_To_Target_Azimuth}");
        if (Track_Data.Sensor_To_Target_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
        if (Track_Data.Sensor_To_Target_Azimuth > 6.28318530717959) writter.AppendLine("!!      Value is greather than 6.28318530717959");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Sensor_To_Target_Elevation = {Track_Data.Sensor_To_Target_Elevation}");
        if (Track_Data.Sensor_To_Target_Elevation < -1.5707963267949) writter.AppendLine("!!      Value is less than -1.5707963267949");
        if (Track_Data.Sensor_To_Target_Elevation > 1.5707963267949) writter.AppendLine("!!      Value is greather than 1.5707963267949");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Track_Data.Target_Source_Radar_type = {Track_Data.Target_Source_Radar_type}");
        if (!Enum.GetValues(typeof(Sys_Radar_type)).OfType<Sys_Radar_type>().Contains(Track_Data.Target_Source_Radar_type)) writter.AppendLine("!!      Value is not valid for enum Sys_Radar_type");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Track_Data.Spare = {Track_Data.Spare}");
        if (Track_Data.Spare < 0) writter.AppendLine("!!      Value is less than 0");
        if (Track_Data.Spare > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        return writter.ToString();
      }
    }

    public class C2_COP_Status : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - C2_Mode")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public C2_Mode C2_Mode { get; set; }

      [DisplayName("(03) - C2_Substate")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public C2_Substate C2_Substate { get; set; }

      [DisplayName("(04) - C2_Safety_State")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public C2_Safety_State C2_Safety_State { get; set; }

      [DisplayName("(05) - C2_Fire_Readiness_Status")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public C2_Fire_Readiness_Status C2_Fire_Readiness_Status { get; set; }

      [DisplayName("(06) - C2_Status")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public Component_Status C2_Status { get; set; }

      [DisplayName("(07) - C2_Position")]
      public ECEF_Position C2_Position { get; } = new ECEF_Position();

      [DisplayName("(08) - Num_Of_Conneted_Sensors")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public byte Num_Of_Conneted_Sensors { get; set; }

      [DisplayName("(09) - Radar_Info")]
      public Radar_Info Radar_Info { get; } = new Radar_Info();

      [DisplayName("(10) - Spare")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort Spare { get; set; }

      [DisplayName("(11) - DLQ_IBIT_Request")]
      public DLQ_IBIT_Request DLQ_IBIT_Request { get; } = new DLQ_IBIT_Request();

      [DisplayName("(12) - Num_Of_Connected_MFUs")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte Num_Of_Connected_MFUs { get; set; }

      [DisplayName("(13) - Initialization_Data_Array")]
      public COP_Initialization_Data_Array[] Initialization_Data_Array { get; } = Enumerable.Range(0, COP_CZ_CCU_COP_ICD_V3_4.Num_Of_Connected_MFUs).Select(i => new COP_Initialization_Data_Array()).ToArray();

      public C2_COP_Status()
      {
        CCU_COP_Header.msg_type = 4;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write((byte)C2_Mode);
        writter.Write((byte)C2_Substate);
        writter.Write((byte)C2_Safety_State);
        writter.Write((byte)C2_Fire_Readiness_Status);
        writter.Write((byte)C2_Status);
        writter.Write(C2_Position.X_Pos);
        writter.Write(C2_Position.Y_Pos);
        writter.Write(C2_Position.Z_Pos);
        writter.Write(Num_Of_Conneted_Sensors);
        writter.Write(Radar_Info.Radar_Position.X_Pos);
        writter.Write(Radar_Info.Radar_Position.Y_Pos);
        writter.Write(Radar_Info.Radar_Position.Z_Pos);
        writter.Write((byte)Radar_Info.Radar_Type);
        {
          byte help = 0;
          help |= (byte)(((byte)Radar_Info.Is_Location_Valid & 1) << 0);
          help |= (byte)(((byte)Radar_Info.Spare_7 & 127) << 1);
          writter.Write(help);
        }
        writter.Write(Spare);
        {
          byte help = 0;
          help |= (byte)(((byte)DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID1 & 1) << 0);
          help |= (byte)(((byte)DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID2 & 1) << 1);
          help |= (byte)(((byte)DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID3 & 1) << 2);
          help |= (byte)(((byte)DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID4 & 1) << 3);
          help |= (byte)(((byte)DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID5 & 1) << 4);
          help |= (byte)(((byte)DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID6 & 1) << 5);
          help |= (byte)(((byte)DLQ_IBIT_Request.Relevant_COP.bSpare1 & 1) << 6);
          help |= (byte)(((byte)DLQ_IBIT_Request.Relevant_COP.bSpare2 & 1) << 7);
          writter.Write(help);
        }
        writter.Write((byte)DLQ_IBIT_Request.IBIT_Action);
        writter.Write(Num_Of_Connected_MFUs);
        for (var i = 0; i < Num_Of_Connected_MFUs; i++)
        {
          writter.Write(Initialization_Data_Array[i].Init_Data.COP_Logical_ID);
          writter.Write(Initialization_Data_Array[i].Init_Data.MCU_Serial_ID);
          {
            byte help = 0;
            help |= (byte)(((byte)Initialization_Data_Array[i].Init_Data.Required_State.Link_Type & 1) << 0);
            help |= (byte)(((byte)Initialization_Data_Array[i].Init_Data.Required_State.Locking_Policy & 1) << 1);
            help |= (byte)(((byte)Initialization_Data_Array[i].Init_Data.Required_State.Fire_Source & 1) << 2);
            help |= (byte)(((byte)Initialization_Data_Array[i].Init_Data.Required_State.Spare1 & 1) << 3);
            help |= (byte)(((byte)Initialization_Data_Array[i].Init_Data.Required_State.Spare2 & 1) << 4);
            help |= (byte)(((byte)Initialization_Data_Array[i].Init_Data.Required_State.Spare3 & 1) << 5);
            help |= (byte)(((byte)Initialization_Data_Array[i].Init_Data.Required_State.Spare4 & 1) << 6);
            help |= (byte)(((byte)Initialization_Data_Array[i].Init_Data.Required_State.Spare5 & 1) << 7);
            writter.Write(help);
          }
          writter.Write(Initialization_Data_Array[i].Init_Data.Site_Text_Length);
          for (var j = 0; j < Initialization_Data_Array[i].Init_Data.Site_Text_Length; j++)
          {
            writter.Write(Initialization_Data_Array[i].Init_Data.Site_Name[j]);
          }
        }
      }

      public C2_COP_Status Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        C2_Mode = (C2_Mode) reader.ReadByte();
        C2_Substate = (C2_Substate) reader.ReadByte();
        C2_Safety_State = (C2_Safety_State) reader.ReadByte();
        C2_Fire_Readiness_Status = (C2_Fire_Readiness_Status) reader.ReadByte();
        C2_Status = (Component_Status) reader.ReadByte();
        C2_Position.X_Pos = reader.ReadInt32();
        C2_Position.Y_Pos = reader.ReadInt32();
        C2_Position.Z_Pos = reader.ReadInt32();
        Num_Of_Conneted_Sensors = reader.ReadByte();
        Radar_Info.Radar_Position.X_Pos = reader.ReadInt32();
        Radar_Info.Radar_Position.Y_Pos = reader.ReadInt32();
        Radar_Info.Radar_Position.Z_Pos = reader.ReadInt32();
        Radar_Info.Radar_Type = (Radar_Type) reader.ReadByte();
        {
          byte help = reader.ReadByte();
          Radar_Info.Is_Location_Valid = (No_Yes_Boolean)((help >> 0) & 1);
          Radar_Info.Spare_7 = (byte)((help >> 1) & 127);
        }
        Spare = reader.ReadUInt16();
        {
          byte help = reader.ReadByte();
          DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID1 = (byte)((help >> 0) & 1);
          DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID2 = (byte)((help >> 1) & 1);
          DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID3 = (byte)((help >> 2) & 1);
          DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID4 = (byte)((help >> 3) & 1);
          DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID5 = (byte)((help >> 4) & 1);
          DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID6 = (byte)((help >> 5) & 1);
          DLQ_IBIT_Request.Relevant_COP.bSpare1 = (byte)((help >> 6) & 1);
          DLQ_IBIT_Request.Relevant_COP.bSpare2 = (byte)((help >> 7) & 1);
        }
        DLQ_IBIT_Request.IBIT_Action = (IBIT_Action) reader.ReadByte();
        Num_Of_Connected_MFUs = reader.ReadByte();
        for (var i = 0; i < Num_Of_Connected_MFUs; i++)
        {
          Initialization_Data_Array[i].Init_Data.COP_Logical_ID = reader.ReadByte();
          Initialization_Data_Array[i].Init_Data.MCU_Serial_ID = reader.ReadByte();
          {
            byte help = reader.ReadByte();
            Initialization_Data_Array[i].Init_Data.Required_State.Link_Type = (Init_Link_Type)((help >> 0) & 1);
            Initialization_Data_Array[i].Init_Data.Required_State.Locking_Policy = (Locking_Policy)((help >> 1) & 1);
            Initialization_Data_Array[i].Init_Data.Required_State.Fire_Source = (Init_Fire_Source)((help >> 2) & 1);
            Initialization_Data_Array[i].Init_Data.Required_State.Spare1 = (byte)((help >> 3) & 1);
            Initialization_Data_Array[i].Init_Data.Required_State.Spare2 = (byte)((help >> 4) & 1);
            Initialization_Data_Array[i].Init_Data.Required_State.Spare3 = (byte)((help >> 5) & 1);
            Initialization_Data_Array[i].Init_Data.Required_State.Spare4 = (byte)((help >> 6) & 1);
            Initialization_Data_Array[i].Init_Data.Required_State.Spare5 = (byte)((help >> 7) & 1);
          }
          Initialization_Data_Array[i].Init_Data.Site_Text_Length = reader.ReadByte();
          for (var j = 0; j < Initialization_Data_Array[i].Init_Data.Site_Text_Length; j++)
          {
            Initialization_Data_Array[i].Init_Data.Site_Name[j] = reader.ReadByte();
          }
        }
        return this;
      }

      public override string ToString()
      {
        return "C2_COP_Status";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Status ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: C2_Mode = {C2_Mode}");
        if (!Enum.GetValues(typeof(C2_Mode)).OfType<C2_Mode>().Contains(C2_Mode)) writter.AppendLine("!!      Value is not valid for enum C2_Mode");
        pos += 1;
        writter.AppendLine($"{pos:X4}: C2_Substate = {C2_Substate}");
        if (!Enum.GetValues(typeof(C2_Substate)).OfType<C2_Substate>().Contains(C2_Substate)) writter.AppendLine("!!      Value is not valid for enum C2_Substate");
        pos += 1;
        writter.AppendLine($"{pos:X4}: C2_Safety_State = {C2_Safety_State}");
        if (!Enum.GetValues(typeof(C2_Safety_State)).OfType<C2_Safety_State>().Contains(C2_Safety_State)) writter.AppendLine("!!      Value is not valid for enum C2_Safety_State");
        pos += 1;
        writter.AppendLine($"{pos:X4}: C2_Fire_Readiness_Status = {C2_Fire_Readiness_Status}");
        if (!Enum.GetValues(typeof(C2_Fire_Readiness_Status)).OfType<C2_Fire_Readiness_Status>().Contains(C2_Fire_Readiness_Status)) writter.AppendLine("!!      Value is not valid for enum C2_Fire_Readiness_Status");
        pos += 1;
        writter.AppendLine($"{pos:X4}: C2_Status = {C2_Status}");
        if (!Enum.GetValues(typeof(Component_Status)).OfType<Component_Status>().Contains(C2_Status)) writter.AppendLine("!!      Value is not valid for enum Component_Status");
        pos += 1;
        writter.AppendLine($"{pos:X4}: C2_Position.X_Pos = {C2_Position.X_Pos}");
        if (C2_Position.X_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (C2_Position.X_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: C2_Position.Y_Pos = {C2_Position.Y_Pos}");
        if (C2_Position.Y_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (C2_Position.Y_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: C2_Position.Z_Pos = {C2_Position.Z_Pos}");
        if (C2_Position.Z_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (C2_Position.Z_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Num_Of_Conneted_Sensors = {Num_Of_Conneted_Sensors}");
        if (Num_Of_Conneted_Sensors < 0) writter.AppendLine("!!      Value is less than 0");
        if (Num_Of_Conneted_Sensors > 1) writter.AppendLine("!!      Value is greather than 1");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Radar_Info.Radar_Position.X_Pos = {Radar_Info.Radar_Position.X_Pos}");
        if (Radar_Info.Radar_Position.X_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Radar_Info.Radar_Position.X_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Radar_Info.Radar_Position.Y_Pos = {Radar_Info.Radar_Position.Y_Pos}");
        if (Radar_Info.Radar_Position.Y_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Radar_Info.Radar_Position.Y_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Radar_Info.Radar_Position.Z_Pos = {Radar_Info.Radar_Position.Z_Pos}");
        if (Radar_Info.Radar_Position.Z_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Radar_Info.Radar_Position.Z_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Radar_Info.Radar_Type = {Radar_Info.Radar_Type}");
        if (!Enum.GetValues(typeof(Radar_Type)).OfType<Radar_Type>().Contains(Radar_Info.Radar_Type)) writter.AppendLine("!!      Value is not valid for enum Radar_Type");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Radar_Info.Is_Location_Valid = {Radar_Info.Is_Location_Valid}");
        writter.AppendLine($"{pos:X4}: Radar_Info.Spare_7 = {Radar_Info.Spare_7}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Spare = {Spare}");
        if (Spare < 0) writter.AppendLine("!!      Value is less than 0");
        if (Spare > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID1 = {DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID1}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID2 = {DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID2}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID3 = {DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID3}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID4 = {DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID4}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID5 = {DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID5}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID6 = {DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID6}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.bSpare1 = {DLQ_IBIT_Request.Relevant_COP.bSpare1}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.bSpare2 = {DLQ_IBIT_Request.Relevant_COP.bSpare2}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.IBIT_Action = {DLQ_IBIT_Request.IBIT_Action}");
        if (!Enum.GetValues(typeof(IBIT_Action)).OfType<IBIT_Action>().Contains(DLQ_IBIT_Request.IBIT_Action)) writter.AppendLine("!!      Value is not valid for enum IBIT_Action");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Num_Of_Connected_MFUs = {Num_Of_Connected_MFUs}");
        if (Num_Of_Connected_MFUs < 0) writter.AppendLine("!!      Value is less than 0");
        if (Num_Of_Connected_MFUs > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        for (var i = 0; i < Num_Of_Connected_MFUs; i++)
        {
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.COP_Logical_ID = {Initialization_Data_Array[i].Init_Data.COP_Logical_ID}");
          if (Initialization_Data_Array[i].Init_Data.COP_Logical_ID < 1) writter.AppendLine("!!      Value is less than 1");
          if (Initialization_Data_Array[i].Init_Data.COP_Logical_ID > 6) writter.AppendLine("!!      Value is greather than 6");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.MCU_Serial_ID = {Initialization_Data_Array[i].Init_Data.MCU_Serial_ID}");
          if (Initialization_Data_Array[i].Init_Data.MCU_Serial_ID < 0) writter.AppendLine("!!      Value is less than 0");
          if (Initialization_Data_Array[i].Init_Data.MCU_Serial_ID > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Link_Type = {Initialization_Data_Array[i].Init_Data.Required_State.Link_Type}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Locking_Policy = {Initialization_Data_Array[i].Init_Data.Required_State.Locking_Policy}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Fire_Source = {Initialization_Data_Array[i].Init_Data.Required_State.Fire_Source}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Spare1 = {Initialization_Data_Array[i].Init_Data.Required_State.Spare1}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Spare2 = {Initialization_Data_Array[i].Init_Data.Required_State.Spare2}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Spare3 = {Initialization_Data_Array[i].Init_Data.Required_State.Spare3}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Spare4 = {Initialization_Data_Array[i].Init_Data.Required_State.Spare4}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Spare5 = {Initialization_Data_Array[i].Init_Data.Required_State.Spare5}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Site_Text_Length = {Initialization_Data_Array[i].Init_Data.Site_Text_Length}");
          if (Initialization_Data_Array[i].Init_Data.Site_Text_Length < 1) writter.AppendLine("!!      Value is less than 1");
          if (Initialization_Data_Array[i].Init_Data.Site_Text_Length > 10) writter.AppendLine("!!      Value is greather than 10");
          pos += 1;
          for (var j = 0; j < Initialization_Data_Array[i].Init_Data.Site_Text_Length; j++)
          {
            writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Site_Name[{j}] = {Initialization_Data_Array[i].Init_Data.Site_Name[j]}");
            pos += 1;
          }
        }
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Status ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: C2_Mode = {C2_Mode}");
        if (!Enum.GetValues(typeof(C2_Mode)).OfType<C2_Mode>().Contains(C2_Mode)) writter.AppendLine("!!      Value is not valid for enum C2_Mode");
        pos += 1;
        writter.AppendLine($"{pos:X4}: C2_Substate = {C2_Substate}");
        if (!Enum.GetValues(typeof(C2_Substate)).OfType<C2_Substate>().Contains(C2_Substate)) writter.AppendLine("!!      Value is not valid for enum C2_Substate");
        pos += 1;
        writter.AppendLine($"{pos:X4}: C2_Safety_State = {C2_Safety_State}");
        if (!Enum.GetValues(typeof(C2_Safety_State)).OfType<C2_Safety_State>().Contains(C2_Safety_State)) writter.AppendLine("!!      Value is not valid for enum C2_Safety_State");
        pos += 1;
        writter.AppendLine($"{pos:X4}: C2_Fire_Readiness_Status = {C2_Fire_Readiness_Status}");
        if (!Enum.GetValues(typeof(C2_Fire_Readiness_Status)).OfType<C2_Fire_Readiness_Status>().Contains(C2_Fire_Readiness_Status)) writter.AppendLine("!!      Value is not valid for enum C2_Fire_Readiness_Status");
        pos += 1;
        writter.AppendLine($"{pos:X4}: C2_Status = {C2_Status}");
        if (!Enum.GetValues(typeof(Component_Status)).OfType<Component_Status>().Contains(C2_Status)) writter.AppendLine("!!      Value is not valid for enum Component_Status");
        pos += 1;
        writter.AppendLine($"{pos:X4}: C2_Position.X_Pos = {C2_Position.X_Pos}");
        if (C2_Position.X_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (C2_Position.X_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: C2_Position.Y_Pos = {C2_Position.Y_Pos}");
        if (C2_Position.Y_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (C2_Position.Y_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: C2_Position.Z_Pos = {C2_Position.Z_Pos}");
        if (C2_Position.Z_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (C2_Position.Z_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Num_Of_Conneted_Sensors = {Num_Of_Conneted_Sensors}");
        if (Num_Of_Conneted_Sensors < 0) writter.AppendLine("!!      Value is less than 0");
        if (Num_Of_Conneted_Sensors > 1) writter.AppendLine("!!      Value is greather than 1");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Radar_Info.Radar_Position.X_Pos = {Radar_Info.Radar_Position.X_Pos}");
        if (Radar_Info.Radar_Position.X_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Radar_Info.Radar_Position.X_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Radar_Info.Radar_Position.Y_Pos = {Radar_Info.Radar_Position.Y_Pos}");
        if (Radar_Info.Radar_Position.Y_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Radar_Info.Radar_Position.Y_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Radar_Info.Radar_Position.Z_Pos = {Radar_Info.Radar_Position.Z_Pos}");
        if (Radar_Info.Radar_Position.Z_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Radar_Info.Radar_Position.Z_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Radar_Info.Radar_Type = {Radar_Info.Radar_Type}");
        if (!Enum.GetValues(typeof(Radar_Type)).OfType<Radar_Type>().Contains(Radar_Info.Radar_Type)) writter.AppendLine("!!      Value is not valid for enum Radar_Type");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Radar_Info.Is_Location_Valid = {Radar_Info.Is_Location_Valid}");
        writter.AppendLine($"{pos:X4}: Radar_Info.Spare_7 = {Radar_Info.Spare_7}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Spare = {Spare}");
        if (Spare < 0) writter.AppendLine("!!      Value is less than 0");
        if (Spare > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID1 = {DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID1}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID2 = {DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID2}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID3 = {DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID3}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID4 = {DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID4}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID5 = {DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID5}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID6 = {DLQ_IBIT_Request.Relevant_COP.COP_Logical_ID6}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.bSpare1 = {DLQ_IBIT_Request.Relevant_COP.bSpare1}");
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.Relevant_COP.bSpare2 = {DLQ_IBIT_Request.Relevant_COP.bSpare2}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: DLQ_IBIT_Request.IBIT_Action = {DLQ_IBIT_Request.IBIT_Action}");
        if (!Enum.GetValues(typeof(IBIT_Action)).OfType<IBIT_Action>().Contains(DLQ_IBIT_Request.IBIT_Action)) writter.AppendLine("!!      Value is not valid for enum IBIT_Action");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Num_Of_Connected_MFUs = {Num_Of_Connected_MFUs}");
        if (Num_Of_Connected_MFUs < 0) writter.AppendLine("!!      Value is less than 0");
        if (Num_Of_Connected_MFUs > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        for (var i = 0; i < Num_Of_Connected_MFUs; i++)
        {
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.COP_Logical_ID = {Initialization_Data_Array[i].Init_Data.COP_Logical_ID}");
          if (Initialization_Data_Array[i].Init_Data.COP_Logical_ID < 1) writter.AppendLine("!!      Value is less than 1");
          if (Initialization_Data_Array[i].Init_Data.COP_Logical_ID > 6) writter.AppendLine("!!      Value is greather than 6");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.MCU_Serial_ID = {Initialization_Data_Array[i].Init_Data.MCU_Serial_ID}");
          if (Initialization_Data_Array[i].Init_Data.MCU_Serial_ID < 0) writter.AppendLine("!!      Value is less than 0");
          if (Initialization_Data_Array[i].Init_Data.MCU_Serial_ID > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Link_Type = {Initialization_Data_Array[i].Init_Data.Required_State.Link_Type}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Locking_Policy = {Initialization_Data_Array[i].Init_Data.Required_State.Locking_Policy}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Fire_Source = {Initialization_Data_Array[i].Init_Data.Required_State.Fire_Source}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Spare1 = {Initialization_Data_Array[i].Init_Data.Required_State.Spare1}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Spare2 = {Initialization_Data_Array[i].Init_Data.Required_State.Spare2}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Spare3 = {Initialization_Data_Array[i].Init_Data.Required_State.Spare3}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Spare4 = {Initialization_Data_Array[i].Init_Data.Required_State.Spare4}");
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Required_State.Spare5 = {Initialization_Data_Array[i].Init_Data.Required_State.Spare5}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Site_Text_Length = {Initialization_Data_Array[i].Init_Data.Site_Text_Length}");
          if (Initialization_Data_Array[i].Init_Data.Site_Text_Length < 1) writter.AppendLine("!!      Value is less than 1");
          if (Initialization_Data_Array[i].Init_Data.Site_Text_Length > 10) writter.AppendLine("!!      Value is greather than 10");
          pos += 1;
          for (var j = 0; j < Initialization_Data_Array[i].Init_Data.Site_Text_Length; j++)
          {
            writter.AppendLine($"{pos:X4}: Initialization_Data_Array[{i}].Init_Data.Site_Name[{j}] = {Initialization_Data_Array[i].Init_Data.Site_Name[j]}");
            pos += 1;
          }
        }
        return writter.ToString();
      }
    }

    public class C2_COP_ACK : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Sequence_num")]
      [Description("Range: 0 ... 4294967295\u000D\u000AAcknowledged message sequence number (cyclic)")]
      public uint Sequence_num { get; set; }

      public C2_COP_ACK()
      {
        CCU_COP_Header.msg_type = 5;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(Sequence_num);
      }

      public C2_COP_ACK Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        Sequence_num = reader.ReadUInt32();
        return this;
      }

      public override string ToString()
      {
        return "C2_COP_ACK";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_ACK ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Sequence_num = {Sequence_num}");
        if (Sequence_num < 0) writter.AppendLine("!!      Value is less than 0");
        if (Sequence_num > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_ACK ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Sequence_num = {Sequence_num}");
        if (Sequence_num < 0) writter.AppendLine("!!      Value is less than 0");
        if (Sequence_num > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        return writter.ToString();
      }
    }

    public class C2_COP_GSP : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Num_Of_GSPs")]
      [Description("Range: 0 ... 7\u000D\u000A")]
      public byte Num_Of_GSPs { get; set; }

      [DisplayName("(03) - Last_In_Cycle")]
      [Description("Range: 0 ... 1\u000D\u000A")]
      public No_Yes_Boolean Last_In_Cycle { get; set; }

      [DisplayName("(04) - GSP_Element")]
      public GSP_Element[] GSP_Element { get; } = Enumerable.Range(0, COP_CZ_CCU_COP_ICD_V3_4.Num_Of_GSP_In_Array).Select(i => new GSP_Element()).ToArray();

      [DisplayName("(05) - Spare")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort Spare { get; set; }

      public C2_COP_GSP()
      {
        CCU_COP_Header.msg_type = 6;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(Num_Of_GSPs);
        writter.Write((byte)Last_In_Cycle);
        for (var i = 0; i < Num_Of_GSPs; i++)
        {
          writter.Write((byte)GSP_Element[i].Area_Type);
          writter.Write(GSP_Element[i].GSP_Element_General_Data.Area_ID);
          {
            byte help = 0;
            help |= (byte)(((byte)GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.Area_Status & 1) << 0);
            help |= (byte)(((byte)GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.Area_Hostility & 3) << 1);
            help |= (byte)(((byte)GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.SeverityOrWCOType & 3) << 3);
            help |= (byte)(((byte)GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.Spare & 7) << 5);
            writter.Write(help);
          }
          writter.Write(GSP_Element[i].GSP_Element_General_Data.Name_Length);
          for (var j = 0; j < GSP_Element[i].GSP_Element_General_Data.Name_Length; j++)
          {
            writter.Write(GSP_Element[i].GSP_Element_General_Data.Name[j]);
          }
          writter.Write((byte)GSP_Element[i].GSP_Element_General_Data.Object_Type);
          writter.Write(GSP_Element[i].GSP_Element_General_Data.Vertices_Num);
          for (var j = 0; j < GSP_Element[i].GSP_Element_General_Data.Vertices_Num; j++)
          {
            writter.Write(GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].X_Pos);
            writter.Write(GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Y_Pos);
            writter.Write(GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Z_Pos);
          }
          writter.Write(GSP_Element[i].Area_Element.SKOZ_Min_Height);
          writter.Write(GSP_Element[i].Area_Element.SKOZ_Max_Height);
          writter.Write(GSP_Element[i].Sector_Element.Start_Azimuth);
          writter.Write(GSP_Element[i].Sector_Element.End_Azimuth);
          writter.Write(GSP_Element[i].Sector_Element.Radius);
          writter.Write(GSP_Element[i].Ellipse_Element.Azimuth);
          writter.Write(GSP_Element[i].Ellipse_Element.Major_Radius);
          writter.Write(GSP_Element[i].Ellipse_Element.Minor_Radius);
        }
        writter.Write(Spare);
      }

      public C2_COP_GSP Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        Num_Of_GSPs = reader.ReadByte();
        Last_In_Cycle = (No_Yes_Boolean) reader.ReadByte();
        for (var i = 0; i < Num_Of_GSPs; i++)
        {
          GSP_Element[i].Area_Type = (GSP_Area_Type) reader.ReadByte();
          GSP_Element[i].GSP_Element_General_Data.Area_ID = reader.ReadUInt16();
          {
            byte help = reader.ReadByte();
            GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.Area_Status = (GSP_Area_Status)((help >> 0) & 1);
            GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.Area_Hostility = (GSP_Area_Hostility)((help >> 1) & 3);
            GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.SeverityOrWCOType = (byte)((help >> 3) & 3);
            GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.Spare = (byte)((help >> 5) & 7);
          }
          GSP_Element[i].GSP_Element_General_Data.Name_Length = reader.ReadByte();
          for (var j = 0; j < GSP_Element[i].GSP_Element_General_Data.Name_Length; j++)
          {
            GSP_Element[i].GSP_Element_General_Data.Name[j] = reader.ReadByte();
          }
          GSP_Element[i].GSP_Element_General_Data.Object_Type = (GSP_Object_Type) reader.ReadByte();
          GSP_Element[i].GSP_Element_General_Data.Vertices_Num = reader.ReadByte();
          for (var j = 0; j < GSP_Element[i].GSP_Element_General_Data.Vertices_Num; j++)
          {
            GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].X_Pos = reader.ReadInt32();
            GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Y_Pos = reader.ReadInt32();
            GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Z_Pos = reader.ReadInt32();
          }
          GSP_Element[i].Area_Element.SKOZ_Min_Height = reader.ReadUInt16();
          GSP_Element[i].Area_Element.SKOZ_Max_Height = reader.ReadUInt16();
          GSP_Element[i].Sector_Element.Start_Azimuth = reader.ReadUInt16();
          GSP_Element[i].Sector_Element.End_Azimuth = reader.ReadUInt16();
          GSP_Element[i].Sector_Element.Radius = reader.ReadUInt16();
          GSP_Element[i].Ellipse_Element.Azimuth = reader.ReadUInt16();
          GSP_Element[i].Ellipse_Element.Major_Radius = reader.ReadUInt16();
          GSP_Element[i].Ellipse_Element.Minor_Radius = reader.ReadUInt16();
        }
        Spare = reader.ReadUInt16();
        return this;
      }

      public override string ToString()
      {
        return "C2_COP_GSP";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_GSP ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Num_Of_GSPs = {Num_Of_GSPs}");
        if (Num_Of_GSPs < 0) writter.AppendLine("!!      Value is less than 0");
        if (Num_Of_GSPs > 7) writter.AppendLine("!!      Value is greather than 7");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Last_In_Cycle = {Last_In_Cycle}");
        if (!Enum.GetValues(typeof(No_Yes_Boolean)).OfType<No_Yes_Boolean>().Contains(Last_In_Cycle)) writter.AppendLine("!!      Value is not valid for enum No_Yes_Boolean");
        pos += 1;
        for (var i = 0; i < Num_Of_GSPs; i++)
        {
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Area_Type = {GSP_Element[i].Area_Type}");
          if (!Enum.GetValues(typeof(GSP_Area_Type)).OfType<GSP_Area_Type>().Contains(GSP_Element[i].Area_Type)) writter.AppendLine("!!      Value is not valid for enum GSP_Area_Type");
          pos += 1;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Area_ID = {GSP_Element[i].GSP_Element_General_Data.Area_ID}");
          if (GSP_Element[i].GSP_Element_General_Data.Area_ID < 1) writter.AppendLine("!!      Value is less than 1");
          if (GSP_Element[i].GSP_Element_General_Data.Area_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Area_Status_And_Hositility.Area_Status = {GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.Area_Status}");
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Area_Status_And_Hositility.Area_Hostility = {GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.Area_Hostility}");
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Area_Status_And_Hositility.SeverityOrWCOType = {GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.SeverityOrWCOType}");
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Area_Status_And_Hositility.Spare = {GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.Spare}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Name_Length = {GSP_Element[i].GSP_Element_General_Data.Name_Length}");
          if (GSP_Element[i].GSP_Element_General_Data.Name_Length < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].GSP_Element_General_Data.Name_Length > 16) writter.AppendLine("!!      Value is greather than 16");
          pos += 1;
          for (var j = 0; j < GSP_Element[i].GSP_Element_General_Data.Name_Length; j++)
          {
            writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Name[{j}] = {GSP_Element[i].GSP_Element_General_Data.Name[j]}");
            pos += 1;
          }
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Object_Type = {GSP_Element[i].GSP_Element_General_Data.Object_Type}");
          if (!Enum.GetValues(typeof(GSP_Object_Type)).OfType<GSP_Object_Type>().Contains(GSP_Element[i].GSP_Element_General_Data.Object_Type)) writter.AppendLine("!!      Value is not valid for enum GSP_Object_Type");
          pos += 1;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Vertices_Num = {GSP_Element[i].GSP_Element_General_Data.Vertices_Num}");
          if (GSP_Element[i].GSP_Element_General_Data.Vertices_Num < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].GSP_Element_General_Data.Vertices_Num > 10) writter.AppendLine("!!      Value is greather than 10");
          pos += 1;
          for (var j = 0; j < GSP_Element[i].GSP_Element_General_Data.Vertices_Num; j++)
          {
            writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Vertices_Element[{j}].X_Pos = {GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].X_Pos}");
            if (GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].X_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
            if (GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].X_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
            pos += 4;
            writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Vertices_Element[{j}].Y_Pos = {GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Y_Pos}");
            if (GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Y_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
            if (GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Y_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
            pos += 4;
            writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Vertices_Element[{j}].Z_Pos = {GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Z_Pos}");
            if (GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Z_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
            if (GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Z_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
            pos += 4;
          }
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Area_Element.SKOZ_Min_Height = {GSP_Element[i].Area_Element.SKOZ_Min_Height}");
          if (GSP_Element[i].Area_Element.SKOZ_Min_Height < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Area_Element.SKOZ_Min_Height > 20000) writter.AppendLine("!!      Value is greather than 20000");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Area_Element.SKOZ_Max_Height = {GSP_Element[i].Area_Element.SKOZ_Max_Height}");
          if (GSP_Element[i].Area_Element.SKOZ_Max_Height < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Area_Element.SKOZ_Max_Height > 20000) writter.AppendLine("!!      Value is greather than 20000");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Sector_Element.Start_Azimuth = {GSP_Element[i].Sector_Element.Start_Azimuth}");
          if (GSP_Element[i].Sector_Element.Start_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Sector_Element.Start_Azimuth > 359) writter.AppendLine("!!      Value is greather than 359");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Sector_Element.End_Azimuth = {GSP_Element[i].Sector_Element.End_Azimuth}");
          if (GSP_Element[i].Sector_Element.End_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Sector_Element.End_Azimuth > 359) writter.AppendLine("!!      Value is greather than 359");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Sector_Element.Radius = {GSP_Element[i].Sector_Element.Radius}");
          if (GSP_Element[i].Sector_Element.Radius < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Sector_Element.Radius > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Ellipse_Element.Azimuth = {GSP_Element[i].Ellipse_Element.Azimuth}");
          if (GSP_Element[i].Ellipse_Element.Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Ellipse_Element.Azimuth > 359) writter.AppendLine("!!      Value is greather than 359");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Ellipse_Element.Major_Radius = {GSP_Element[i].Ellipse_Element.Major_Radius}");
          if (GSP_Element[i].Ellipse_Element.Major_Radius < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Ellipse_Element.Major_Radius > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Ellipse_Element.Minor_Radius = {GSP_Element[i].Ellipse_Element.Minor_Radius}");
          if (GSP_Element[i].Ellipse_Element.Minor_Radius < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Ellipse_Element.Minor_Radius > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
        }
        writter.AppendLine($"{pos:X4}: Spare = {Spare}");
        if (Spare < 0) writter.AppendLine("!!      Value is less than 0");
        if (Spare > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_GSP ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Num_Of_GSPs = {Num_Of_GSPs}");
        if (Num_Of_GSPs < 0) writter.AppendLine("!!      Value is less than 0");
        if (Num_Of_GSPs > 7) writter.AppendLine("!!      Value is greather than 7");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Last_In_Cycle = {Last_In_Cycle}");
        if (!Enum.GetValues(typeof(No_Yes_Boolean)).OfType<No_Yes_Boolean>().Contains(Last_In_Cycle)) writter.AppendLine("!!      Value is not valid for enum No_Yes_Boolean");
        pos += 1;
        for (var i = 0; i < Num_Of_GSPs; i++)
        {
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Area_Type = {GSP_Element[i].Area_Type}");
          if (!Enum.GetValues(typeof(GSP_Area_Type)).OfType<GSP_Area_Type>().Contains(GSP_Element[i].Area_Type)) writter.AppendLine("!!      Value is not valid for enum GSP_Area_Type");
          pos += 1;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Area_ID = {GSP_Element[i].GSP_Element_General_Data.Area_ID}");
          if (GSP_Element[i].GSP_Element_General_Data.Area_ID < 1) writter.AppendLine("!!      Value is less than 1");
          if (GSP_Element[i].GSP_Element_General_Data.Area_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Area_Status_And_Hositility.Area_Status = {GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.Area_Status}");
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Area_Status_And_Hositility.Area_Hostility = {GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.Area_Hostility}");
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Area_Status_And_Hositility.SeverityOrWCOType = {GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.SeverityOrWCOType}");
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Area_Status_And_Hositility.Spare = {GSP_Element[i].GSP_Element_General_Data.Area_Status_And_Hositility.Spare}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Name_Length = {GSP_Element[i].GSP_Element_General_Data.Name_Length}");
          if (GSP_Element[i].GSP_Element_General_Data.Name_Length < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].GSP_Element_General_Data.Name_Length > 16) writter.AppendLine("!!      Value is greather than 16");
          pos += 1;
          for (var j = 0; j < GSP_Element[i].GSP_Element_General_Data.Name_Length; j++)
          {
            writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Name[{j}] = {GSP_Element[i].GSP_Element_General_Data.Name[j]}");
            pos += 1;
          }
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Object_Type = {GSP_Element[i].GSP_Element_General_Data.Object_Type}");
          if (!Enum.GetValues(typeof(GSP_Object_Type)).OfType<GSP_Object_Type>().Contains(GSP_Element[i].GSP_Element_General_Data.Object_Type)) writter.AppendLine("!!      Value is not valid for enum GSP_Object_Type");
          pos += 1;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Vertices_Num = {GSP_Element[i].GSP_Element_General_Data.Vertices_Num}");
          if (GSP_Element[i].GSP_Element_General_Data.Vertices_Num < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].GSP_Element_General_Data.Vertices_Num > 10) writter.AppendLine("!!      Value is greather than 10");
          pos += 1;
          for (var j = 0; j < GSP_Element[i].GSP_Element_General_Data.Vertices_Num; j++)
          {
            writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Vertices_Element[{j}].X_Pos = {GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].X_Pos}");
            if (GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].X_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
            if (GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].X_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
            pos += 4;
            writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Vertices_Element[{j}].Y_Pos = {GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Y_Pos}");
            if (GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Y_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
            if (GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Y_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
            pos += 4;
            writter.AppendLine($"{pos:X4}: GSP_Element[{i}].GSP_Element_General_Data.Vertices_Element[{j}].Z_Pos = {GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Z_Pos}");
            if (GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Z_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
            if (GSP_Element[i].GSP_Element_General_Data.Vertices_Element[j].Z_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
            pos += 4;
          }
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Area_Element.SKOZ_Min_Height = {GSP_Element[i].Area_Element.SKOZ_Min_Height}");
          if (GSP_Element[i].Area_Element.SKOZ_Min_Height < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Area_Element.SKOZ_Min_Height > 20000) writter.AppendLine("!!      Value is greather than 20000");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Area_Element.SKOZ_Max_Height = {GSP_Element[i].Area_Element.SKOZ_Max_Height}");
          if (GSP_Element[i].Area_Element.SKOZ_Max_Height < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Area_Element.SKOZ_Max_Height > 20000) writter.AppendLine("!!      Value is greather than 20000");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Sector_Element.Start_Azimuth = {GSP_Element[i].Sector_Element.Start_Azimuth}");
          if (GSP_Element[i].Sector_Element.Start_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Sector_Element.Start_Azimuth > 359) writter.AppendLine("!!      Value is greather than 359");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Sector_Element.End_Azimuth = {GSP_Element[i].Sector_Element.End_Azimuth}");
          if (GSP_Element[i].Sector_Element.End_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Sector_Element.End_Azimuth > 359) writter.AppendLine("!!      Value is greather than 359");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Sector_Element.Radius = {GSP_Element[i].Sector_Element.Radius}");
          if (GSP_Element[i].Sector_Element.Radius < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Sector_Element.Radius > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Ellipse_Element.Azimuth = {GSP_Element[i].Ellipse_Element.Azimuth}");
          if (GSP_Element[i].Ellipse_Element.Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Ellipse_Element.Azimuth > 359) writter.AppendLine("!!      Value is greather than 359");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Ellipse_Element.Major_Radius = {GSP_Element[i].Ellipse_Element.Major_Radius}");
          if (GSP_Element[i].Ellipse_Element.Major_Radius < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Ellipse_Element.Major_Radius > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: GSP_Element[{i}].Ellipse_Element.Minor_Radius = {GSP_Element[i].Ellipse_Element.Minor_Radius}");
          if (GSP_Element[i].Ellipse_Element.Minor_Radius < 0) writter.AppendLine("!!      Value is less than 0");
          if (GSP_Element[i].Ellipse_Element.Minor_Radius > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
        }
        writter.AppendLine($"{pos:X4}: Spare = {Spare}");
        if (Spare < 0) writter.AppendLine("!!      Value is less than 0");
        if (Spare > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        return writter.ToString();
      }
    }

    public class C2_COP_Engagement : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Track_ID")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort Track_ID { get; set; }

      [DisplayName("(03) - CCU_Engagement_ID")]
      [Description("Range: 0 ... 65500\u000D\u000A")]
      public ushort CCU_Engagement_ID { get; set; }

      [DisplayName("(04) - Engagement_Characteristics")]
      public Engagement_Characteristics Engagement_Characteristics { get; } = new Engagement_Characteristics();

      [DisplayName("(05) - Engagement_Status")]
      public CCU_Engagement_Status_From_ICS Engagement_Status { get; } = new CCU_Engagement_Status_From_ICS();

      [DisplayName("(06) - Freq_Allocation")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public byte Freq_Allocation { get; set; }

      [DisplayName("(07) - Channel_Allocation_Index")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte Channel_Allocation_Index { get; set; }

      [DisplayName("(08) - C2_Fire_Control1")]
      public C2_Fire_Control1 C2_Fire_Control1 { get; } = new C2_Fire_Control1();

      [DisplayName("(09) - Fire_Control2")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public Engagement_Fire_Control_2 Fire_Control2 { get; set; }

      [DisplayName("(10) - Abort_Engagement")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public eSafetyBoolean Abort_Engagement { get; set; }

      [DisplayName("(11) - CCU_Time")]
      [Description("Range: 0 ... 4294967295\u000D\u000A")]
      public uint CCU_Time { get; set; }

      [DisplayName("(12) - Magic_Word")]
      [Description("Range: 0 ... 4294967295\u000D\u000A")]
      public uint Magic_Word { get; set; }

      [DisplayName("(13) - Spare")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort Spare { get; set; }

      [DisplayName("(14) - ICP_Plan_Exists")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public eSafetyBoolean ICP_Plan_Exists { get; set; }

      [DisplayName("(15) - Number_Of_Points_In_Traj")]
      [Description("Range: 0 ... 60\u000D\u000A")]
      public byte Number_Of_Points_In_Traj { get; set; }

      [DisplayName("(16) - Trajectory_Point")]
      public ECEF_Position_And_Time[] Trajectory_Point { get; } = Enumerable.Range(0, COP_CZ_CCU_COP_ICD_V3_4.Num_Of_Points_In_Trajectory).Select(i => new ECEF_Position_And_Time()).ToArray();

      [DisplayName("(17) - C2_PIP_Position")]
      public ECEF_PIP C2_PIP_Position { get; } = new ECEF_PIP();

      public C2_COP_Engagement()
      {
        CCU_COP_Header.msg_type = 11;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(Track_ID);
        writter.Write(CCU_Engagement_ID);
        {
          byte help = 0;
          help |= (byte)(((byte)Engagement_Characteristics.Engagement_Priority & 1) << 0);
          help |= (byte)(((byte)Engagement_Characteristics.Type_Of_ICP & 3) << 1);
          help |= (byte)(((byte)Engagement_Characteristics.Locking_Policy & 1) << 3);
          help |= (byte)(((byte)Engagement_Characteristics.Engagement_Request & 3) << 4);
          help |= (byte)(((byte)Engagement_Characteristics.Spare1 & 1) << 6);
          help |= (byte)(((byte)Engagement_Characteristics.Spare2 & 1) << 7);
          writter.Write(help);
        }
        {
          byte help = 0;
          help |= (byte)(((byte)Engagement_Status.Plan_Status & 3) << 0);
          help |= (byte)(((byte)Engagement_Status.Engagement_Status & 7) << 2);
          help |= (byte)(((byte)Engagement_Status.Spare1 & 1) << 5);
          help |= (byte)(((byte)Engagement_Status.Spare2 & 1) << 6);
          help |= (byte)(((byte)Engagement_Status.Spare3 & 1) << 7);
          writter.Write(help);
        }
        writter.Write(Freq_Allocation);
        writter.Write(Channel_Allocation_Index);
        {
          byte help = 0;
          help |= (byte)(((byte)C2_Fire_Control1.Fire_Mode & 1) << 0);
          help |= (byte)(((byte)C2_Fire_Control1.wSpare & 127) << 1);
          writter.Write(help);
        }
        writter.Write((byte)Fire_Control2);
        writter.Write((byte)Abort_Engagement);
        writter.Write(CCU_Time);
        writter.Write(Magic_Word);
        writter.Write(Spare);
        writter.Write((byte)ICP_Plan_Exists);
        writter.Write(Number_Of_Points_In_Traj);
        for (var i = 0; i < Number_Of_Points_In_Traj; i++)
        {
          writter.Write(Trajectory_Point[i].X_Pos);
          writter.Write(Trajectory_Point[i].Y_Pos);
          writter.Write(Trajectory_Point[i].Z_Pos);
          writter.Write(Trajectory_Point[i].Rel_Time);
        }
        writter.Write(C2_PIP_Position.PIP_Pos_X);
        writter.Write(C2_PIP_Position.PIP_Pos_Y);
        writter.Write(C2_PIP_Position.PIP_Pos_Z);
      }

      public C2_COP_Engagement Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        Track_ID = reader.ReadUInt16();
        CCU_Engagement_ID = reader.ReadUInt16();
        {
          byte help = reader.ReadByte();
          Engagement_Characteristics.Engagement_Priority = (Engagement_Priority)((help >> 0) & 1);
          Engagement_Characteristics.Type_Of_ICP = (Type_Of_ICP)((help >> 1) & 3);
          Engagement_Characteristics.Locking_Policy = (Locking_Policy)((help >> 3) & 1);
          Engagement_Characteristics.Engagement_Request = (CCU_Engagement_Request)((help >> 4) & 3);
          Engagement_Characteristics.Spare1 = (byte)((help >> 6) & 1);
          Engagement_Characteristics.Spare2 = (byte)((help >> 7) & 1);
        }
        {
          byte help = reader.ReadByte();
          Engagement_Status.Plan_Status = (Plan_Status)((help >> 0) & 3);
          Engagement_Status.Engagement_Status = (Engagement_Status)((help >> 2) & 7);
          Engagement_Status.Spare1 = (byte)((help >> 5) & 1);
          Engagement_Status.Spare2 = (byte)((help >> 6) & 1);
          Engagement_Status.Spare3 = (byte)((help >> 7) & 1);
        }
        Freq_Allocation = reader.ReadByte();
        Channel_Allocation_Index = reader.ReadByte();
        {
          byte help = reader.ReadByte();
          C2_Fire_Control1.Fire_Mode = (Engagement_Fire_Control_1)((help >> 0) & 1);
          C2_Fire_Control1.wSpare = (byte)((help >> 1) & 127);
        }
        Fire_Control2 = (Engagement_Fire_Control_2) reader.ReadByte();
        Abort_Engagement = (eSafetyBoolean) reader.ReadByte();
        CCU_Time = reader.ReadUInt32();
        Magic_Word = reader.ReadUInt32();
        Spare = reader.ReadUInt16();
        ICP_Plan_Exists = (eSafetyBoolean) reader.ReadByte();
        Number_Of_Points_In_Traj = reader.ReadByte();
        for (var i = 0; i < Number_Of_Points_In_Traj; i++)
        {
          Trajectory_Point[i].X_Pos = reader.ReadInt32();
          Trajectory_Point[i].Y_Pos = reader.ReadInt32();
          Trajectory_Point[i].Z_Pos = reader.ReadInt32();
          Trajectory_Point[i].Rel_Time = reader.ReadUInt32();
        }
        C2_PIP_Position.PIP_Pos_X = reader.ReadInt32();
        C2_PIP_Position.PIP_Pos_Y = reader.ReadInt32();
        C2_PIP_Position.PIP_Pos_Z = reader.ReadInt32();
        return this;
      }

      public override string ToString()
      {
        return "C2_COP_Engagement";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Engagement ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Track_ID = {Track_ID}");
        if (Track_ID < 0) writter.AppendLine("!!      Value is less than 0");
        if (Track_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_Engagement_ID = {CCU_Engagement_ID}");
        if (CCU_Engagement_ID < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_Engagement_ID > 65500) writter.AppendLine("!!      Value is greather than 65500");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Engagement_Characteristics.Engagement_Priority = {Engagement_Characteristics.Engagement_Priority}");
        writter.AppendLine($"{pos:X4}: Engagement_Characteristics.Type_Of_ICP = {Engagement_Characteristics.Type_Of_ICP}");
        writter.AppendLine($"{pos:X4}: Engagement_Characteristics.Locking_Policy = {Engagement_Characteristics.Locking_Policy}");
        writter.AppendLine($"{pos:X4}: Engagement_Characteristics.Engagement_Request = {Engagement_Characteristics.Engagement_Request}");
        writter.AppendLine($"{pos:X4}: Engagement_Characteristics.Spare1 = {Engagement_Characteristics.Spare1}");
        writter.AppendLine($"{pos:X4}: Engagement_Characteristics.Spare2 = {Engagement_Characteristics.Spare2}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Engagement_Status.Plan_Status = {Engagement_Status.Plan_Status}");
        writter.AppendLine($"{pos:X4}: Engagement_Status.Engagement_Status = {Engagement_Status.Engagement_Status}");
        writter.AppendLine($"{pos:X4}: Engagement_Status.Spare1 = {Engagement_Status.Spare1}");
        writter.AppendLine($"{pos:X4}: Engagement_Status.Spare2 = {Engagement_Status.Spare2}");
        writter.AppendLine($"{pos:X4}: Engagement_Status.Spare3 = {Engagement_Status.Spare3}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Freq_Allocation = {Freq_Allocation}");
        if (Freq_Allocation < 0) writter.AppendLine("!!      Value is less than 0");
        if (Freq_Allocation > 3) writter.AppendLine("!!      Value is greather than 3");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Channel_Allocation_Index = {Channel_Allocation_Index}");
        if (Channel_Allocation_Index < 0) writter.AppendLine("!!      Value is less than 0");
        if (Channel_Allocation_Index > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: C2_Fire_Control1.Fire_Mode = {C2_Fire_Control1.Fire_Mode}");
        writter.AppendLine($"{pos:X4}: C2_Fire_Control1.wSpare = {C2_Fire_Control1.wSpare}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Fire_Control2 = {Fire_Control2}");
        if (!Enum.GetValues(typeof(Engagement_Fire_Control_2)).OfType<Engagement_Fire_Control_2>().Contains(Fire_Control2)) writter.AppendLine("!!      Value is not valid for enum Engagement_Fire_Control_2");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Abort_Engagement = {Abort_Engagement}");
        if (!Enum.GetValues(typeof(eSafetyBoolean)).OfType<eSafetyBoolean>().Contains(Abort_Engagement)) writter.AppendLine("!!      Value is not valid for enum eSafetyBoolean");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_Time = {CCU_Time}");
        if (CCU_Time < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_Time > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Magic_Word = {Magic_Word}");
        if (Magic_Word < 0) writter.AppendLine("!!      Value is less than 0");
        if (Magic_Word > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Spare = {Spare}");
        if (Spare < 0) writter.AppendLine("!!      Value is less than 0");
        if (Spare > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: ICP_Plan_Exists = {ICP_Plan_Exists}");
        if (!Enum.GetValues(typeof(eSafetyBoolean)).OfType<eSafetyBoolean>().Contains(ICP_Plan_Exists)) writter.AppendLine("!!      Value is not valid for enum eSafetyBoolean");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Number_Of_Points_In_Traj = {Number_Of_Points_In_Traj}");
        if (Number_Of_Points_In_Traj < 0) writter.AppendLine("!!      Value is less than 0");
        if (Number_Of_Points_In_Traj > 60) writter.AppendLine("!!      Value is greather than 60");
        pos += 1;
        for (var i = 0; i < Number_Of_Points_In_Traj; i++)
        {
          writter.AppendLine($"{pos:X4}: Trajectory_Point[{i}].X_Pos = {Trajectory_Point[i].X_Pos}");
          if (Trajectory_Point[i].X_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Trajectory_Point[i].X_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Trajectory_Point[{i}].Y_Pos = {Trajectory_Point[i].Y_Pos}");
          if (Trajectory_Point[i].Y_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Trajectory_Point[i].Y_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Trajectory_Point[{i}].Z_Pos = {Trajectory_Point[i].Z_Pos}");
          if (Trajectory_Point[i].Z_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Trajectory_Point[i].Z_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Trajectory_Point[{i}].Rel_Time = {Trajectory_Point[i].Rel_Time}");
          if (Trajectory_Point[i].Rel_Time < 0) writter.AppendLine("!!      Value is less than 0");
          if (Trajectory_Point[i].Rel_Time > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
          pos += 4;
        }
        writter.AppendLine($"{pos:X4}: C2_PIP_Position.PIP_Pos_X = {C2_PIP_Position.PIP_Pos_X}");
        if (C2_PIP_Position.PIP_Pos_X < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (C2_PIP_Position.PIP_Pos_X > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: C2_PIP_Position.PIP_Pos_Y = {C2_PIP_Position.PIP_Pos_Y}");
        if (C2_PIP_Position.PIP_Pos_Y < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (C2_PIP_Position.PIP_Pos_Y > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: C2_PIP_Position.PIP_Pos_Z = {C2_PIP_Position.PIP_Pos_Z}");
        if (C2_PIP_Position.PIP_Pos_Z < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (C2_PIP_Position.PIP_Pos_Z > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Engagement ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Track_ID = {Track_ID}");
        if (Track_ID < 0) writter.AppendLine("!!      Value is less than 0");
        if (Track_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_Engagement_ID = {CCU_Engagement_ID}");
        if (CCU_Engagement_ID < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_Engagement_ID > 65500) writter.AppendLine("!!      Value is greather than 65500");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Engagement_Characteristics.Engagement_Priority = {Engagement_Characteristics.Engagement_Priority}");
        writter.AppendLine($"{pos:X4}: Engagement_Characteristics.Type_Of_ICP = {Engagement_Characteristics.Type_Of_ICP}");
        writter.AppendLine($"{pos:X4}: Engagement_Characteristics.Locking_Policy = {Engagement_Characteristics.Locking_Policy}");
        writter.AppendLine($"{pos:X4}: Engagement_Characteristics.Engagement_Request = {Engagement_Characteristics.Engagement_Request}");
        writter.AppendLine($"{pos:X4}: Engagement_Characteristics.Spare1 = {Engagement_Characteristics.Spare1}");
        writter.AppendLine($"{pos:X4}: Engagement_Characteristics.Spare2 = {Engagement_Characteristics.Spare2}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Engagement_Status.Plan_Status = {Engagement_Status.Plan_Status}");
        writter.AppendLine($"{pos:X4}: Engagement_Status.Engagement_Status = {Engagement_Status.Engagement_Status}");
        writter.AppendLine($"{pos:X4}: Engagement_Status.Spare1 = {Engagement_Status.Spare1}");
        writter.AppendLine($"{pos:X4}: Engagement_Status.Spare2 = {Engagement_Status.Spare2}");
        writter.AppendLine($"{pos:X4}: Engagement_Status.Spare3 = {Engagement_Status.Spare3}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Freq_Allocation = {Freq_Allocation}");
        if (Freq_Allocation < 0) writter.AppendLine("!!      Value is less than 0");
        if (Freq_Allocation > 3) writter.AppendLine("!!      Value is greather than 3");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Channel_Allocation_Index = {Channel_Allocation_Index}");
        if (Channel_Allocation_Index < 0) writter.AppendLine("!!      Value is less than 0");
        if (Channel_Allocation_Index > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: C2_Fire_Control1.Fire_Mode = {C2_Fire_Control1.Fire_Mode}");
        writter.AppendLine($"{pos:X4}: C2_Fire_Control1.wSpare = {C2_Fire_Control1.wSpare}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Fire_Control2 = {Fire_Control2}");
        if (!Enum.GetValues(typeof(Engagement_Fire_Control_2)).OfType<Engagement_Fire_Control_2>().Contains(Fire_Control2)) writter.AppendLine("!!      Value is not valid for enum Engagement_Fire_Control_2");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Abort_Engagement = {Abort_Engagement}");
        if (!Enum.GetValues(typeof(eSafetyBoolean)).OfType<eSafetyBoolean>().Contains(Abort_Engagement)) writter.AppendLine("!!      Value is not valid for enum eSafetyBoolean");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_Time = {CCU_Time}");
        if (CCU_Time < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_Time > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Magic_Word = {Magic_Word}");
        if (Magic_Word < 0) writter.AppendLine("!!      Value is less than 0");
        if (Magic_Word > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Spare = {Spare}");
        if (Spare < 0) writter.AppendLine("!!      Value is less than 0");
        if (Spare > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: ICP_Plan_Exists = {ICP_Plan_Exists}");
        if (!Enum.GetValues(typeof(eSafetyBoolean)).OfType<eSafetyBoolean>().Contains(ICP_Plan_Exists)) writter.AppendLine("!!      Value is not valid for enum eSafetyBoolean");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Number_Of_Points_In_Traj = {Number_Of_Points_In_Traj}");
        if (Number_Of_Points_In_Traj < 0) writter.AppendLine("!!      Value is less than 0");
        if (Number_Of_Points_In_Traj > 60) writter.AppendLine("!!      Value is greather than 60");
        pos += 1;
        for (var i = 0; i < Number_Of_Points_In_Traj; i++)
        {
          writter.AppendLine($"{pos:X4}: Trajectory_Point[{i}].X_Pos = {Trajectory_Point[i].X_Pos}");
          if (Trajectory_Point[i].X_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Trajectory_Point[i].X_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Trajectory_Point[{i}].Y_Pos = {Trajectory_Point[i].Y_Pos}");
          if (Trajectory_Point[i].Y_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Trajectory_Point[i].Y_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Trajectory_Point[{i}].Z_Pos = {Trajectory_Point[i].Z_Pos}");
          if (Trajectory_Point[i].Z_Pos < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Trajectory_Point[i].Z_Pos > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Trajectory_Point[{i}].Rel_Time = {Trajectory_Point[i].Rel_Time}");
          if (Trajectory_Point[i].Rel_Time < 0) writter.AppendLine("!!      Value is less than 0");
          if (Trajectory_Point[i].Rel_Time > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
          pos += 4;
        }
        writter.AppendLine($"{pos:X4}: C2_PIP_Position.PIP_Pos_X = {C2_PIP_Position.PIP_Pos_X}");
        if (C2_PIP_Position.PIP_Pos_X < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (C2_PIP_Position.PIP_Pos_X > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: C2_PIP_Position.PIP_Pos_Y = {C2_PIP_Position.PIP_Pos_Y}");
        if (C2_PIP_Position.PIP_Pos_Y < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (C2_PIP_Position.PIP_Pos_Y > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: C2_PIP_Position.PIP_Pos_Z = {C2_PIP_Position.PIP_Pos_Z}");
        if (C2_PIP_Position.PIP_Pos_Z < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (C2_PIP_Position.PIP_Pos_Z > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        return writter.ToString();
      }
    }

    public class C2_COP_Chat : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Recipients")]
      public Recipients Recipients { get; } = new Recipients();

      [DisplayName("(03) - Text_Length")]
      [Description("Range: 1 ... 160\u000D\u000A")]
      public byte Text_Length { get; set; }

      [DisplayName("(04) - Chat_Text")]
      [Description("Range: -128 ... 127\u000D\u000A")]
      public byte[] Chat_Text { get; } = new byte[Num_Of_Chars_In_Chat];

      [DisplayName("(04-A) - Chat_Text")]
      public string Chat_Text_Text
      {
        get
        {
          return UTF8Encoding.UTF8
            .GetString(Chat_Text, 0, Text_Length);
        }
        set
        {
          var data = UTF8Encoding.UTF8.GetBytes(value);
          var length = Math.Min(data.Length, Chat_Text.Length);
          
          Array.Clear(Chat_Text, 0, Chat_Text.Length);
          Array.Copy(data, 0, Chat_Text, 0, length);
          Text_Length = (byte)length;
        }
      }

      public C2_COP_Chat()
      {
        CCU_COP_Header.msg_type = 10;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        {
          byte help = 0;
          help |= (byte)(((byte)Recipients.MFU1 & 1) << 0);
          help |= (byte)(((byte)Recipients.MFU2 & 1) << 1);
          help |= (byte)(((byte)Recipients.MFU3 & 1) << 2);
          help |= (byte)(((byte)Recipients.MFU4 & 1) << 3);
          help |= (byte)(((byte)Recipients.MFU5 & 1) << 4);
          help |= (byte)(((byte)Recipients.MFU6 & 1) << 5);
          help |= (byte)(((byte)Recipients.Spare & 3) << 6);
          writter.Write(help);
        }
        writter.Write(Text_Length);
        for (var i = 0; i < Text_Length; i++)
        {
          writter.Write(Chat_Text[i]);
        }
      }

      public C2_COP_Chat Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        {
          byte help = reader.ReadByte();
          Recipients.MFU1 = (byte)((help >> 0) & 1);
          Recipients.MFU2 = (byte)((help >> 1) & 1);
          Recipients.MFU3 = (byte)((help >> 2) & 1);
          Recipients.MFU4 = (byte)((help >> 3) & 1);
          Recipients.MFU5 = (byte)((help >> 4) & 1);
          Recipients.MFU6 = (byte)((help >> 5) & 1);
          Recipients.Spare = (byte)((help >> 6) & 3);
        }
        Text_Length = reader.ReadByte();
        for (var i = 0; i < Text_Length; i++)
        {
          Chat_Text[i] = reader.ReadByte();
        }
        return this;
      }

      public override string ToString()
      {
        return "C2_COP_Chat";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Chat ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Recipients.MFU1 = {Recipients.MFU1}");
        writter.AppendLine($"{pos:X4}: Recipients.MFU2 = {Recipients.MFU2}");
        writter.AppendLine($"{pos:X4}: Recipients.MFU3 = {Recipients.MFU3}");
        writter.AppendLine($"{pos:X4}: Recipients.MFU4 = {Recipients.MFU4}");
        writter.AppendLine($"{pos:X4}: Recipients.MFU5 = {Recipients.MFU5}");
        writter.AppendLine($"{pos:X4}: Recipients.MFU6 = {Recipients.MFU6}");
        writter.AppendLine($"{pos:X4}: Recipients.Spare = {Recipients.Spare}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Text_Length = {Text_Length}");
        if (Text_Length < 1) writter.AppendLine("!!      Value is less than 1");
        if (Text_Length > 160) writter.AppendLine("!!      Value is greather than 160");
        pos += 1;
        for (var i = 0; i < Text_Length; i++)
        {
          writter.AppendLine($"{pos:X4}: Chat_Text[{i}] = {Chat_Text[i]}");
          pos += 1;
        }
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Chat ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Recipients.MFU1 = {Recipients.MFU1}");
        writter.AppendLine($"{pos:X4}: Recipients.MFU2 = {Recipients.MFU2}");
        writter.AppendLine($"{pos:X4}: Recipients.MFU3 = {Recipients.MFU3}");
        writter.AppendLine($"{pos:X4}: Recipients.MFU4 = {Recipients.MFU4}");
        writter.AppendLine($"{pos:X4}: Recipients.MFU5 = {Recipients.MFU5}");
        writter.AppendLine($"{pos:X4}: Recipients.MFU6 = {Recipients.MFU6}");
        writter.AppendLine($"{pos:X4}: Recipients.Spare = {Recipients.Spare}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Text_Length = {Text_Length}");
        if (Text_Length < 1) writter.AppendLine("!!      Value is less than 1");
        if (Text_Length > 160) writter.AppendLine("!!      Value is greather than 160");
        pos += 1;
        for (var i = 0; i < Text_Length; i++)
        {
          writter.AppendLine($"{pos:X4}: Chat_Text[{i}] = {Chat_Text[i]}");
          pos += 1;
        }
        return writter.ToString();
      }
    }

    public class C2_COP_Pointer : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Text_Length")]
      [Description("Range: 0 ... 31\u000D\u000A")]
      public byte Text_Length { get; set; }

      [DisplayName("(03) - Text")]
      [Description("Range: -128 ... 127\u000D\u000A")]
      public byte[] Text { get; } = new byte[Num_Of_Chars_In_Pointer];

      [DisplayName("(03-A) - Text")]
      public string Text_Text
      {
        get
        {
          return UTF8Encoding.UTF8
            .GetString(Text, 0, Text_Length);
        }
        set
        {
          var data = UTF8Encoding.UTF8.GetBytes(value);
          var length = Math.Min(data.Length, Text.Length);
          
          Array.Clear(Text, 0, Text.Length);
          Array.Copy(data, 0, Text, 0, length);
          Text_Length = (byte)length;
        }
      }

      [DisplayName("(04) - Latitude")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Latitude { get; set; }

      [DisplayName("(05) - Longitude")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Longitude { get; set; }

      public C2_COP_Pointer()
      {
        CCU_COP_Header.msg_type = 12;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(Text_Length);
        for (var i = 0; i < Text_Length; i++)
        {
          writter.Write(Text[i]);
        }
        writter.Write(Latitude);
        writter.Write(Longitude);
      }

      public C2_COP_Pointer Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        Text_Length = reader.ReadByte();
        for (var i = 0; i < Text_Length; i++)
        {
          Text[i] = reader.ReadByte();
        }
        Latitude = reader.ReadSingle();
        Longitude = reader.ReadSingle();
        return this;
      }

      public override string ToString()
      {
        return "C2_COP_Pointer";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Pointer ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Text_Length = {Text_Length}");
        if (Text_Length < 0) writter.AppendLine("!!      Value is less than 0");
        if (Text_Length > 31) writter.AppendLine("!!      Value is greather than 31");
        pos += 1;
        for (var i = 0; i < Text_Length; i++)
        {
          writter.AppendLine($"{pos:X4}: Text[{i}] = {Text[i]}");
          pos += 1;
        }
        writter.AppendLine($"{pos:X4}: Latitude = {Latitude}");
        if (Latitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Latitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Longitude = {Longitude}");
        if (Longitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Longitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Pointer ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Text_Length = {Text_Length}");
        if (Text_Length < 0) writter.AppendLine("!!      Value is less than 0");
        if (Text_Length > 31) writter.AppendLine("!!      Value is greather than 31");
        pos += 1;
        for (var i = 0; i < Text_Length; i++)
        {
          writter.AppendLine($"{pos:X4}: Text[{i}] = {Text[i]}");
          pos += 1;
        }
        writter.AppendLine($"{pos:X4}: Latitude = {Latitude}");
        if (Latitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Latitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Longitude = {Longitude}");
        if (Longitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Longitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        return writter.ToString();
      }
    }

    public class C2_COP_Msls_Location_From_Rdr : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Data")]
      [Description("Data")]
      public SMslsLocationFromRdrData Data { get; } = new SMslsLocationFromRdrData();

      public C2_COP_Msls_Location_From_Rdr()
      {
        CCU_COP_Header.msg_type = 7;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(Data.q_NumOfMslInAir);
        for (var i = 0; i < Data.q_NumOfMslInAir; i++)
        {
          writter.Write(Data.q_MslInAir[i].q_MslID);
          writter.Write(Data.q_MslInAir[i].q_Engagement1ID);
          writter.Write(Data.q_MslInAir[i].q_RDR_Index);
          writter.Write(Data.q_MslInAir[i]._q_RdrTimeFromMidnight);
          writter.Write(Data.q_MslInAir[i].q_RdrTimeYear);
          writter.Write(Data.q_MslInAir[i].q_RdrTimeDayInAYear);
          writter.Write(Data.q_MslInAir[i].q_RdrAzimuthToMsl1);
          writter.Write(Data.q_MslInAir[i].q_RdrElevationToMsl1);
          writter.Write(Data.q_MslInAir[i].q_RdrRangeToMsl1);
          writter.Write(Data.q_MslInAir[i].q_RdrMsl1Status);
          writter.Write(Data.q_MslInAir[i].q_RdrCovX);
          writter.Write(Data.q_MslInAir[i].q_RdrCovY);
          writter.Write(Data.q_MslInAir[i].q_RdrCovZ);
          writter.Write(Data.q_MslInAir[i].q_spare);
        }
      }

      public C2_COP_Msls_Location_From_Rdr Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        Data.q_NumOfMslInAir = reader.ReadByte();
        for (var i = 0; i < Data.q_NumOfMslInAir; i++)
        {
          Data.q_MslInAir[i].q_MslID = reader.ReadByte();
          Data.q_MslInAir[i].q_Engagement1ID = reader.ReadUInt16();
          Data.q_MslInAir[i].q_RDR_Index = reader.ReadByte();
          Data.q_MslInAir[i]._q_RdrTimeFromMidnight = reader.ReadUInt32();
          Data.q_MslInAir[i].q_RdrTimeYear = reader.ReadUInt16();
          Data.q_MslInAir[i].q_RdrTimeDayInAYear = reader.ReadUInt16();
          Data.q_MslInAir[i].q_RdrAzimuthToMsl1 = reader.ReadInt16();
          Data.q_MslInAir[i].q_RdrElevationToMsl1 = reader.ReadInt16();
          Data.q_MslInAir[i].q_RdrRangeToMsl1 = reader.ReadInt16();
          Data.q_MslInAir[i].q_RdrMsl1Status = reader.ReadInt16();
          Data.q_MslInAir[i].q_RdrCovX = reader.ReadByte();
          Data.q_MslInAir[i].q_RdrCovY = reader.ReadByte();
          Data.q_MslInAir[i].q_RdrCovZ = reader.ReadByte();
          Data.q_MslInAir[i].q_spare = reader.ReadByte();
        }
        return this;
      }

      public override string ToString()
      {
        return "C2_COP_Msls_Location_From_Rdr";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Msls_Location_From_Rdr ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Data.q_NumOfMslInAir = {Data.q_NumOfMslInAir}");
        if (Data.q_NumOfMslInAir < 0) writter.AppendLine("!!      Value is less than 0");
        if (Data.q_NumOfMslInAir > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        for (var i = 0; i < Data.q_NumOfMslInAir; i++)
        {
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslID = {Data.q_MslInAir[i].q_MslID}");
          if (Data.q_MslInAir[i].q_MslID < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslID > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_Engagement1ID = {Data.q_MslInAir[i].q_Engagement1ID}");
          if (Data.q_MslInAir[i].q_Engagement1ID < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_Engagement1ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RDR_Index = {Data.q_MslInAir[i].q_RDR_Index}");
          if (Data.q_MslInAir[i].q_RDR_Index < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_RDR_Index > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrTimeFromMidnight = {Data.q_MslInAir[i].q_RdrTimeFromMidnight}");
          if (Data.q_MslInAir[i]._q_RdrTimeFromMidnight < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i]._q_RdrTimeFromMidnight > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrTimeYear = {Data.q_MslInAir[i].q_RdrTimeYear}");
          if (Data.q_MslInAir[i].q_RdrTimeYear < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_RdrTimeYear > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrTimeDayInAYear = {Data.q_MslInAir[i].q_RdrTimeDayInAYear}");
          if (Data.q_MslInAir[i].q_RdrTimeDayInAYear < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_RdrTimeDayInAYear > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrAzimuthToMsl1 = {Data.q_MslInAir[i].q_RdrAzimuthToMsl1}");
          if (Data.q_MslInAir[i].q_RdrAzimuthToMsl1 < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_RdrAzimuthToMsl1 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrElevationToMsl1 = {Data.q_MslInAir[i].q_RdrElevationToMsl1}");
          if (Data.q_MslInAir[i].q_RdrElevationToMsl1 < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_RdrElevationToMsl1 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrRangeToMsl1 = {Data.q_MslInAir[i].q_RdrRangeToMsl1}");
          if (Data.q_MslInAir[i].q_RdrRangeToMsl1 < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_RdrRangeToMsl1 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrMsl1Status = {Data.q_MslInAir[i].q_RdrMsl1Status}");
          if (Data.q_MslInAir[i].q_RdrMsl1Status < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_RdrMsl1Status > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrCovX = {Data.q_MslInAir[i].q_RdrCovX}");
          if (Data.q_MslInAir[i].q_RdrCovX < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_RdrCovX > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrCovY = {Data.q_MslInAir[i].q_RdrCovY}");
          if (Data.q_MslInAir[i].q_RdrCovY < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_RdrCovY > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrCovZ = {Data.q_MslInAir[i].q_RdrCovZ}");
          if (Data.q_MslInAir[i].q_RdrCovZ < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_RdrCovZ > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_spare = {Data.q_MslInAir[i].q_spare}");
          if (Data.q_MslInAir[i].q_spare < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_spare > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
        }
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Msls_Location_From_Rdr ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Data.q_NumOfMslInAir = {Data.q_NumOfMslInAir}");
        if (Data.q_NumOfMslInAir < 0) writter.AppendLine("!!      Value is less than 0");
        if (Data.q_NumOfMslInAir > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        for (var i = 0; i < Data.q_NumOfMslInAir; i++)
        {
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslID = {Data.q_MslInAir[i].q_MslID}");
          if (Data.q_MslInAir[i].q_MslID < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslID > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_Engagement1ID = {Data.q_MslInAir[i].q_Engagement1ID}");
          if (Data.q_MslInAir[i].q_Engagement1ID < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_Engagement1ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RDR_Index = {Data.q_MslInAir[i].q_RDR_Index}");
          if (Data.q_MslInAir[i].q_RDR_Index < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_RDR_Index > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrTimeFromMidnight = {Data.q_MslInAir[i].q_RdrTimeFromMidnight}");
          if (Data.q_MslInAir[i]._q_RdrTimeFromMidnight < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i]._q_RdrTimeFromMidnight > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrTimeYear = {Data.q_MslInAir[i].q_RdrTimeYear}");
          if (Data.q_MslInAir[i].q_RdrTimeYear < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_RdrTimeYear > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrTimeDayInAYear = {Data.q_MslInAir[i].q_RdrTimeDayInAYear}");
          if (Data.q_MslInAir[i].q_RdrTimeDayInAYear < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_RdrTimeDayInAYear > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrAzimuthToMsl1 = {Data.q_MslInAir[i].q_RdrAzimuthToMsl1}");
          if (Data.q_MslInAir[i].q_RdrAzimuthToMsl1 < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_RdrAzimuthToMsl1 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrElevationToMsl1 = {Data.q_MslInAir[i].q_RdrElevationToMsl1}");
          if (Data.q_MslInAir[i].q_RdrElevationToMsl1 < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_RdrElevationToMsl1 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrRangeToMsl1 = {Data.q_MslInAir[i].q_RdrRangeToMsl1}");
          if (Data.q_MslInAir[i].q_RdrRangeToMsl1 < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_RdrRangeToMsl1 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrMsl1Status = {Data.q_MslInAir[i].q_RdrMsl1Status}");
          if (Data.q_MslInAir[i].q_RdrMsl1Status < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_RdrMsl1Status > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrCovX = {Data.q_MslInAir[i].q_RdrCovX}");
          if (Data.q_MslInAir[i].q_RdrCovX < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_RdrCovX > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrCovY = {Data.q_MslInAir[i].q_RdrCovY}");
          if (Data.q_MslInAir[i].q_RdrCovY < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_RdrCovY > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_RdrCovZ = {Data.q_MslInAir[i].q_RdrCovZ}");
          if (Data.q_MslInAir[i].q_RdrCovZ < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_RdrCovZ > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_spare = {Data.q_MslInAir[i].q_spare}");
          if (Data.q_MslInAir[i].q_spare < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_spare > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
        }
        return writter.ToString();
      }
    }

    public class C2_COP_Training_Failures : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Training_Failures")]
      public Training_Failures Training_Failures { get; } = new Training_Failures();

      public C2_COP_Training_Failures()
      {
        CCU_COP_Header.msg_type = 113;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(Training_Failures.Sim_Python_BIT);
        writter.Write(Training_Failures.Sim_Derby_BIT);
        writter.Write(Training_Failures.Sim_ER_LR_BIT);
        writter.Write(Training_Failures.Dlu_Fail);
        writter.Write(Training_Failures.Launch_Fail);
        writter.Write(Training_Failures.Mis_Fire);
        writter.Write(Training_Failures.Seeker_Does_Not_Lock);
      }

      public C2_COP_Training_Failures Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        Training_Failures.Sim_Python_BIT = reader.ReadByte();
        Training_Failures.Sim_Derby_BIT = reader.ReadByte();
        Training_Failures.Sim_ER_LR_BIT = reader.ReadByte();
        Training_Failures.Dlu_Fail = reader.ReadByte();
        Training_Failures.Launch_Fail = reader.ReadByte();
        Training_Failures.Mis_Fire = reader.ReadByte();
        Training_Failures.Seeker_Does_Not_Lock = reader.ReadByte();
        return this;
      }

      public override string ToString()
      {
        return "C2_COP_Training_Failures";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Training_Failures ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Training_Failures.Sim_Python_BIT = {Training_Failures.Sim_Python_BIT}");
        if (Training_Failures.Sim_Python_BIT < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Sim_Python_BIT > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Failures.Sim_Derby_BIT = {Training_Failures.Sim_Derby_BIT}");
        if (Training_Failures.Sim_Derby_BIT < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Sim_Derby_BIT > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Failures.Sim_ER_LR_BIT = {Training_Failures.Sim_ER_LR_BIT}");
        if (Training_Failures.Sim_ER_LR_BIT < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Sim_ER_LR_BIT > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Failures.Dlu_Fail = {Training_Failures.Dlu_Fail}");
        if (Training_Failures.Dlu_Fail < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Dlu_Fail > 1) writter.AppendLine("!!      Value is greather than 1");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Failures.Launch_Fail = {Training_Failures.Launch_Fail}");
        if (Training_Failures.Launch_Fail < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Launch_Fail > 1) writter.AppendLine("!!      Value is greather than 1");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Failures.Mis_Fire = {Training_Failures.Mis_Fire}");
        if (Training_Failures.Mis_Fire < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Mis_Fire > 1) writter.AppendLine("!!      Value is greather than 1");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Failures.Seeker_Does_Not_Lock = {Training_Failures.Seeker_Does_Not_Lock}");
        if (Training_Failures.Seeker_Does_Not_Lock < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Seeker_Does_Not_Lock > 1) writter.AppendLine("!!      Value is greather than 1");
        pos += 1;
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Training_Failures ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Training_Failures.Sim_Python_BIT = {Training_Failures.Sim_Python_BIT}");
        if (Training_Failures.Sim_Python_BIT < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Sim_Python_BIT > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Failures.Sim_Derby_BIT = {Training_Failures.Sim_Derby_BIT}");
        if (Training_Failures.Sim_Derby_BIT < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Sim_Derby_BIT > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Failures.Sim_ER_LR_BIT = {Training_Failures.Sim_ER_LR_BIT}");
        if (Training_Failures.Sim_ER_LR_BIT < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Sim_ER_LR_BIT > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Failures.Dlu_Fail = {Training_Failures.Dlu_Fail}");
        if (Training_Failures.Dlu_Fail < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Dlu_Fail > 1) writter.AppendLine("!!      Value is greather than 1");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Failures.Launch_Fail = {Training_Failures.Launch_Fail}");
        if (Training_Failures.Launch_Fail < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Launch_Fail > 1) writter.AppendLine("!!      Value is greather than 1");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Failures.Mis_Fire = {Training_Failures.Mis_Fire}");
        if (Training_Failures.Mis_Fire < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Mis_Fire > 1) writter.AppendLine("!!      Value is greather than 1");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Failures.Seeker_Does_Not_Lock = {Training_Failures.Seeker_Does_Not_Lock}");
        if (Training_Failures.Seeker_Does_Not_Lock < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Failures.Seeker_Does_Not_Lock > 1) writter.AppendLine("!!      Value is greather than 1");
        pos += 1;
        return writter.ToString();
      }
    }

    public class C2_COP_Training_Settings : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Training_Settings_Data")]
      public Training_Settings_Data Training_Settings_Data { get; } = new Training_Settings_Data();

      public C2_COP_Training_Settings()
      {
        CCU_COP_Header.msg_type = 114;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(Training_Settings_Data.Sim_Derby_To_Load);
        writter.Write(Training_Settings_Data.Sim_Python_To_Load);
        writter.Write(Training_Settings_Data.Sim_ER_LR_To_Load);
      }

      public C2_COP_Training_Settings Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        Training_Settings_Data.Sim_Derby_To_Load = reader.ReadByte();
        Training_Settings_Data.Sim_Python_To_Load = reader.ReadByte();
        Training_Settings_Data.Sim_ER_LR_To_Load = reader.ReadByte();
        return this;
      }

      public override string ToString()
      {
        return "C2_COP_Training_Settings";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Training_Settings ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Training_Settings_Data.Sim_Derby_To_Load = {Training_Settings_Data.Sim_Derby_To_Load}");
        if (Training_Settings_Data.Sim_Derby_To_Load < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Settings_Data.Sim_Derby_To_Load > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Settings_Data.Sim_Python_To_Load = {Training_Settings_Data.Sim_Python_To_Load}");
        if (Training_Settings_Data.Sim_Python_To_Load < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Settings_Data.Sim_Python_To_Load > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Settings_Data.Sim_ER_LR_To_Load = {Training_Settings_Data.Sim_ER_LR_To_Load}");
        if (Training_Settings_Data.Sim_ER_LR_To_Load < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Settings_Data.Sim_ER_LR_To_Load > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== C2_COP_Training_Settings ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Training_Settings_Data.Sim_Derby_To_Load = {Training_Settings_Data.Sim_Derby_To_Load}");
        if (Training_Settings_Data.Sim_Derby_To_Load < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Settings_Data.Sim_Derby_To_Load > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Settings_Data.Sim_Python_To_Load = {Training_Settings_Data.Sim_Python_To_Load}");
        if (Training_Settings_Data.Sim_Python_To_Load < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Settings_Data.Sim_Python_To_Load > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Training_Settings_Data.Sim_ER_LR_To_Load = {Training_Settings_Data.Sim_ER_LR_To_Load}");
        if (Training_Settings_Data.Sim_ER_LR_To_Load < 0) writter.AppendLine("!!      Value is less than 0");
        if (Training_Settings_Data.Sim_ER_LR_To_Load > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        return writter.ToString();
      }
    }

    public class COP_C2_Status : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - MCU_Serial_ID")]
      [Description("Range: 0 ... 255\u000D\u000AMCU physical  ID (Tail number)")]
      public byte MCU_Serial_ID { get; set; }

      [DisplayName("(03) - COP_State")]
      public COP_State COP_State { get; } = new COP_State();

      [DisplayName("(04) - LM_Readiness")]
      public LM_Readiness LM_Readiness { get; } = new LM_Readiness();

      [DisplayName("(05) - MCU_Not_Ready_Reason")]
      public MCU_Not_Ready_Reason MCU_Not_Ready_Reason { get; } = new MCU_Not_Ready_Reason();

      [DisplayName("(06) - COP_Not_Ready_Reason")]
      public COP_Not_Ready_Reason COP_Not_Ready_Reason { get; } = new COP_Not_Ready_Reason();

      [DisplayName("(07) - Network_Size")]
      [Description("Range: 1 ... 255\u000D\u000ADistinct for every COP, can be set by COP operator")]
      public byte Network_Size { get; set; }

      [DisplayName("(08) - Missiles_Available_For_Fire")]
      public COP_Missiles_Available_For_Fire Missiles_Available_For_Fire { get; } = new COP_Missiles_Available_For_Fire();

      [DisplayName("(09) - Total_Missiles")]
      public COP_Total_Missiles Total_Missiles { get; } = new COP_Total_Missiles();

      [DisplayName("(10) - Turret_Azimuth")]
      [Description("Range: 0 ... 6399\u000D\u000AAzimuth of the turret Relative to vehicle axis. Measured by the turret Resolver\u000D\u000ADirection: CW convention.")]
      public ushort Turret_Azimuth { get; set; }

      [DisplayName("(11) - Cabin_Azimuth")]
      [Description("Range: 0 ... 4294967295\u000D\u000AScaleFactor: 2.98023223876953E-06\u000D\u000A")]
      public double Cabin_Azimuth {
        get =>  _Cabin_Azimuth * 2.98023223876953E-06;
        set => _Cabin_Azimuth = (uint)Math.Round(value / 2.98023223876953E-06);
      }
      internal uint _Cabin_Azimuth;

      [DisplayName("(12) - Launcher_Position")]
      public LLA_Position Launcher_Position { get; } = new LLA_Position();

      [DisplayName("(13) - Number_Of_Sectors")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public byte Number_Of_Sectors { get; set; }

      [DisplayName("(14) - No_Launch_Sector_Array")]
      public COP_Sector_Element[] No_Launch_Sector_Array { get; } = Enumerable.Range(0, COP_CZ_CCU_COP_ICD_V3_4.Num_Of_No_Launch_Sector).Select(i => new COP_Sector_Element()).ToArray();

      [DisplayName("(15) - Mechanical_Limit")]
      public COP_Sector_Element Mechanical_Limit { get; } = new COP_Sector_Element();

      [DisplayName("(16) - Component_Status_1")]
      public COP_Component_Status_1 Component_Status_1 { get; } = new COP_Component_Status_1();

      [DisplayName("(17) - DLQ")]
      public DLQ DLQ { get; } = new DLQ();

      [DisplayName("(18) - Versions")]
      public COP_Version Versions { get; } = new COP_Version();

      [DisplayName("(19) - Chat_Text_Length")]
      [Description("Range: 0 ... 51\u000D\u000A")]
      public byte Chat_Text_Length { get; set; }

      [DisplayName("(20) - Chat_Text")]
      [Description("Range: -128 ... 127\u000D\u000A")]
      public byte[] Chat_Text { get; } = new byte[Num_Of_Chars_In_Small_Text];

      [DisplayName("(20-A) - Chat_Text")]
      public string Chat_Text_Text
      {
        get
        {
          return UTF8Encoding.UTF8
            .GetString(Chat_Text, 0, Chat_Text_Length);
        }
        set
        {
          var data = UTF8Encoding.UTF8.GetBytes(value);
          var length = Math.Min(data.Length, Chat_Text.Length);
          
          Array.Clear(Chat_Text, 0, Chat_Text.Length);
          Array.Copy(data, 0, Chat_Text, 0, length);
          Chat_Text_Length = (byte)length;
        }
      }

      [DisplayName("(21) - Toplite_Azimuth")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort Toplite_Azimuth { get; set; }

      [DisplayName("(22) - Toplite_Elevation")]
      [Description("Range: 0 ... 65535\u000D\u000A")]
      public ushort Toplite_Elevation { get; set; }

      [DisplayName("(23) - Toplite_Target_Range")]
      [Description("Range: 0 ... 4294967295\u000D\u000AScaleFactor: 0.01\u000D\u000A")]
      public double Toplite_Target_Range {
        get =>  _Toplite_Target_Range * 0.01;
        set => _Toplite_Target_Range = (uint)Math.Round(value / 0.01);
      }
      internal uint _Toplite_Target_Range;

      [DisplayName("(24) - COPTopliteEngagementIndication")]
      [Description("Range: 0 ... 3\u000D\u000A")]
      public COPTopliteEngagementIndication COPTopliteEngagementIndication { get; set; }

      [DisplayName("(25) - Spare")]
      [Description("Range: 0 ... 63\u000D\u000A")]
      public byte Spare { get; set; }

      public COP_C2_Status()
      {
        CCU_COP_Header.msg_type = 8;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(MCU_Serial_ID);
        {
          byte help = 0;
          help |= (byte)(((byte)COP_State.Operability & 3) << 0);
          help |= (byte)(((byte)COP_State.Mode & 15) << 2);
          help |= (byte)(((byte)COP_State.Type & 1) << 6);
          help |= (byte)(((byte)COP_State.Spare & 1) << 7);
          writter.Write(help);
        }
        {
          byte help = 0;
          help |= (byte)(((byte)LM_Readiness.MCU_State & 7) << 0);
          help |= (byte)(((byte)LM_Readiness.LM_Readiness_To_Engage & 3) << 3);
          help |= (byte)(((byte)LM_Readiness.LM_Armed & 1) << 5);
          help |= (byte)(((byte)LM_Readiness.MCU_Abort_Button_Pressed & 1) << 6);
          help |= (byte)(((byte)LM_Readiness.Spare2 & 1) << 7);
          writter.Write(help);
        }
        {
          ushort help = 0;
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.Not_In_Immediate_Status & 1) << 0);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.Conflict_In_The_Fire_Source & 1) << 1);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.No_Missile_Available & 1) << 2);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.Uplink_Activated & 1) << 3);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.Launch_Fail & 1) << 4);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.MCU_Failure & 1) << 5);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.LCU_Failure & 1) << 6);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.MDCU_Failure & 1) << 7);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.BNET_Failure_And_LOAL & 1) << 8);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.INS_Failure & 1) << 9);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.GPS_Failure & 1) << 10);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.Time_Validity_Failure & 1) << 11);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.Pwr_Supply_Failure & 1) << 12);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.MCU_disconnected & 1) << 13);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.Turret_In_No_Launch_Sector & 1) << 14);
          help |= (ushort)(((ushort)MCU_Not_Ready_Reason.Spare3 & 1) << 15);
          writter.Write(help);
        }
        {
          ushort help = 0;
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.COP_Client_Disconnected & 1) << 0);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.COP_Server_Disconnected & 1) << 1);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.CCU_Server_Disconnected & 1) << 2);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.COP_CCU_Desync & 1) << 3);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.COP_MCU_Desync & 1) << 4);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.DLQ_Smaller_Than_Threashold & 1) << 5);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.COP_Client_Faulty & 1) << 6);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.COP_Server_Faulty & 1) << 7);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.CCU_Server_Faulty & 1) << 8);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.DTM_Comp & 1) << 9);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.SICS_Faulty & 1) << 10);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.Toplite_Faulty & 1) << 11);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.Spare4 & 1) << 12);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.Spare5 & 1) << 13);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.Spare6 & 1) << 14);
          help |= (ushort)(((ushort)COP_Not_Ready_Reason.Spare7 & 1) << 15);
          writter.Write(help);
        }
        writter.Write(Network_Size);
        {
          byte help = 0;
          help |= (byte)(((byte)Missiles_Available_For_Fire.objP & 15) << 0);
          help |= (byte)(((byte)Missiles_Available_For_Fire.Derby & 15) << 4);
          writter.Write(help);
        }
        {
          byte help = 0;
          help |= (byte)(((byte)Missiles_Available_For_Fire.ER_Derby & 15) << 0);
          help |= (byte)(((byte)Missiles_Available_For_Fire.LR_Derby & 15) << 4);
          writter.Write(help);
        }
        {
          byte help = 0;
          help |= (byte)(((byte)Total_Missiles.objP & 15) << 0);
          help |= (byte)(((byte)Total_Missiles.Derby & 15) << 4);
          writter.Write(help);
        }
        {
          byte help = 0;
          help |= (byte)(((byte)Total_Missiles.ER_Derby & 15) << 0);
          help |= (byte)(((byte)Total_Missiles.LR_Derby & 15) << 4);
          writter.Write(help);
        }
        writter.Write(Turret_Azimuth);
        writter.Write(_Cabin_Azimuth);
        writter.Write(Launcher_Position.Latitude);
        writter.Write(Launcher_Position.Longitude);
        writter.Write(Launcher_Position.Altitude);
        writter.Write(Number_Of_Sectors);
        for (var i = 0; i < Number_Of_Sectors; i++)
        {
          writter.Write(No_Launch_Sector_Array[i].Start_Azimuth);
          writter.Write(No_Launch_Sector_Array[i].Sector_Size);
        }
        writter.Write(Mechanical_Limit.Start_Azimuth);
        writter.Write(Mechanical_Limit.Sector_Size);
        {
          uint help = 0;
          help |= (uint)(((uint)Component_Status_1.MCU & 3) << 0);
          help |= (uint)(((uint)Component_Status_1.INS & 3) << 2);
          help |= (uint)(((uint)Component_Status_1.GPS & 3) << 4);
          help |= (uint)(((uint)Component_Status_1.MDCU & 3) << 6);
          help |= (uint)(((uint)Component_Status_1.LCU1 & 3) << 8);
          help |= (uint)(((uint)Component_Status_1.LCU2 & 3) << 10);
          help |= (uint)(((uint)Component_Status_1.BNET & 3) << 12);
          help |= (uint)(((uint)Component_Status_1.Time_Validity & 3) << 14);
          help |= (uint)(((uint)Component_Status_1.mDRS & 3) << 16);
          help |= (uint)(((uint)Component_Status_1.TopLite & 3) << 18);
          help |= (uint)(((uint)Component_Status_1.Rubidium & 3) << 20);
          help |= (uint)(((uint)Component_Status_1.Spare3 & 3) << 22);
          help |= (uint)(((uint)Component_Status_1.Spare4 & 3) << 24);
          help |= (uint)(((uint)Component_Status_1.Spare5 & 3) << 26);
          help |= (uint)(((uint)Component_Status_1.Spare6 & 3) << 28);
          help |= (uint)(((uint)Component_Status_1.Spare7 & 3) << 30);
          writter.Write(help);
        }
        {
          byte help = 0;
          help |= (byte)(((byte)DLQ.Data_Source & 1) << 0);
          help |= (byte)(((byte)DLQ.DLQ_Value & 127) << 1);
          writter.Write(help);
        }
        writter.Write(Versions.MCU);
        writter.Write(Versions.BNET);
        writter.Write(Versions.INS);
        writter.Write(Versions.MDCU);
        writter.Write(Versions.LCU);
        writter.Write(Versions.COP_Server_Version);
        writter.Write(Versions.COP_Client_Version);
        writter.Write(Versions.ICS_DTM_Version);
        writter.Write(Chat_Text_Length);
        for (var i = 0; i < Chat_Text_Length; i++)
        {
          writter.Write(Chat_Text[i]);
        }
        writter.Write(Toplite_Azimuth);
        writter.Write(Toplite_Elevation);
        writter.Write(_Toplite_Target_Range);
        {
          byte help = 0;
          help |= (byte)(((byte)COPTopliteEngagementIndication & 3) << 0);
          help |= (byte)(((byte)Spare & 63) << 2);
          writter.Write(help);
        }
      }

      public COP_C2_Status Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        MCU_Serial_ID = reader.ReadByte();
        {
          byte help = reader.ReadByte();
          COP_State.Operability = (COP_Operability)((help >> 0) & 3);
          COP_State.Mode = (COP_Mode)((help >> 2) & 15);
          COP_State.Type = (COP_Type)((help >> 6) & 1);
          COP_State.Spare = (byte)((help >> 7) & 1);
        }
        {
          byte help = reader.ReadByte();
          LM_Readiness.MCU_State = (COP_MCU_State)((help >> 0) & 7);
          LM_Readiness.LM_Readiness_To_Engage = (COP_Readiness_To_Engage)((help >> 3) & 3);
          LM_Readiness.LM_Armed = (COP_LM_Armed)((help >> 5) & 1);
          LM_Readiness.MCU_Abort_Button_Pressed = (No_Yes_Boolean)((help >> 6) & 1);
          LM_Readiness.Spare2 = (byte)((help >> 7) & 1);
        }
        {
          ushort help = reader.ReadUInt16();
          MCU_Not_Ready_Reason.Not_In_Immediate_Status = (No_Yes_Boolean)((help >> 0) & 1);
          MCU_Not_Ready_Reason.Conflict_In_The_Fire_Source = (No_Yes_Boolean)((help >> 1) & 1);
          MCU_Not_Ready_Reason.No_Missile_Available = (No_Yes_Boolean)((help >> 2) & 1);
          MCU_Not_Ready_Reason.Uplink_Activated = (No_Yes_Boolean)((help >> 3) & 1);
          MCU_Not_Ready_Reason.Launch_Fail = (No_Yes_Boolean)((help >> 4) & 1);
          MCU_Not_Ready_Reason.MCU_Failure = (No_Yes_Boolean)((help >> 5) & 1);
          MCU_Not_Ready_Reason.LCU_Failure = (No_Yes_Boolean)((help >> 6) & 1);
          MCU_Not_Ready_Reason.MDCU_Failure = (No_Yes_Boolean)((help >> 7) & 1);
          MCU_Not_Ready_Reason.BNET_Failure_And_LOAL = (No_Yes_Boolean)((help >> 8) & 1);
          MCU_Not_Ready_Reason.INS_Failure = (No_Yes_Boolean)((help >> 9) & 1);
          MCU_Not_Ready_Reason.GPS_Failure = (No_Yes_Boolean)((help >> 10) & 1);
          MCU_Not_Ready_Reason.Time_Validity_Failure = (No_Yes_Boolean)((help >> 11) & 1);
          MCU_Not_Ready_Reason.Pwr_Supply_Failure = (No_Yes_Boolean)((help >> 12) & 1);
          MCU_Not_Ready_Reason.MCU_disconnected = (No_Yes_Boolean)((help >> 13) & 1);
          MCU_Not_Ready_Reason.Turret_In_No_Launch_Sector = (No_Yes_Boolean)((help >> 14) & 1);
          MCU_Not_Ready_Reason.Spare3 = (ushort)((help >> 15) & 1);
        }
        {
          ushort help = reader.ReadUInt16();
          COP_Not_Ready_Reason.COP_Client_Disconnected = (No_Yes_Boolean)((help >> 0) & 1);
          COP_Not_Ready_Reason.COP_Server_Disconnected = (No_Yes_Boolean)((help >> 1) & 1);
          COP_Not_Ready_Reason.CCU_Server_Disconnected = (No_Yes_Boolean)((help >> 2) & 1);
          COP_Not_Ready_Reason.COP_CCU_Desync = (No_Yes_Boolean)((help >> 3) & 1);
          COP_Not_Ready_Reason.COP_MCU_Desync = (No_Yes_Boolean)((help >> 4) & 1);
          COP_Not_Ready_Reason.DLQ_Smaller_Than_Threashold = (No_Yes_Boolean)((help >> 5) & 1);
          COP_Not_Ready_Reason.COP_Client_Faulty = (No_Yes_Boolean)((help >> 6) & 1);
          COP_Not_Ready_Reason.COP_Server_Faulty = (No_Yes_Boolean)((help >> 7) & 1);
          COP_Not_Ready_Reason.CCU_Server_Faulty = (No_Yes_Boolean)((help >> 8) & 1);
          COP_Not_Ready_Reason.DTM_Comp = (No_Yes_Boolean)((help >> 9) & 1);
          COP_Not_Ready_Reason.SICS_Faulty = (No_Yes_Boolean)((help >> 10) & 1);
          COP_Not_Ready_Reason.Toplite_Faulty = (No_Yes_Boolean)((help >> 11) & 1);
          COP_Not_Ready_Reason.Spare4 = (ushort)((help >> 12) & 1);
          COP_Not_Ready_Reason.Spare5 = (ushort)((help >> 13) & 1);
          COP_Not_Ready_Reason.Spare6 = (ushort)((help >> 14) & 1);
          COP_Not_Ready_Reason.Spare7 = (ushort)((help >> 15) & 1);
        }
        Network_Size = reader.ReadByte();
        {
          byte help = reader.ReadByte();
          Missiles_Available_For_Fire.objP = (byte)((help >> 0) & 15);
          Missiles_Available_For_Fire.Derby = (byte)((help >> 4) & 15);
        }
        {
          byte help = reader.ReadByte();
          Missiles_Available_For_Fire.ER_Derby = (byte)((help >> 0) & 15);
          Missiles_Available_For_Fire.LR_Derby = (byte)((help >> 4) & 15);
        }
        {
          byte help = reader.ReadByte();
          Total_Missiles.objP = (byte)((help >> 0) & 15);
          Total_Missiles.Derby = (byte)((help >> 4) & 15);
        }
        {
          byte help = reader.ReadByte();
          Total_Missiles.ER_Derby = (byte)((help >> 0) & 15);
          Total_Missiles.LR_Derby = (byte)((help >> 4) & 15);
        }
        Turret_Azimuth = reader.ReadUInt16();
        _Cabin_Azimuth = reader.ReadUInt32();
        Launcher_Position.Latitude = reader.ReadSingle();
        Launcher_Position.Longitude = reader.ReadSingle();
        Launcher_Position.Altitude = reader.ReadSingle();
        Number_Of_Sectors = reader.ReadByte();
        for (var i = 0; i < Number_Of_Sectors; i++)
        {
          No_Launch_Sector_Array[i].Start_Azimuth = reader.ReadUInt16();
          No_Launch_Sector_Array[i].Sector_Size = reader.ReadUInt16();
        }
        Mechanical_Limit.Start_Azimuth = reader.ReadUInt16();
        Mechanical_Limit.Sector_Size = reader.ReadUInt16();
        {
          uint help = reader.ReadUInt32();
          Component_Status_1.MCU = (COP_Component_Status)((help >> 0) & 3);
          Component_Status_1.INS = (COP_Component_Status)((help >> 2) & 3);
          Component_Status_1.GPS = (COP_Component_Status)((help >> 4) & 3);
          Component_Status_1.MDCU = (COP_Component_Status)((help >> 6) & 3);
          Component_Status_1.LCU1 = (COP_Component_Status)((help >> 8) & 3);
          Component_Status_1.LCU2 = (COP_Component_Status)((help >> 10) & 3);
          Component_Status_1.BNET = (COP_Component_Status)((help >> 12) & 3);
          Component_Status_1.Time_Validity = (COP_Component_Status)((help >> 14) & 3);
          Component_Status_1.mDRS = (COP_Component_Status)((help >> 16) & 3);
          Component_Status_1.TopLite = (COP_Component_Status)((help >> 18) & 3);
          Component_Status_1.Rubidium = (COP_Component_Status)((help >> 20) & 3);
          Component_Status_1.Spare3 = (uint)((help >> 22) & 3);
          Component_Status_1.Spare4 = (uint)((help >> 24) & 3);
          Component_Status_1.Spare5 = (uint)((help >> 26) & 3);
          Component_Status_1.Spare6 = (uint)((help >> 28) & 3);
          Component_Status_1.Spare7 = (uint)((help >> 30) & 3);
        }
        {
          byte help = reader.ReadByte();
          DLQ.Data_Source = (DLQ_Data_Source)((help >> 0) & 1);
          DLQ.DLQ_Value = (byte)((help >> 1) & 127);
        }
        Versions.MCU = reader.ReadUInt16();
        Versions.BNET = reader.ReadUInt16();
        Versions.INS = reader.ReadUInt16();
        Versions.MDCU = reader.ReadUInt16();
        Versions.LCU = reader.ReadUInt16();
        Versions.COP_Server_Version = reader.ReadUInt16();
        Versions.COP_Client_Version = reader.ReadUInt16();
        Versions.ICS_DTM_Version = reader.ReadUInt16();
        Chat_Text_Length = reader.ReadByte();
        for (var i = 0; i < Chat_Text_Length; i++)
        {
          Chat_Text[i] = reader.ReadByte();
        }
        Toplite_Azimuth = reader.ReadUInt16();
        Toplite_Elevation = reader.ReadUInt16();
        _Toplite_Target_Range = reader.ReadUInt32();
        {
          byte help = reader.ReadByte();
          COPTopliteEngagementIndication = (COPTopliteEngagementIndication)((help >> 0) & 3);
          Spare = (byte)((help >> 2) & 63);
        }
        return this;
      }

      public override string ToString()
      {
        return "COP_C2_Status";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_Status ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: MCU_Serial_ID = {MCU_Serial_ID}");
        if (MCU_Serial_ID < 0) writter.AppendLine("!!      Value is less than 0");
        if (MCU_Serial_ID > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: COP_State.Operability = {COP_State.Operability}");
        writter.AppendLine($"{pos:X4}: COP_State.Mode = {COP_State.Mode}");
        writter.AppendLine($"{pos:X4}: COP_State.Type = {COP_State.Type}");
        writter.AppendLine($"{pos:X4}: COP_State.Spare = {COP_State.Spare}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: LM_Readiness.MCU_State = {LM_Readiness.MCU_State}");
        writter.AppendLine($"{pos:X4}: LM_Readiness.LM_Readiness_To_Engage = {LM_Readiness.LM_Readiness_To_Engage}");
        writter.AppendLine($"{pos:X4}: LM_Readiness.LM_Armed = {LM_Readiness.LM_Armed}");
        writter.AppendLine($"{pos:X4}: LM_Readiness.MCU_Abort_Button_Pressed = {LM_Readiness.MCU_Abort_Button_Pressed}");
        writter.AppendLine($"{pos:X4}: LM_Readiness.Spare2 = {LM_Readiness.Spare2}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Not_In_Immediate_Status = {MCU_Not_Ready_Reason.Not_In_Immediate_Status}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Conflict_In_The_Fire_Source = {MCU_Not_Ready_Reason.Conflict_In_The_Fire_Source}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.No_Missile_Available = {MCU_Not_Ready_Reason.No_Missile_Available}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Uplink_Activated = {MCU_Not_Ready_Reason.Uplink_Activated}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Launch_Fail = {MCU_Not_Ready_Reason.Launch_Fail}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.MCU_Failure = {MCU_Not_Ready_Reason.MCU_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.LCU_Failure = {MCU_Not_Ready_Reason.LCU_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.MDCU_Failure = {MCU_Not_Ready_Reason.MDCU_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.BNET_Failure_And_LOAL = {MCU_Not_Ready_Reason.BNET_Failure_And_LOAL}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.INS_Failure = {MCU_Not_Ready_Reason.INS_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.GPS_Failure = {MCU_Not_Ready_Reason.GPS_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Time_Validity_Failure = {MCU_Not_Ready_Reason.Time_Validity_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Pwr_Supply_Failure = {MCU_Not_Ready_Reason.Pwr_Supply_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.MCU_disconnected = {MCU_Not_Ready_Reason.MCU_disconnected}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Turret_In_No_Launch_Sector = {MCU_Not_Ready_Reason.Turret_In_No_Launch_Sector}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Spare3 = {MCU_Not_Ready_Reason.Spare3}");
        pos += 2;
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.COP_Client_Disconnected = {COP_Not_Ready_Reason.COP_Client_Disconnected}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.COP_Server_Disconnected = {COP_Not_Ready_Reason.COP_Server_Disconnected}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.CCU_Server_Disconnected = {COP_Not_Ready_Reason.CCU_Server_Disconnected}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.COP_CCU_Desync = {COP_Not_Ready_Reason.COP_CCU_Desync}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.COP_MCU_Desync = {COP_Not_Ready_Reason.COP_MCU_Desync}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.DLQ_Smaller_Than_Threashold = {COP_Not_Ready_Reason.DLQ_Smaller_Than_Threashold}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.COP_Client_Faulty = {COP_Not_Ready_Reason.COP_Client_Faulty}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.COP_Server_Faulty = {COP_Not_Ready_Reason.COP_Server_Faulty}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.CCU_Server_Faulty = {COP_Not_Ready_Reason.CCU_Server_Faulty}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.DTM_Comp = {COP_Not_Ready_Reason.DTM_Comp}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.SICS_Faulty = {COP_Not_Ready_Reason.SICS_Faulty}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.Toplite_Faulty = {COP_Not_Ready_Reason.Toplite_Faulty}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.Spare4 = {COP_Not_Ready_Reason.Spare4}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.Spare5 = {COP_Not_Ready_Reason.Spare5}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.Spare6 = {COP_Not_Ready_Reason.Spare6}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.Spare7 = {COP_Not_Ready_Reason.Spare7}");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Network_Size = {Network_Size}");
        if (Network_Size < 1) writter.AppendLine("!!      Value is less than 1");
        if (Network_Size > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Missiles_Available_For_Fire.objP = {Missiles_Available_For_Fire.objP}");
        writter.AppendLine($"{pos:X4}: Missiles_Available_For_Fire.Derby = {Missiles_Available_For_Fire.Derby}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Missiles_Available_For_Fire.ER_Derby = {Missiles_Available_For_Fire.ER_Derby}");
        writter.AppendLine($"{pos:X4}: Missiles_Available_For_Fire.LR_Derby = {Missiles_Available_For_Fire.LR_Derby}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Total_Missiles.objP = {Total_Missiles.objP}");
        writter.AppendLine($"{pos:X4}: Total_Missiles.Derby = {Total_Missiles.Derby}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Total_Missiles.ER_Derby = {Total_Missiles.ER_Derby}");
        writter.AppendLine($"{pos:X4}: Total_Missiles.LR_Derby = {Total_Missiles.LR_Derby}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Turret_Azimuth = {Turret_Azimuth}");
        if (Turret_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
        if (Turret_Azimuth > 6399) writter.AppendLine("!!      Value is greather than 6399");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Cabin_Azimuth = {Cabin_Azimuth}");
        if (_Cabin_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
        if (_Cabin_Azimuth > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Launcher_Position.Latitude = {Launcher_Position.Latitude}");
        if (Launcher_Position.Latitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Launcher_Position.Latitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Launcher_Position.Longitude = {Launcher_Position.Longitude}");
        if (Launcher_Position.Longitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Launcher_Position.Longitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Launcher_Position.Altitude = {Launcher_Position.Altitude}");
        if (Launcher_Position.Altitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Launcher_Position.Altitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Number_Of_Sectors = {Number_Of_Sectors}");
        if (Number_Of_Sectors < 0) writter.AppendLine("!!      Value is less than 0");
        if (Number_Of_Sectors > 3) writter.AppendLine("!!      Value is greather than 3");
        pos += 1;
        for (var i = 0; i < Number_Of_Sectors; i++)
        {
          writter.AppendLine($"{pos:X4}: No_Launch_Sector_Array[{i}].Start_Azimuth = {No_Launch_Sector_Array[i].Start_Azimuth}");
          if (No_Launch_Sector_Array[i].Start_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
          if (No_Launch_Sector_Array[i].Start_Azimuth > 359) writter.AppendLine("!!      Value is greather than 359");
          pos += 2;
          writter.AppendLine($"{pos:X4}: No_Launch_Sector_Array[{i}].Sector_Size = {No_Launch_Sector_Array[i].Sector_Size}");
          if (No_Launch_Sector_Array[i].Sector_Size < 0) writter.AppendLine("!!      Value is less than 0");
          if (No_Launch_Sector_Array[i].Sector_Size > 359) writter.AppendLine("!!      Value is greather than 359");
          pos += 2;
        }
        writter.AppendLine($"{pos:X4}: Mechanical_Limit.Start_Azimuth = {Mechanical_Limit.Start_Azimuth}");
        if (Mechanical_Limit.Start_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
        if (Mechanical_Limit.Start_Azimuth > 359) writter.AppendLine("!!      Value is greather than 359");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Mechanical_Limit.Sector_Size = {Mechanical_Limit.Sector_Size}");
        if (Mechanical_Limit.Sector_Size < 0) writter.AppendLine("!!      Value is less than 0");
        if (Mechanical_Limit.Sector_Size > 359) writter.AppendLine("!!      Value is greather than 359");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Component_Status_1.MCU = {Component_Status_1.MCU}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.INS = {Component_Status_1.INS}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.GPS = {Component_Status_1.GPS}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.MDCU = {Component_Status_1.MDCU}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.LCU1 = {Component_Status_1.LCU1}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.LCU2 = {Component_Status_1.LCU2}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.BNET = {Component_Status_1.BNET}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Time_Validity = {Component_Status_1.Time_Validity}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.mDRS = {Component_Status_1.mDRS}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.TopLite = {Component_Status_1.TopLite}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Rubidium = {Component_Status_1.Rubidium}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Spare3 = {Component_Status_1.Spare3}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Spare4 = {Component_Status_1.Spare4}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Spare5 = {Component_Status_1.Spare5}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Spare6 = {Component_Status_1.Spare6}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Spare7 = {Component_Status_1.Spare7}");
        pos += 4;
        writter.AppendLine($"{pos:X4}: DLQ.Data_Source = {DLQ.Data_Source}");
        writter.AppendLine($"{pos:X4}: DLQ.DLQ_Value = {DLQ.DLQ_Value}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Versions.MCU = {Versions.MCU}");
        if (Versions.MCU < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.MCU > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.BNET = {Versions.BNET}");
        if (Versions.BNET < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.BNET > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.INS = {Versions.INS}");
        if (Versions.INS < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.INS > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.MDCU = {Versions.MDCU}");
        if (Versions.MDCU < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.MDCU > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.LCU = {Versions.LCU}");
        if (Versions.LCU < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.LCU > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.COP_Server_Version = {Versions.COP_Server_Version}");
        if (Versions.COP_Server_Version < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.COP_Server_Version > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.COP_Client_Version = {Versions.COP_Client_Version}");
        if (Versions.COP_Client_Version < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.COP_Client_Version > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.ICS_DTM_Version = {Versions.ICS_DTM_Version}");
        if (Versions.ICS_DTM_Version < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.ICS_DTM_Version > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Chat_Text_Length = {Chat_Text_Length}");
        if (Chat_Text_Length < 0) writter.AppendLine("!!      Value is less than 0");
        if (Chat_Text_Length > 51) writter.AppendLine("!!      Value is greather than 51");
        pos += 1;
        for (var i = 0; i < Chat_Text_Length; i++)
        {
          writter.AppendLine($"{pos:X4}: Chat_Text[{i}] = {Chat_Text[i]}");
          pos += 1;
        }
        writter.AppendLine($"{pos:X4}: Toplite_Azimuth = {Toplite_Azimuth}");
        if (Toplite_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
        if (Toplite_Azimuth > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Toplite_Elevation = {Toplite_Elevation}");
        if (Toplite_Elevation < 0) writter.AppendLine("!!      Value is less than 0");
        if (Toplite_Elevation > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Toplite_Target_Range = {Toplite_Target_Range}");
        if (_Toplite_Target_Range < 0) writter.AppendLine("!!      Value is less than 0");
        if (_Toplite_Target_Range > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        writter.AppendLine($"{pos:X4}: COPTopliteEngagementIndication = {COPTopliteEngagementIndication}");
        writter.AppendLine($"{pos:X4}: Spare = {Spare}");
        pos += 1;
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_Status ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: MCU_Serial_ID = {MCU_Serial_ID}");
        if (MCU_Serial_ID < 0) writter.AppendLine("!!      Value is less than 0");
        if (MCU_Serial_ID > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: COP_State.Operability = {COP_State.Operability}");
        writter.AppendLine($"{pos:X4}: COP_State.Mode = {COP_State.Mode}");
        writter.AppendLine($"{pos:X4}: COP_State.Type = {COP_State.Type}");
        writter.AppendLine($"{pos:X4}: COP_State.Spare = {COP_State.Spare}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: LM_Readiness.MCU_State = {LM_Readiness.MCU_State}");
        writter.AppendLine($"{pos:X4}: LM_Readiness.LM_Readiness_To_Engage = {LM_Readiness.LM_Readiness_To_Engage}");
        writter.AppendLine($"{pos:X4}: LM_Readiness.LM_Armed = {LM_Readiness.LM_Armed}");
        writter.AppendLine($"{pos:X4}: LM_Readiness.MCU_Abort_Button_Pressed = {LM_Readiness.MCU_Abort_Button_Pressed}");
        writter.AppendLine($"{pos:X4}: LM_Readiness.Spare2 = {LM_Readiness.Spare2}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Not_In_Immediate_Status = {MCU_Not_Ready_Reason.Not_In_Immediate_Status}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Conflict_In_The_Fire_Source = {MCU_Not_Ready_Reason.Conflict_In_The_Fire_Source}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.No_Missile_Available = {MCU_Not_Ready_Reason.No_Missile_Available}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Uplink_Activated = {MCU_Not_Ready_Reason.Uplink_Activated}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Launch_Fail = {MCU_Not_Ready_Reason.Launch_Fail}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.MCU_Failure = {MCU_Not_Ready_Reason.MCU_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.LCU_Failure = {MCU_Not_Ready_Reason.LCU_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.MDCU_Failure = {MCU_Not_Ready_Reason.MDCU_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.BNET_Failure_And_LOAL = {MCU_Not_Ready_Reason.BNET_Failure_And_LOAL}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.INS_Failure = {MCU_Not_Ready_Reason.INS_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.GPS_Failure = {MCU_Not_Ready_Reason.GPS_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Time_Validity_Failure = {MCU_Not_Ready_Reason.Time_Validity_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Pwr_Supply_Failure = {MCU_Not_Ready_Reason.Pwr_Supply_Failure}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.MCU_disconnected = {MCU_Not_Ready_Reason.MCU_disconnected}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Turret_In_No_Launch_Sector = {MCU_Not_Ready_Reason.Turret_In_No_Launch_Sector}");
        writter.AppendLine($"{pos:X4}: MCU_Not_Ready_Reason.Spare3 = {MCU_Not_Ready_Reason.Spare3}");
        pos += 2;
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.COP_Client_Disconnected = {COP_Not_Ready_Reason.COP_Client_Disconnected}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.COP_Server_Disconnected = {COP_Not_Ready_Reason.COP_Server_Disconnected}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.CCU_Server_Disconnected = {COP_Not_Ready_Reason.CCU_Server_Disconnected}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.COP_CCU_Desync = {COP_Not_Ready_Reason.COP_CCU_Desync}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.COP_MCU_Desync = {COP_Not_Ready_Reason.COP_MCU_Desync}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.DLQ_Smaller_Than_Threashold = {COP_Not_Ready_Reason.DLQ_Smaller_Than_Threashold}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.COP_Client_Faulty = {COP_Not_Ready_Reason.COP_Client_Faulty}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.COP_Server_Faulty = {COP_Not_Ready_Reason.COP_Server_Faulty}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.CCU_Server_Faulty = {COP_Not_Ready_Reason.CCU_Server_Faulty}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.DTM_Comp = {COP_Not_Ready_Reason.DTM_Comp}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.SICS_Faulty = {COP_Not_Ready_Reason.SICS_Faulty}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.Toplite_Faulty = {COP_Not_Ready_Reason.Toplite_Faulty}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.Spare4 = {COP_Not_Ready_Reason.Spare4}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.Spare5 = {COP_Not_Ready_Reason.Spare5}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.Spare6 = {COP_Not_Ready_Reason.Spare6}");
        writter.AppendLine($"{pos:X4}: COP_Not_Ready_Reason.Spare7 = {COP_Not_Ready_Reason.Spare7}");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Network_Size = {Network_Size}");
        if (Network_Size < 1) writter.AppendLine("!!      Value is less than 1");
        if (Network_Size > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Missiles_Available_For_Fire.objP = {Missiles_Available_For_Fire.objP}");
        writter.AppendLine($"{pos:X4}: Missiles_Available_For_Fire.Derby = {Missiles_Available_For_Fire.Derby}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Missiles_Available_For_Fire.ER_Derby = {Missiles_Available_For_Fire.ER_Derby}");
        writter.AppendLine($"{pos:X4}: Missiles_Available_For_Fire.LR_Derby = {Missiles_Available_For_Fire.LR_Derby}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Total_Missiles.objP = {Total_Missiles.objP}");
        writter.AppendLine($"{pos:X4}: Total_Missiles.Derby = {Total_Missiles.Derby}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Total_Missiles.ER_Derby = {Total_Missiles.ER_Derby}");
        writter.AppendLine($"{pos:X4}: Total_Missiles.LR_Derby = {Total_Missiles.LR_Derby}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Turret_Azimuth = {Turret_Azimuth}");
        if (Turret_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
        if (Turret_Azimuth > 6399) writter.AppendLine("!!      Value is greather than 6399");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Cabin_Azimuth = {Cabin_Azimuth}");
        if (_Cabin_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
        if (_Cabin_Azimuth > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Launcher_Position.Latitude = {Launcher_Position.Latitude}");
        if (Launcher_Position.Latitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Launcher_Position.Latitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Launcher_Position.Longitude = {Launcher_Position.Longitude}");
        if (Launcher_Position.Longitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Launcher_Position.Longitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Launcher_Position.Altitude = {Launcher_Position.Altitude}");
        if (Launcher_Position.Altitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Launcher_Position.Altitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Number_Of_Sectors = {Number_Of_Sectors}");
        if (Number_Of_Sectors < 0) writter.AppendLine("!!      Value is less than 0");
        if (Number_Of_Sectors > 3) writter.AppendLine("!!      Value is greather than 3");
        pos += 1;
        for (var i = 0; i < Number_Of_Sectors; i++)
        {
          writter.AppendLine($"{pos:X4}: No_Launch_Sector_Array[{i}].Start_Azimuth = {No_Launch_Sector_Array[i].Start_Azimuth}");
          if (No_Launch_Sector_Array[i].Start_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
          if (No_Launch_Sector_Array[i].Start_Azimuth > 359) writter.AppendLine("!!      Value is greather than 359");
          pos += 2;
          writter.AppendLine($"{pos:X4}: No_Launch_Sector_Array[{i}].Sector_Size = {No_Launch_Sector_Array[i].Sector_Size}");
          if (No_Launch_Sector_Array[i].Sector_Size < 0) writter.AppendLine("!!      Value is less than 0");
          if (No_Launch_Sector_Array[i].Sector_Size > 359) writter.AppendLine("!!      Value is greather than 359");
          pos += 2;
        }
        writter.AppendLine($"{pos:X4}: Mechanical_Limit.Start_Azimuth = {Mechanical_Limit.Start_Azimuth}");
        if (Mechanical_Limit.Start_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
        if (Mechanical_Limit.Start_Azimuth > 359) writter.AppendLine("!!      Value is greather than 359");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Mechanical_Limit.Sector_Size = {Mechanical_Limit.Sector_Size}");
        if (Mechanical_Limit.Sector_Size < 0) writter.AppendLine("!!      Value is less than 0");
        if (Mechanical_Limit.Sector_Size > 359) writter.AppendLine("!!      Value is greather than 359");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Component_Status_1.MCU = {Component_Status_1.MCU}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.INS = {Component_Status_1.INS}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.GPS = {Component_Status_1.GPS}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.MDCU = {Component_Status_1.MDCU}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.LCU1 = {Component_Status_1.LCU1}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.LCU2 = {Component_Status_1.LCU2}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.BNET = {Component_Status_1.BNET}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Time_Validity = {Component_Status_1.Time_Validity}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.mDRS = {Component_Status_1.mDRS}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.TopLite = {Component_Status_1.TopLite}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Rubidium = {Component_Status_1.Rubidium}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Spare3 = {Component_Status_1.Spare3}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Spare4 = {Component_Status_1.Spare4}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Spare5 = {Component_Status_1.Spare5}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Spare6 = {Component_Status_1.Spare6}");
        writter.AppendLine($"{pos:X4}: Component_Status_1.Spare7 = {Component_Status_1.Spare7}");
        pos += 4;
        writter.AppendLine($"{pos:X4}: DLQ.Data_Source = {DLQ.Data_Source}");
        writter.AppendLine($"{pos:X4}: DLQ.DLQ_Value = {DLQ.DLQ_Value}");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Versions.MCU = {Versions.MCU}");
        if (Versions.MCU < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.MCU > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.BNET = {Versions.BNET}");
        if (Versions.BNET < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.BNET > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.INS = {Versions.INS}");
        if (Versions.INS < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.INS > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.MDCU = {Versions.MDCU}");
        if (Versions.MDCU < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.MDCU > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.LCU = {Versions.LCU}");
        if (Versions.LCU < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.LCU > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.COP_Server_Version = {Versions.COP_Server_Version}");
        if (Versions.COP_Server_Version < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.COP_Server_Version > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.COP_Client_Version = {Versions.COP_Client_Version}");
        if (Versions.COP_Client_Version < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.COP_Client_Version > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Versions.ICS_DTM_Version = {Versions.ICS_DTM_Version}");
        if (Versions.ICS_DTM_Version < 0) writter.AppendLine("!!      Value is less than 0");
        if (Versions.ICS_DTM_Version > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Chat_Text_Length = {Chat_Text_Length}");
        if (Chat_Text_Length < 0) writter.AppendLine("!!      Value is less than 0");
        if (Chat_Text_Length > 51) writter.AppendLine("!!      Value is greather than 51");
        pos += 1;
        for (var i = 0; i < Chat_Text_Length; i++)
        {
          writter.AppendLine($"{pos:X4}: Chat_Text[{i}] = {Chat_Text[i]}");
          pos += 1;
        }
        writter.AppendLine($"{pos:X4}: Toplite_Azimuth = {Toplite_Azimuth}");
        if (Toplite_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
        if (Toplite_Azimuth > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Toplite_Elevation = {Toplite_Elevation}");
        if (Toplite_Elevation < 0) writter.AppendLine("!!      Value is less than 0");
        if (Toplite_Elevation > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Toplite_Target_Range = {Toplite_Target_Range}");
        if (_Toplite_Target_Range < 0) writter.AppendLine("!!      Value is less than 0");
        if (_Toplite_Target_Range > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        writter.AppendLine($"{pos:X4}: COPTopliteEngagementIndication = {COPTopliteEngagementIndication}");
        writter.AppendLine($"{pos:X4}: Spare = {Spare}");
        pos += 1;
        return writter.ToString();
      }
    }

    public class COP_C2_Engagement_Status : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Number_Of_Engagements")]
      [Description("Range: 0 ... 8\u000D\u000A")]
      public byte Number_Of_Engagements { get; set; }

      [DisplayName("(03) - Engagement_Data")]
      public COP_Engagement_Status[] Engagement_Data { get; } = Enumerable.Range(0, COP_CZ_CCU_COP_ICD_V3_4.Num_Of_Engagements_From_MCU).Select(i => new COP_Engagement_Status()).ToArray();

      [DisplayName("(04) - Spare2")]
      [Description("Range: 0 ... 255\u000D\u000A")]
      public byte Spare2 { get; set; }

      public COP_C2_Engagement_Status()
      {
        CCU_COP_Header.msg_type = 129;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(Number_Of_Engagements);
        for (var i = 0; i < Number_Of_Engagements; i++)
        {
          writter.Write(Engagement_Data[i].Track_ID);
          writter.Write(Engagement_Data[i].CCU_Engagement_ID);
          writter.Write(Engagement_Data[i].COP_Engagement_ID);
          {
            byte help = 0;
            help |= (byte)(((byte)Engagement_Data[i].MCU_Engagement.MCU_Engagement_Handling & 7) << 0);
            help |= (byte)(((byte)Engagement_Data[i].MCU_Engagement.MCU_Engagement_Status & 3) << 3);
            help |= (byte)(((byte)Engagement_Data[i].MCU_Engagement.Spare & 1) << 5);
            help |= (byte)(((byte)Engagement_Data[i].MCU_Engagement.Engagement_Abort_Source & 3) << 6);
            writter.Write(help);
          }
          {
            byte help = 0;
            help |= (byte)(((byte)Engagement_Data[i].Allocated_Missile_Status.ICP_Allocated & 3) << 0);
            help |= (byte)(((byte)Engagement_Data[i].Allocated_Missile_Status.objP_ICP_Lock & 1) << 2);
            help |= (byte)(((byte)Engagement_Data[i].Allocated_Missile_Status.Derby_ICP_Lock & 1) << 3);
            help |= (byte)(((byte)Engagement_Data[i].Allocated_Missile_Status.ER_Derby_ICP_Lock & 1) << 4);
            help |= (byte)(((byte)Engagement_Data[i].Allocated_Missile_Status.LR_Derby_ICP_Lock & 1) << 5);
            help |= (byte)(((byte)Engagement_Data[i].Allocated_Missile_Status.ICP_Stage & 1) << 6);
            help |= (byte)(((byte)Engagement_Data[i].Allocated_Missile_Status.Lock_Type & 1) << 7);
            writter.Write(help);
          }
          {
            byte help = 0;
            help |= (byte)(((byte)Engagement_Data[i].Allocated_Missile_Status.Misfire_Indication & 1) << 0);
            help |= (byte)(((byte)Engagement_Data[i].Allocated_Missile_Status.Misfire_Reason & 3) << 1);
            help |= (byte)(((byte)Engagement_Data[i].Allocated_Missile_Status.spare & 31) << 3);
            writter.Write(help);
          }
          writter.Write(Engagement_Data[i].Engagement_Launch_Time);
          writter.Write(Engagement_Data[i].Turret_Launching_Azimuth);
          {
            byte help = 0;
            help |= (byte)(((byte)Engagement_Data[i].MCU_Engagement_Spare.Seeker_Mode & 1) << 0);
            help |= (byte)(((byte)Engagement_Data[i].MCU_Engagement_Spare.SPare1 & 1) << 1);
            help |= (byte)(((byte)Engagement_Data[i].MCU_Engagement_Spare.Spare2 & 1) << 2);
            help |= (byte)(((byte)Engagement_Data[i].MCU_Engagement_Spare.Spare3 & 1) << 3);
            help |= (byte)(((byte)Engagement_Data[i].MCU_Engagement_Spare.Spare4 & 3) << 4);
            help |= (byte)(((byte)Engagement_Data[i].MCU_Engagement_Spare.Spare5 & 3) << 6);
            writter.Write(help);
          }
          writter.Write(Engagement_Data[i].Coupled_Station_Id);
        }
        writter.Write(Spare2);
      }

      public COP_C2_Engagement_Status Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        Number_Of_Engagements = reader.ReadByte();
        for (var i = 0; i < Number_Of_Engagements; i++)
        {
          Engagement_Data[i].Track_ID = reader.ReadUInt16();
          Engagement_Data[i].CCU_Engagement_ID = reader.ReadUInt16();
          Engagement_Data[i].COP_Engagement_ID = reader.ReadUInt16();
          {
            byte help = reader.ReadByte();
            Engagement_Data[i].MCU_Engagement.MCU_Engagement_Handling = (COP_Engagement_Handling)((help >> 0) & 7);
            Engagement_Data[i].MCU_Engagement.MCU_Engagement_Status = (COP_MCU_Engagement_Status)((help >> 3) & 3);
            Engagement_Data[i].MCU_Engagement.Spare = (byte)((help >> 5) & 1);
            Engagement_Data[i].MCU_Engagement.Engagement_Abort_Source = (COP_Engagement_Abort_Source)((help >> 6) & 3);
          }
          {
            byte help = reader.ReadByte();
            Engagement_Data[i].Allocated_Missile_Status.ICP_Allocated = (Type_Of_ICP)((help >> 0) & 3);
            Engagement_Data[i].Allocated_Missile_Status.objP_ICP_Lock = (ICP_Lock_Status)((help >> 2) & 1);
            Engagement_Data[i].Allocated_Missile_Status.Derby_ICP_Lock = (ICP_Lock_Status)((help >> 3) & 1);
            Engagement_Data[i].Allocated_Missile_Status.ER_Derby_ICP_Lock = (ICP_Lock_Status)((help >> 4) & 1);
            Engagement_Data[i].Allocated_Missile_Status.LR_Derby_ICP_Lock = (ICP_Lock_Status)((help >> 5) & 1);
            Engagement_Data[i].Allocated_Missile_Status.ICP_Stage = (ICP_Stage)((help >> 6) & 1);
            Engagement_Data[i].Allocated_Missile_Status.Lock_Type = (Locking_Policy)((help >> 7) & 1);
          }
          {
            byte help = reader.ReadByte();
            Engagement_Data[i].Allocated_Missile_Status.Misfire_Indication = (No_Yes_Boolean)((help >> 0) & 1);
            Engagement_Data[i].Allocated_Missile_Status.Misfire_Reason = (COP_Misfire_Reason)((help >> 1) & 3);
            Engagement_Data[i].Allocated_Missile_Status.spare = (byte)((help >> 3) & 31);
          }
          Engagement_Data[i].Engagement_Launch_Time = reader.ReadUInt32();
          Engagement_Data[i].Turret_Launching_Azimuth = reader.ReadUInt16();
          {
            byte help = reader.ReadByte();
            Engagement_Data[i].MCU_Engagement_Spare.Seeker_Mode = (COP_Seeker_mode)((help >> 0) & 1);
            Engagement_Data[i].MCU_Engagement_Spare.SPare1 = (byte)((help >> 1) & 1);
            Engagement_Data[i].MCU_Engagement_Spare.Spare2 = (byte)((help >> 2) & 1);
            Engagement_Data[i].MCU_Engagement_Spare.Spare3 = (byte)((help >> 3) & 1);
            Engagement_Data[i].MCU_Engagement_Spare.Spare4 = (byte)((help >> 4) & 3);
            Engagement_Data[i].MCU_Engagement_Spare.Spare5 = (byte)((help >> 6) & 3);
          }
          Engagement_Data[i].Coupled_Station_Id = reader.ReadByte();
        }
        Spare2 = reader.ReadByte();
        return this;
      }

      public override string ToString()
      {
        return "COP_C2_Engagement_Status";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_Engagement_Status ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Number_Of_Engagements = {Number_Of_Engagements}");
        if (Number_Of_Engagements < 0) writter.AppendLine("!!      Value is less than 0");
        if (Number_Of_Engagements > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        for (var i = 0; i < Number_Of_Engagements; i++)
        {
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Track_ID = {Engagement_Data[i].Track_ID}");
          if (Engagement_Data[i].Track_ID < 1) writter.AppendLine("!!      Value is less than 1");
          if (Engagement_Data[i].Track_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].CCU_Engagement_ID = {Engagement_Data[i].CCU_Engagement_ID}");
          if (Engagement_Data[i].CCU_Engagement_ID < 1) writter.AppendLine("!!      Value is less than 1");
          if (Engagement_Data[i].CCU_Engagement_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].COP_Engagement_ID = {Engagement_Data[i].COP_Engagement_ID}");
          if (Engagement_Data[i].COP_Engagement_ID < 1) writter.AppendLine("!!      Value is less than 1");
          if (Engagement_Data[i].COP_Engagement_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement.MCU_Engagement_Handling = {Engagement_Data[i].MCU_Engagement.MCU_Engagement_Handling}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement.MCU_Engagement_Status = {Engagement_Data[i].MCU_Engagement.MCU_Engagement_Status}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement.Spare = {Engagement_Data[i].MCU_Engagement.Spare}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement.Engagement_Abort_Source = {Engagement_Data[i].MCU_Engagement.Engagement_Abort_Source}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.ICP_Allocated = {Engagement_Data[i].Allocated_Missile_Status.ICP_Allocated}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.objP_ICP_Lock = {Engagement_Data[i].Allocated_Missile_Status.objP_ICP_Lock}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.Derby_ICP_Lock = {Engagement_Data[i].Allocated_Missile_Status.Derby_ICP_Lock}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.ER_Derby_ICP_Lock = {Engagement_Data[i].Allocated_Missile_Status.ER_Derby_ICP_Lock}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.LR_Derby_ICP_Lock = {Engagement_Data[i].Allocated_Missile_Status.LR_Derby_ICP_Lock}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.ICP_Stage = {Engagement_Data[i].Allocated_Missile_Status.ICP_Stage}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.Lock_Type = {Engagement_Data[i].Allocated_Missile_Status.Lock_Type}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.Misfire_Indication = {Engagement_Data[i].Allocated_Missile_Status.Misfire_Indication}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.Misfire_Reason = {Engagement_Data[i].Allocated_Missile_Status.Misfire_Reason}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.spare = {Engagement_Data[i].Allocated_Missile_Status.spare}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Engagement_Launch_Time = {Engagement_Data[i].Engagement_Launch_Time}");
          if (Engagement_Data[i].Engagement_Launch_Time < 0) writter.AppendLine("!!      Value is less than 0");
          if (Engagement_Data[i].Engagement_Launch_Time > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Turret_Launching_Azimuth = {Engagement_Data[i].Turret_Launching_Azimuth}");
          if (Engagement_Data[i].Turret_Launching_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
          if (Engagement_Data[i].Turret_Launching_Azimuth > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement_Spare.Seeker_Mode = {Engagement_Data[i].MCU_Engagement_Spare.Seeker_Mode}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement_Spare.SPare1 = {Engagement_Data[i].MCU_Engagement_Spare.SPare1}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement_Spare.Spare2 = {Engagement_Data[i].MCU_Engagement_Spare.Spare2}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement_Spare.Spare3 = {Engagement_Data[i].MCU_Engagement_Spare.Spare3}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement_Spare.Spare4 = {Engagement_Data[i].MCU_Engagement_Spare.Spare4}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement_Spare.Spare5 = {Engagement_Data[i].MCU_Engagement_Spare.Spare5}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Coupled_Station_Id = {Engagement_Data[i].Coupled_Station_Id}");
          if (Engagement_Data[i].Coupled_Station_Id < 0) writter.AppendLine("!!      Value is less than 0");
          if (Engagement_Data[i].Coupled_Station_Id > 8) writter.AppendLine("!!      Value is greather than 8");
          pos += 1;
        }
        writter.AppendLine($"{pos:X4}: Spare2 = {Spare2}");
        if (Spare2 < 0) writter.AppendLine("!!      Value is less than 0");
        if (Spare2 > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_Engagement_Status ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Number_Of_Engagements = {Number_Of_Engagements}");
        if (Number_Of_Engagements < 0) writter.AppendLine("!!      Value is less than 0");
        if (Number_Of_Engagements > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 1;
        for (var i = 0; i < Number_Of_Engagements; i++)
        {
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Track_ID = {Engagement_Data[i].Track_ID}");
          if (Engagement_Data[i].Track_ID < 1) writter.AppendLine("!!      Value is less than 1");
          if (Engagement_Data[i].Track_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].CCU_Engagement_ID = {Engagement_Data[i].CCU_Engagement_ID}");
          if (Engagement_Data[i].CCU_Engagement_ID < 1) writter.AppendLine("!!      Value is less than 1");
          if (Engagement_Data[i].CCU_Engagement_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].COP_Engagement_ID = {Engagement_Data[i].COP_Engagement_ID}");
          if (Engagement_Data[i].COP_Engagement_ID < 1) writter.AppendLine("!!      Value is less than 1");
          if (Engagement_Data[i].COP_Engagement_ID > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement.MCU_Engagement_Handling = {Engagement_Data[i].MCU_Engagement.MCU_Engagement_Handling}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement.MCU_Engagement_Status = {Engagement_Data[i].MCU_Engagement.MCU_Engagement_Status}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement.Spare = {Engagement_Data[i].MCU_Engagement.Spare}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement.Engagement_Abort_Source = {Engagement_Data[i].MCU_Engagement.Engagement_Abort_Source}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.ICP_Allocated = {Engagement_Data[i].Allocated_Missile_Status.ICP_Allocated}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.objP_ICP_Lock = {Engagement_Data[i].Allocated_Missile_Status.objP_ICP_Lock}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.Derby_ICP_Lock = {Engagement_Data[i].Allocated_Missile_Status.Derby_ICP_Lock}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.ER_Derby_ICP_Lock = {Engagement_Data[i].Allocated_Missile_Status.ER_Derby_ICP_Lock}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.LR_Derby_ICP_Lock = {Engagement_Data[i].Allocated_Missile_Status.LR_Derby_ICP_Lock}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.ICP_Stage = {Engagement_Data[i].Allocated_Missile_Status.ICP_Stage}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.Lock_Type = {Engagement_Data[i].Allocated_Missile_Status.Lock_Type}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.Misfire_Indication = {Engagement_Data[i].Allocated_Missile_Status.Misfire_Indication}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.Misfire_Reason = {Engagement_Data[i].Allocated_Missile_Status.Misfire_Reason}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Allocated_Missile_Status.spare = {Engagement_Data[i].Allocated_Missile_Status.spare}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Engagement_Launch_Time = {Engagement_Data[i].Engagement_Launch_Time}");
          if (Engagement_Data[i].Engagement_Launch_Time < 0) writter.AppendLine("!!      Value is less than 0");
          if (Engagement_Data[i].Engagement_Launch_Time > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Turret_Launching_Azimuth = {Engagement_Data[i].Turret_Launching_Azimuth}");
          if (Engagement_Data[i].Turret_Launching_Azimuth < 0) writter.AppendLine("!!      Value is less than 0");
          if (Engagement_Data[i].Turret_Launching_Azimuth > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement_Spare.Seeker_Mode = {Engagement_Data[i].MCU_Engagement_Spare.Seeker_Mode}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement_Spare.SPare1 = {Engagement_Data[i].MCU_Engagement_Spare.SPare1}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement_Spare.Spare2 = {Engagement_Data[i].MCU_Engagement_Spare.Spare2}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement_Spare.Spare3 = {Engagement_Data[i].MCU_Engagement_Spare.Spare3}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement_Spare.Spare4 = {Engagement_Data[i].MCU_Engagement_Spare.Spare4}");
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].MCU_Engagement_Spare.Spare5 = {Engagement_Data[i].MCU_Engagement_Spare.Spare5}");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Engagement_Data[{i}].Coupled_Station_Id = {Engagement_Data[i].Coupled_Station_Id}");
          if (Engagement_Data[i].Coupled_Station_Id < 0) writter.AppendLine("!!      Value is less than 0");
          if (Engagement_Data[i].Coupled_Station_Id > 8) writter.AppendLine("!!      Value is greather than 8");
          pos += 1;
        }
        writter.AppendLine($"{pos:X4}: Spare2 = {Spare2}");
        if (Spare2 < 0) writter.AppendLine("!!      Value is less than 0");
        if (Spare2 > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        return writter.ToString();
      }
    }

    public class COP_C2_ACK : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Sequence_num")]
      [Description("Range: 0 ... 4294967295\u000D\u000A")]
      public uint Sequence_num { get; set; }

      public COP_C2_ACK()
      {
        CCU_COP_Header.msg_type = 133;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(Sequence_num);
      }

      public COP_C2_ACK Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        Sequence_num = reader.ReadUInt32();
        return this;
      }

      public override string ToString()
      {
        return "COP_C2_ACK";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_ACK ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Sequence_num = {Sequence_num}");
        if (Sequence_num < 0) writter.AppendLine("!!      Value is less than 0");
        if (Sequence_num > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_ACK ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Sequence_num = {Sequence_num}");
        if (Sequence_num < 0) writter.AppendLine("!!      Value is less than 0");
        if (Sequence_num > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        return writter.ToString();
      }
    }

    public class COP_C2_Pointer : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Text_Length")]
      [Description("Range: 0 ... 31\u000D\u000A")]
      public byte Text_Length { get; set; }

      [DisplayName("(03) - Text")]
      [Description("Range: -128 ... 127\u000D\u000A")]
      public byte[] Text { get; } = new byte[Num_Of_Chars_In_Pointer];

      [DisplayName("(03-A) - Text")]
      public string Text_Text
      {
        get
        {
          return UTF8Encoding.UTF8
            .GetString(Text, 0, Text_Length);
        }
        set
        {
          var data = UTF8Encoding.UTF8.GetBytes(value);
          var length = Math.Min(data.Length, Text.Length);
          
          Array.Clear(Text, 0, Text.Length);
          Array.Copy(data, 0, Text, 0, length);
          Text_Length = (byte)length;
        }
      }

      [DisplayName("(04) - Latitude")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Latitude { get; set; }

      [DisplayName("(05) - Longitude")]
      [Description("Range: -3.4E+38 ... 3.4E+38\u000D\u000A")]
      public float Longitude { get; set; }

      public COP_C2_Pointer()
      {
        CCU_COP_Header.msg_type = 140;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(Text_Length);
        for (var i = 0; i < Text_Length; i++)
        {
          writter.Write(Text[i]);
        }
        writter.Write(Latitude);
        writter.Write(Longitude);
      }

      public COP_C2_Pointer Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        Text_Length = reader.ReadByte();
        for (var i = 0; i < Text_Length; i++)
        {
          Text[i] = reader.ReadByte();
        }
        Latitude = reader.ReadSingle();
        Longitude = reader.ReadSingle();
        return this;
      }

      public override string ToString()
      {
        return "COP_C2_Pointer";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_Pointer ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Text_Length = {Text_Length}");
        if (Text_Length < 0) writter.AppendLine("!!      Value is less than 0");
        if (Text_Length > 31) writter.AppendLine("!!      Value is greather than 31");
        pos += 1;
        for (var i = 0; i < Text_Length; i++)
        {
          writter.AppendLine($"{pos:X4}: Text[{i}] = {Text[i]}");
          pos += 1;
        }
        writter.AppendLine($"{pos:X4}: Latitude = {Latitude}");
        if (Latitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Latitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Longitude = {Longitude}");
        if (Longitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Longitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_Pointer ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Text_Length = {Text_Length}");
        if (Text_Length < 0) writter.AppendLine("!!      Value is less than 0");
        if (Text_Length > 31) writter.AppendLine("!!      Value is greather than 31");
        pos += 1;
        for (var i = 0; i < Text_Length; i++)
        {
          writter.AppendLine($"{pos:X4}: Text[{i}] = {Text[i]}");
          pos += 1;
        }
        writter.AppendLine($"{pos:X4}: Latitude = {Latitude}");
        if (Latitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Latitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Longitude = {Longitude}");
        if (Longitude < -3.4E+38) writter.AppendLine("!!      Value is less than -3.4E+38");
        if (Longitude > 3.4E+38) writter.AppendLine("!!      Value is greather than 3.4E+38");
        pos += 4;
        return writter.ToString();
      }
    }

    public class COP_C2_Msl_DL : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Data")]
      public SMslsInAirDlData Data { get; } = new SMslsInAirDlData();

      public COP_C2_Msl_DL()
      {
        CCU_COP_Header.msg_type = 134;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(Data.q_NumOfDlReportsInMsg);
        for (var i = 0; i < Data.q_NumOfDlReportsInMsg; i++)
        {
          writter.Write(Data.q_MslInAir[i].q_MslID);
          writter.Write(Data.q_MslInAir[i].q_TimeOfLastDlMsgRcv);
          writter.Write(Data.q_MslInAir[i].q_TimeFromMLL);
          writter.Write(Data.q_MslInAir[i].q_servoPressure);
          writter.Write(Data.q_MslInAir[i]._q_mslPosition_N_L0);
          writter.Write(Data.q_MslInAir[i]._q_mslPosition_E_L0);
          writter.Write(Data.q_MslInAir[i]._q_mslPosition_D_LO);
          writter.Write(Data.q_MslInAir[i].q_mslVn_L0);
          writter.Write(Data.q_MslInAir[i].q_mslVe_L0);
          writter.Write(Data.q_MslInAir[i].q_mslVd_L0);
          writter.Write(Data.q_MslInAir[i].q_MslNavPos_X_Uncertainty);
          writter.Write(Data.q_MslInAir[i].q_MslNavPos_Y_Uncertainty);
          writter.Write(Data.q_MslInAir[i].q_MslNavPos_Z_Uncertainty);
          writter.Write(Data.q_MslInAir[i].q_MslNavVel_X_Uncertainty);
          writter.Write(Data.q_MslInAir[i].q_MslNavVel_Y_Uncertainty);
          writter.Write(Data.q_MslInAir[i].q_MslNavVel_Z_Uncertainty);
          writter.Write(Data.q_MslInAir[i].q_TimeToGo);
          writter.Write(Data.q_MslInAir[i].q_guidanceMode);
          writter.Write(Data.q_MslInAir[i].q_navPerformance);
          writter.Write(Data.q_MslInAir[i].q_netState);
          writter.Write(Data.q_MslInAir[i].q_seekerState);
          writter.Write(Data.q_MslInAir[i].q_skrGimbal_Az);
          writter.Write(Data.q_MslInAir[i].q_skrGimbal_El);
          writter.Write(Data.q_MslInAir[i].q_skrRange);
          writter.Write(Data.q_MslInAir[i].q_skrRangeDot);
          writter.Write(Data.q_MslInAir[i].q_skrSnr);
          writter.Write(Data.q_MslInAir[i].q_skrEcm);
          writter.Write(Data.q_MslInAir[i].q_lamdaDotY);
          writter.Write(Data.q_MslInAir[i].q_lamdaDotZ);
          writter.Write(Data.q_MslInAir[i].q_mmcBit);
          writter.Write(Data.q_MslInAir[i].q_mslStatus);
        }
      }

      public COP_C2_Msl_DL Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        Data.q_NumOfDlReportsInMsg = reader.ReadUInt16();
        for (var i = 0; i < Data.q_NumOfDlReportsInMsg; i++)
        {
          Data.q_MslInAir[i].q_MslID = reader.ReadByte();
          Data.q_MslInAir[i].q_TimeOfLastDlMsgRcv = reader.ReadInt32();
          Data.q_MslInAir[i].q_TimeFromMLL = reader.ReadUInt32();
          Data.q_MslInAir[i].q_servoPressure = reader.ReadUInt16();
          Data.q_MslInAir[i]._q_mslPosition_N_L0 = reader.ReadInt16();
          Data.q_MslInAir[i]._q_mslPosition_E_L0 = reader.ReadInt16();
          Data.q_MslInAir[i]._q_mslPosition_D_LO = reader.ReadInt16();
          Data.q_MslInAir[i].q_mslVn_L0 = reader.ReadInt16();
          Data.q_MslInAir[i].q_mslVe_L0 = reader.ReadInt16();
          Data.q_MslInAir[i].q_mslVd_L0 = reader.ReadInt16();
          Data.q_MslInAir[i].q_MslNavPos_X_Uncertainty = reader.ReadUInt16();
          Data.q_MslInAir[i].q_MslNavPos_Y_Uncertainty = reader.ReadUInt16();
          Data.q_MslInAir[i].q_MslNavPos_Z_Uncertainty = reader.ReadUInt16();
          Data.q_MslInAir[i].q_MslNavVel_X_Uncertainty = reader.ReadUInt16();
          Data.q_MslInAir[i].q_MslNavVel_Y_Uncertainty = reader.ReadUInt16();
          Data.q_MslInAir[i].q_MslNavVel_Z_Uncertainty = reader.ReadUInt16();
          Data.q_MslInAir[i].q_TimeToGo = reader.ReadInt16();
          Data.q_MslInAir[i].q_guidanceMode = reader.ReadByte();
          Data.q_MslInAir[i].q_navPerformance = reader.ReadByte();
          Data.q_MslInAir[i].q_netState = reader.ReadByte();
          Data.q_MslInAir[i].q_seekerState = reader.ReadByte();
          Data.q_MslInAir[i].q_skrGimbal_Az = reader.ReadByte();
          Data.q_MslInAir[i].q_skrGimbal_El = reader.ReadByte();
          Data.q_MslInAir[i].q_skrRange = reader.ReadInt16();
          Data.q_MslInAir[i].q_skrRangeDot = reader.ReadInt16();
          Data.q_MslInAir[i].q_skrSnr = reader.ReadByte();
          Data.q_MslInAir[i].q_skrEcm = reader.ReadByte();
          Data.q_MslInAir[i].q_lamdaDotY = reader.ReadInt16();
          Data.q_MslInAir[i].q_lamdaDotZ = reader.ReadInt16();
          Data.q_MslInAir[i].q_mmcBit = reader.ReadByte();
          Data.q_MslInAir[i].q_mslStatus = reader.ReadByte();
        }
        return this;
      }

      public override string ToString()
      {
        return "COP_C2_Msl_DL";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_Msl_DL ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Data.q_NumOfDlReportsInMsg = {Data.q_NumOfDlReportsInMsg}");
        if (Data.q_NumOfDlReportsInMsg < 0) writter.AppendLine("!!      Value is less than 0");
        if (Data.q_NumOfDlReportsInMsg > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 2;
        for (var i = 0; i < Data.q_NumOfDlReportsInMsg; i++)
        {
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslID = {Data.q_MslInAir[i].q_MslID}");
          if (Data.q_MslInAir[i].q_MslID < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslID > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_TimeOfLastDlMsgRcv = {Data.q_MslInAir[i].q_TimeOfLastDlMsgRcv}");
          if (Data.q_MslInAir[i].q_TimeOfLastDlMsgRcv < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Data.q_MslInAir[i].q_TimeOfLastDlMsgRcv > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_TimeFromMLL = {Data.q_MslInAir[i].q_TimeFromMLL}");
          if (Data.q_MslInAir[i].q_TimeFromMLL < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_TimeFromMLL > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_servoPressure = {Data.q_MslInAir[i].q_servoPressure}");
          if (Data.q_MslInAir[i].q_servoPressure < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_servoPressure > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslPosition_N_L0 = {Data.q_MslInAir[i].q_mslPosition_N_L0}");
          if (Data.q_MslInAir[i]._q_mslPosition_N_L0 < -20000) writter.AppendLine("!!      Value is less than -20000");
          if (Data.q_MslInAir[i]._q_mslPosition_N_L0 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslPosition_E_L0 = {Data.q_MslInAir[i].q_mslPosition_E_L0}");
          if (Data.q_MslInAir[i]._q_mslPosition_E_L0 < -20000) writter.AppendLine("!!      Value is less than -20000");
          if (Data.q_MslInAir[i]._q_mslPosition_E_L0 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslPosition_D_LO = {Data.q_MslInAir[i].q_mslPosition_D_LO}");
          if (Data.q_MslInAir[i]._q_mslPosition_D_LO < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i]._q_mslPosition_D_LO > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslVn_L0 = {Data.q_MslInAir[i].q_mslVn_L0}");
          if (Data.q_MslInAir[i].q_mslVn_L0 < -2000) writter.AppendLine("!!      Value is less than -2000");
          if (Data.q_MslInAir[i].q_mslVn_L0 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslVe_L0 = {Data.q_MslInAir[i].q_mslVe_L0}");
          if (Data.q_MslInAir[i].q_mslVe_L0 < -2000) writter.AppendLine("!!      Value is less than -2000");
          if (Data.q_MslInAir[i].q_mslVe_L0 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslVd_L0 = {Data.q_MslInAir[i].q_mslVd_L0}");
          if (Data.q_MslInAir[i].q_mslVd_L0 < -2000) writter.AppendLine("!!      Value is less than -2000");
          if (Data.q_MslInAir[i].q_mslVd_L0 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslNavPos_X_Uncertainty = {Data.q_MslInAir[i].q_MslNavPos_X_Uncertainty}");
          if (Data.q_MslInAir[i].q_MslNavPos_X_Uncertainty < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslNavPos_X_Uncertainty > 1000) writter.AppendLine("!!      Value is greather than 1000");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslNavPos_Y_Uncertainty = {Data.q_MslInAir[i].q_MslNavPos_Y_Uncertainty}");
          if (Data.q_MslInAir[i].q_MslNavPos_Y_Uncertainty < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslNavPos_Y_Uncertainty > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslNavPos_Z_Uncertainty = {Data.q_MslInAir[i].q_MslNavPos_Z_Uncertainty}");
          if (Data.q_MslInAir[i].q_MslNavPos_Z_Uncertainty < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslNavPos_Z_Uncertainty > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslNavVel_X_Uncertainty = {Data.q_MslInAir[i].q_MslNavVel_X_Uncertainty}");
          if (Data.q_MslInAir[i].q_MslNavVel_X_Uncertainty < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslNavVel_X_Uncertainty > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslNavVel_Y_Uncertainty = {Data.q_MslInAir[i].q_MslNavVel_Y_Uncertainty}");
          if (Data.q_MslInAir[i].q_MslNavVel_Y_Uncertainty < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslNavVel_Y_Uncertainty > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslNavVel_Z_Uncertainty = {Data.q_MslInAir[i].q_MslNavVel_Z_Uncertainty}");
          if (Data.q_MslInAir[i].q_MslNavVel_Z_Uncertainty < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslNavVel_Z_Uncertainty > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_TimeToGo = {Data.q_MslInAir[i].q_TimeToGo}");
          if (Data.q_MslInAir[i].q_TimeToGo < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_TimeToGo > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_guidanceMode = {Data.q_MslInAir[i].q_guidanceMode}");
          if (Data.q_MslInAir[i].q_guidanceMode < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_guidanceMode > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_navPerformance = {Data.q_MslInAir[i].q_navPerformance}");
          if (Data.q_MslInAir[i].q_navPerformance < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_navPerformance > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_netState = {Data.q_MslInAir[i].q_netState}");
          if (Data.q_MslInAir[i].q_netState < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_netState > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_seekerState = {Data.q_MslInAir[i].q_seekerState}");
          if (Data.q_MslInAir[i].q_seekerState < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_seekerState > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_skrGimbal_Az = {Data.q_MslInAir[i].q_skrGimbal_Az}");
          if (Data.q_MslInAir[i].q_skrGimbal_Az < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_skrGimbal_Az > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_skrGimbal_El = {Data.q_MslInAir[i].q_skrGimbal_El}");
          if (Data.q_MslInAir[i].q_skrGimbal_El < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_skrGimbal_El > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_skrRange = {Data.q_MslInAir[i].q_skrRange}");
          if (Data.q_MslInAir[i].q_skrRange < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_skrRange > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_skrRangeDot = {Data.q_MslInAir[i].q_skrRangeDot}");
          if (Data.q_MslInAir[i].q_skrRangeDot < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_skrRangeDot > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_skrSnr = {Data.q_MslInAir[i].q_skrSnr}");
          if (Data.q_MslInAir[i].q_skrSnr < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_skrSnr > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_skrEcm = {Data.q_MslInAir[i].q_skrEcm}");
          if (Data.q_MslInAir[i].q_skrEcm < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_skrEcm > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_lamdaDotY = {Data.q_MslInAir[i].q_lamdaDotY}");
          if (Data.q_MslInAir[i].q_lamdaDotY < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_lamdaDotY > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_lamdaDotZ = {Data.q_MslInAir[i].q_lamdaDotZ}");
          if (Data.q_MslInAir[i].q_lamdaDotZ < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_lamdaDotZ > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mmcBit = {Data.q_MslInAir[i].q_mmcBit}");
          if (Data.q_MslInAir[i].q_mmcBit < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_mmcBit > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslStatus = {Data.q_MslInAir[i].q_mslStatus}");
          if (Data.q_MslInAir[i].q_mslStatus < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_mslStatus > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
        }
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_Msl_DL ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Data.q_NumOfDlReportsInMsg = {Data.q_NumOfDlReportsInMsg}");
        if (Data.q_NumOfDlReportsInMsg < 0) writter.AppendLine("!!      Value is less than 0");
        if (Data.q_NumOfDlReportsInMsg > 8) writter.AppendLine("!!      Value is greather than 8");
        pos += 2;
        for (var i = 0; i < Data.q_NumOfDlReportsInMsg; i++)
        {
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslID = {Data.q_MslInAir[i].q_MslID}");
          if (Data.q_MslInAir[i].q_MslID < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslID > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_TimeOfLastDlMsgRcv = {Data.q_MslInAir[i].q_TimeOfLastDlMsgRcv}");
          if (Data.q_MslInAir[i].q_TimeOfLastDlMsgRcv < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
          if (Data.q_MslInAir[i].q_TimeOfLastDlMsgRcv > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_TimeFromMLL = {Data.q_MslInAir[i].q_TimeFromMLL}");
          if (Data.q_MslInAir[i].q_TimeFromMLL < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_TimeFromMLL > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
          pos += 4;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_servoPressure = {Data.q_MslInAir[i].q_servoPressure}");
          if (Data.q_MslInAir[i].q_servoPressure < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_servoPressure > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslPosition_N_L0 = {Data.q_MslInAir[i].q_mslPosition_N_L0}");
          if (Data.q_MslInAir[i]._q_mslPosition_N_L0 < -20000) writter.AppendLine("!!      Value is less than -20000");
          if (Data.q_MslInAir[i]._q_mslPosition_N_L0 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslPosition_E_L0 = {Data.q_MslInAir[i].q_mslPosition_E_L0}");
          if (Data.q_MslInAir[i]._q_mslPosition_E_L0 < -20000) writter.AppendLine("!!      Value is less than -20000");
          if (Data.q_MslInAir[i]._q_mslPosition_E_L0 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslPosition_D_LO = {Data.q_MslInAir[i].q_mslPosition_D_LO}");
          if (Data.q_MslInAir[i]._q_mslPosition_D_LO < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i]._q_mslPosition_D_LO > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslVn_L0 = {Data.q_MslInAir[i].q_mslVn_L0}");
          if (Data.q_MslInAir[i].q_mslVn_L0 < -2000) writter.AppendLine("!!      Value is less than -2000");
          if (Data.q_MslInAir[i].q_mslVn_L0 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslVe_L0 = {Data.q_MslInAir[i].q_mslVe_L0}");
          if (Data.q_MslInAir[i].q_mslVe_L0 < -2000) writter.AppendLine("!!      Value is less than -2000");
          if (Data.q_MslInAir[i].q_mslVe_L0 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslVd_L0 = {Data.q_MslInAir[i].q_mslVd_L0}");
          if (Data.q_MslInAir[i].q_mslVd_L0 < -2000) writter.AppendLine("!!      Value is less than -2000");
          if (Data.q_MslInAir[i].q_mslVd_L0 > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslNavPos_X_Uncertainty = {Data.q_MslInAir[i].q_MslNavPos_X_Uncertainty}");
          if (Data.q_MslInAir[i].q_MslNavPos_X_Uncertainty < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslNavPos_X_Uncertainty > 1000) writter.AppendLine("!!      Value is greather than 1000");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslNavPos_Y_Uncertainty = {Data.q_MslInAir[i].q_MslNavPos_Y_Uncertainty}");
          if (Data.q_MslInAir[i].q_MslNavPos_Y_Uncertainty < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslNavPos_Y_Uncertainty > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslNavPos_Z_Uncertainty = {Data.q_MslInAir[i].q_MslNavPos_Z_Uncertainty}");
          if (Data.q_MslInAir[i].q_MslNavPos_Z_Uncertainty < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslNavPos_Z_Uncertainty > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslNavVel_X_Uncertainty = {Data.q_MslInAir[i].q_MslNavVel_X_Uncertainty}");
          if (Data.q_MslInAir[i].q_MslNavVel_X_Uncertainty < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslNavVel_X_Uncertainty > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslNavVel_Y_Uncertainty = {Data.q_MslInAir[i].q_MslNavVel_Y_Uncertainty}");
          if (Data.q_MslInAir[i].q_MslNavVel_Y_Uncertainty < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslNavVel_Y_Uncertainty > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_MslNavVel_Z_Uncertainty = {Data.q_MslInAir[i].q_MslNavVel_Z_Uncertainty}");
          if (Data.q_MslInAir[i].q_MslNavVel_Z_Uncertainty < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_MslNavVel_Z_Uncertainty > 65535) writter.AppendLine("!!      Value is greather than 65535");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_TimeToGo = {Data.q_MslInAir[i].q_TimeToGo}");
          if (Data.q_MslInAir[i].q_TimeToGo < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_TimeToGo > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_guidanceMode = {Data.q_MslInAir[i].q_guidanceMode}");
          if (Data.q_MslInAir[i].q_guidanceMode < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_guidanceMode > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_navPerformance = {Data.q_MslInAir[i].q_navPerformance}");
          if (Data.q_MslInAir[i].q_navPerformance < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_navPerformance > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_netState = {Data.q_MslInAir[i].q_netState}");
          if (Data.q_MslInAir[i].q_netState < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_netState > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_seekerState = {Data.q_MslInAir[i].q_seekerState}");
          if (Data.q_MslInAir[i].q_seekerState < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_seekerState > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_skrGimbal_Az = {Data.q_MslInAir[i].q_skrGimbal_Az}");
          if (Data.q_MslInAir[i].q_skrGimbal_Az < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_skrGimbal_Az > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_skrGimbal_El = {Data.q_MslInAir[i].q_skrGimbal_El}");
          if (Data.q_MslInAir[i].q_skrGimbal_El < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_skrGimbal_El > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_skrRange = {Data.q_MslInAir[i].q_skrRange}");
          if (Data.q_MslInAir[i].q_skrRange < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_skrRange > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_skrRangeDot = {Data.q_MslInAir[i].q_skrRangeDot}");
          if (Data.q_MslInAir[i].q_skrRangeDot < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_skrRangeDot > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_skrSnr = {Data.q_MslInAir[i].q_skrSnr}");
          if (Data.q_MslInAir[i].q_skrSnr < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_skrSnr > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_skrEcm = {Data.q_MslInAir[i].q_skrEcm}");
          if (Data.q_MslInAir[i].q_skrEcm < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_skrEcm > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_lamdaDotY = {Data.q_MslInAir[i].q_lamdaDotY}");
          if (Data.q_MslInAir[i].q_lamdaDotY < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_lamdaDotY > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_lamdaDotZ = {Data.q_MslInAir[i].q_lamdaDotZ}");
          if (Data.q_MslInAir[i].q_lamdaDotZ < -32768) writter.AppendLine("!!      Value is less than -32768");
          if (Data.q_MslInAir[i].q_lamdaDotZ > 32767) writter.AppendLine("!!      Value is greather than 32767");
          pos += 2;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mmcBit = {Data.q_MslInAir[i].q_mmcBit}");
          if (Data.q_MslInAir[i].q_mmcBit < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_mmcBit > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
          writter.AppendLine($"{pos:X4}: Data.q_MslInAir[{i}].q_mslStatus = {Data.q_MslInAir[i].q_mslStatus}");
          if (Data.q_MslInAir[i].q_mslStatus < 0) writter.AppendLine("!!      Value is less than 0");
          if (Data.q_MslInAir[i].q_mslStatus > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
        }
        return writter.ToString();
      }
    }

    public class COP_C2_Msl_Metry : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - q_MslID")]
      [Description("Range: 0 ... 255\u000D\u000AAs defined by station number")]
      public byte q_MslID { get; set; }

      [DisplayName("(03) - q_MissleDataBuffer")]
      [Description("Range: 0 ... 255\u000D\u000AMetry data - Each byte is\u000D\u000A unsigned  numeric data\u000D\u000A 0 - 255 ")]
      public byte[] q_MissleDataBuffer { get; } = new byte[ARRAY_SIZE_1000];

      public COP_C2_Msl_Metry()
      {
        CCU_COP_Header.msg_type = 135;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(q_MslID);
        for (var i = 0; i < q_MissleDataBuffer.Length; i++)
        {
          writter.Write(q_MissleDataBuffer[i]);
        }
      }

      public COP_C2_Msl_Metry Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        q_MslID = reader.ReadByte();
        for (var i = 0; i < q_MissleDataBuffer.Length; i++)
        {
          q_MissleDataBuffer[i] = reader.ReadByte();
        }
        return this;
      }

      public override string ToString()
      {
        return "COP_C2_Msl_Metry";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_Msl_Metry ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: q_MslID = {q_MslID}");
        if (q_MslID < 0) writter.AppendLine("!!      Value is less than 0");
        if (q_MslID > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        for (var i = 0; i < q_MissleDataBuffer.Length; i++)
        {
          writter.AppendLine($"{pos:X4}: q_MissleDataBuffer[{i}] = {q_MissleDataBuffer[i]}");
          if (q_MissleDataBuffer[i] < 0) writter.AppendLine("!!      Value is less than 0");
          if (q_MissleDataBuffer[i] > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
        }
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_Msl_Metry ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: q_MslID = {q_MslID}");
        if (q_MslID < 0) writter.AppendLine("!!      Value is less than 0");
        if (q_MslID > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        for (var i = 0; i < q_MissleDataBuffer.Length; i++)
        {
          writter.AppendLine($"{pos:X4}: q_MissleDataBuffer[{i}] = {q_MissleDataBuffer[i]}");
          if (q_MissleDataBuffer[i] < 0) writter.AppendLine("!!      Value is less than 0");
          if (q_MissleDataBuffer[i] > 255) writter.AppendLine("!!      Value is greather than 255");
          pos += 1;
        }
        return writter.ToString();
      }
    }

    public class COP_C2_Toplite_Track : RidaMessage {

      [DisplayName("(01) - CCU_COP_Header")]
      public CCU_COP_Header CCU_COP_Header { get; } = new CCU_COP_Header();

      [DisplayName("(02) - Toplite_Track_Data")]
      public Toplite_Track_Data Toplite_Track_Data { get; } = new Toplite_Track_Data();

      public COP_C2_Toplite_Track()
      {
        CCU_COP_Header.msg_type = 136;
      }

      public override void Write(BinaryWriter writter)
      {
        writter.Write(CCU_COP_Header.msg_type);
        writter.Write(CCU_COP_Header.msg_length);
        writter.Write(CCU_COP_Header.msg_number);
        writter.Write(CCU_COP_Header.sender);
        writter.Write((byte)CCU_COP_Header.sim_flag);
        writter.Write(CCU_COP_Header.msg_sent_time);
        writter.Write(CCU_COP_Header.msg_checksum);
        writter.Write(Toplite_Track_Data.TargetUpdateTime);
        writter.Write(Toplite_Track_Data.TargetPositionNorth);
        writter.Write(Toplite_Track_Data.TargetPositionEast);
        writter.Write(Toplite_Track_Data.TargetPositionDown);
        writter.Write(Toplite_Track_Data.TargetVelocityNorth);
        writter.Write(Toplite_Track_Data.TargetVelocityEast);
        writter.Write(Toplite_Track_Data.TargetVelocityDown);
        writter.Write(Toplite_Track_Data.AutonomousTargetID);
        writter.Write(Toplite_Track_Data.AutonomousEngagementID);
        writter.Write((byte)Toplite_Track_Data.TopliteTargetType);
        {
          byte help = 0;
          help |= (byte)(((byte)Toplite_Track_Data.Validity & 1) << 0);
          help |= (byte)(((byte)Toplite_Track_Data.spare & 127) << 1);
          writter.Write(help);
        }
      }

      public COP_C2_Toplite_Track Read(BinaryReader reader)
      {
        CCU_COP_Header.msg_type = reader.ReadByte();
        CCU_COP_Header.msg_length = reader.ReadUInt16();
        CCU_COP_Header.msg_number = reader.ReadUInt16();
        CCU_COP_Header.sender = reader.ReadUInt16();
        CCU_COP_Header.sim_flag = (header_sim_flag) reader.ReadByte();
        CCU_COP_Header.msg_sent_time = reader.ReadDouble();
        CCU_COP_Header.msg_checksum = reader.ReadUInt16();
        Toplite_Track_Data.TargetUpdateTime = reader.ReadUInt32();
        Toplite_Track_Data.TargetPositionNorth = reader.ReadInt32();
        Toplite_Track_Data.TargetPositionEast = reader.ReadInt32();
        Toplite_Track_Data.TargetPositionDown = reader.ReadInt32();
        Toplite_Track_Data.TargetVelocityNorth = reader.ReadInt32();
        Toplite_Track_Data.TargetVelocityEast = reader.ReadInt32();
        Toplite_Track_Data.TargetVelocityDown = reader.ReadInt32();
        Toplite_Track_Data.AutonomousTargetID = reader.ReadUInt16();
        Toplite_Track_Data.AutonomousEngagementID = reader.ReadUInt16();
        Toplite_Track_Data.TopliteTargetType = (TopliteTargetType) reader.ReadByte();
        {
          byte help = reader.ReadByte();
          Toplite_Track_Data.Validity = (No_Yes_Boolean)((help >> 0) & 1);
          Toplite_Track_Data.spare = (byte)((help >> 1) & 127);
        }
        return this;
      }

      public override string ToString()
      {
        return "COP_C2_Toplite_Track";
      }

      public override string ToTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_Toplite_Track ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetUpdateTime = {Toplite_Track_Data.TargetUpdateTime}");
        if (Toplite_Track_Data.TargetUpdateTime < 0) writter.AppendLine("!!      Value is less than 0");
        if (Toplite_Track_Data.TargetUpdateTime > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetPositionNorth = {Toplite_Track_Data.TargetPositionNorth}");
        if (Toplite_Track_Data.TargetPositionNorth < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Toplite_Track_Data.TargetPositionNorth > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetPositionEast = {Toplite_Track_Data.TargetPositionEast}");
        if (Toplite_Track_Data.TargetPositionEast < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Toplite_Track_Data.TargetPositionEast > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetPositionDown = {Toplite_Track_Data.TargetPositionDown}");
        if (Toplite_Track_Data.TargetPositionDown < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Toplite_Track_Data.TargetPositionDown > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetVelocityNorth = {Toplite_Track_Data.TargetVelocityNorth}");
        if (Toplite_Track_Data.TargetVelocityNorth < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Toplite_Track_Data.TargetVelocityNorth > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetVelocityEast = {Toplite_Track_Data.TargetVelocityEast}");
        if (Toplite_Track_Data.TargetVelocityEast < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Toplite_Track_Data.TargetVelocityEast > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetVelocityDown = {Toplite_Track_Data.TargetVelocityDown}");
        if (Toplite_Track_Data.TargetVelocityDown < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Toplite_Track_Data.TargetVelocityDown > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.AutonomousTargetID = {Toplite_Track_Data.AutonomousTargetID}");
        if (Toplite_Track_Data.AutonomousTargetID < 0) writter.AppendLine("!!      Value is less than 0");
        if (Toplite_Track_Data.AutonomousTargetID > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.AutonomousEngagementID = {Toplite_Track_Data.AutonomousEngagementID}");
        if (Toplite_Track_Data.AutonomousEngagementID < 0) writter.AppendLine("!!      Value is less than 0");
        if (Toplite_Track_Data.AutonomousEngagementID > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TopliteTargetType = {Toplite_Track_Data.TopliteTargetType}");
        if (!Enum.GetValues(typeof(TopliteTargetType)).OfType<TopliteTargetType>().Contains(Toplite_Track_Data.TopliteTargetType)) writter.AppendLine("!!      Value is not valid for enum TopliteTargetType");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.Validity = {Toplite_Track_Data.Validity}");
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.spare = {Toplite_Track_Data.spare}");
        pos += 1;
        return writter.ToString();
      }

      public override string ToJsonTextMessage()
      {
        var writter = new StringBuilder();
        var pos = 0;
        writter.AppendLine("=== COP_C2_Toplite_Track ===");
        writter.AppendLine("");
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_type = {CCU_COP_Header.msg_type}");
        if (CCU_COP_Header.msg_type < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_type > 255) writter.AppendLine("!!      Value is greather than 255");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_length = {CCU_COP_Header.msg_length}");
        if (CCU_COP_Header.msg_length < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_length > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_number = {CCU_COP_Header.msg_number}");
        if (CCU_COP_Header.msg_number < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_number > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sender = {CCU_COP_Header.sender}");
        if (CCU_COP_Header.sender < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.sender > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.sim_flag = {CCU_COP_Header.sim_flag}");
        if (!Enum.GetValues(typeof(header_sim_flag)).OfType<header_sim_flag>().Contains(CCU_COP_Header.sim_flag)) writter.AppendLine("!!      Value is not valid for enum header_sim_flag");
        pos += 1;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_sent_time = {CCU_COP_Header.msg_sent_time}");
        if (CCU_COP_Header.msg_sent_time < 946684800) writter.AppendLine("!!      Value is less than 946684800");
        if (CCU_COP_Header.msg_sent_time > 4102444800) writter.AppendLine("!!      Value is greather than 4102444800");
        pos += 8;
        writter.AppendLine($"{pos:X4}: CCU_COP_Header.msg_checksum = {CCU_COP_Header.msg_checksum}");
        if (CCU_COP_Header.msg_checksum < 0) writter.AppendLine("!!      Value is less than 0");
        if (CCU_COP_Header.msg_checksum > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetUpdateTime = {Toplite_Track_Data.TargetUpdateTime}");
        if (Toplite_Track_Data.TargetUpdateTime < 0) writter.AppendLine("!!      Value is less than 0");
        if (Toplite_Track_Data.TargetUpdateTime > 4294967295) writter.AppendLine("!!      Value is greather than 4294967295");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetPositionNorth = {Toplite_Track_Data.TargetPositionNorth}");
        if (Toplite_Track_Data.TargetPositionNorth < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Toplite_Track_Data.TargetPositionNorth > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetPositionEast = {Toplite_Track_Data.TargetPositionEast}");
        if (Toplite_Track_Data.TargetPositionEast < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Toplite_Track_Data.TargetPositionEast > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetPositionDown = {Toplite_Track_Data.TargetPositionDown}");
        if (Toplite_Track_Data.TargetPositionDown < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Toplite_Track_Data.TargetPositionDown > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetVelocityNorth = {Toplite_Track_Data.TargetVelocityNorth}");
        if (Toplite_Track_Data.TargetVelocityNorth < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Toplite_Track_Data.TargetVelocityNorth > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetVelocityEast = {Toplite_Track_Data.TargetVelocityEast}");
        if (Toplite_Track_Data.TargetVelocityEast < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Toplite_Track_Data.TargetVelocityEast > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TargetVelocityDown = {Toplite_Track_Data.TargetVelocityDown}");
        if (Toplite_Track_Data.TargetVelocityDown < -2147483648) writter.AppendLine("!!      Value is less than -2147483648");
        if (Toplite_Track_Data.TargetVelocityDown > 2147483647) writter.AppendLine("!!      Value is greather than 2147483647");
        pos += 4;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.AutonomousTargetID = {Toplite_Track_Data.AutonomousTargetID}");
        if (Toplite_Track_Data.AutonomousTargetID < 0) writter.AppendLine("!!      Value is less than 0");
        if (Toplite_Track_Data.AutonomousTargetID > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.AutonomousEngagementID = {Toplite_Track_Data.AutonomousEngagementID}");
        if (Toplite_Track_Data.AutonomousEngagementID < 0) writter.AppendLine("!!      Value is less than 0");
        if (Toplite_Track_Data.AutonomousEngagementID > 65535) writter.AppendLine("!!      Value is greather than 65535");
        pos += 2;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.TopliteTargetType = {Toplite_Track_Data.TopliteTargetType}");
        if (!Enum.GetValues(typeof(TopliteTargetType)).OfType<TopliteTargetType>().Contains(Toplite_Track_Data.TopliteTargetType)) writter.AppendLine("!!      Value is not valid for enum TopliteTargetType");
        pos += 1;
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.Validity = {Toplite_Track_Data.Validity}");
        writter.AppendLine($"{pos:X4}: Toplite_Track_Data.spare = {Toplite_Track_Data.spare}");
        pos += 1;
        return writter.ToString();
      }
    }
  }
}
